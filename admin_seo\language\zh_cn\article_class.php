<?php
defined('InShopNC') or exit('Access Invalid!');
/**
 * index
 */
$lang['article_class_index_del_succ']	= '删除分类成功。';
$lang['article_class_index_choose']		= '请选择要删除的内容!';
$lang['article_class_index_class']		= '文章分类';
$lang['article_class_index_class_subhead']= '网站文章分类添加与管理';
$lang['article_class_index_name']		= '分类名称';
$lang['article_class_index_ensure_del']	= '删除该分类将会同时删除该分类的所有下级分类，您确定要删除吗';
$lang['article_class_index_help1']		= '管理员新增文章时，可选择文章分类。文章分类将在前台文章列表页显示';
$lang['article_class_index_help2']		= '默认的文章分类不可以删除';
/**
 * 新增分类
 */
$lang['article_class_add_sup_class']	= '上级分类';
$lang['article_class_add_sup_class_notice']	= '如果选择上级分类，那么新增的分类则为被选择上级分类的子分类';
$lang['article_class_add_sup_class_notice2']	= '如果选择上级分类，那么编辑的分类则为被选择上级分类的子分类';
$lang['article_class_add_update_sort']	= '更新排序';
$lang['article_class_add_name_null']	= '分类名称不能为空';
$lang['article_class_add_name_exists']	= '该分类名称已经存在了，请您换一个';
$lang['article_class_add_sort_int']		= '分类排序仅能为数字';
$lang['article_class_add_back_to_list']	= '返回分类列表';
$lang['article_class_add_class']		= '继续新增分类';
$lang['article_class_add_succ']			= '新增分类成功';
$lang['article_class_add_fail']			= '新增分类失败';
/**
 * 编辑分类
 */
$lang['article_class_edit_again']		= '重新编辑该分类';
$lang['article_class_edit_succ']		= '编辑分类成功';
$lang['article_class_edit_fail']		= '编辑分类失败';
/**
 * 删除分类
 */
$lang['article_class_del_']	= '';
$lang['article_class_del_']	= '';
$lang['article_class_del_']	= '';
$lang['article_class_del_']	= '';
$lang['article_class_del_']	= '';
$lang['article_class_del_']	= '';
$lang['article_class_del_']	= '';
$lang['article_class_del_']	= '';
$lang['article_class_del_']	= '';