<?php defined('InShopNC') or exit('Access Invalid!');?>

<form method="post" name="xianshi-check-from" id="xianshi-check-from" action="<?php echo urlAdminShop('promotion_xianshi', 'check');?>">
  <input type="hidden" name="form_submit" value="ok" />
  <input type="hidden" value="<?php echo $_GET['xianshi_id']; ?>" name="xianshi_id">
  <input type="hidden" value="<?php echo $_GET['pass']; ?>" name="pass">
  <div class="ncap-form-default">
    <dl class="row">
        <textarea rows="12" class="tarea" cols="60" name="check_reason"
                  placeholder="请输入..." style="width: 100%;"></textarea>
    </dl>
    <div class="bot">
        <a href="javascript:void(0);" class="ncap-btn-big ncap-btn" nctype="btn_cancel" style="margin-right: 10px;">取消</a>
        <a href="javascript:void(0);" class="ncap-btn-big ncap-btn-<?php echo $_GET['pass']  ? 'green' : 'red';?>" nctype="btn_submit">
            <?php echo $_GET['pass'] ? '审核通过' : '审核不通过';?>
        </a>
    </div>
  </div>
</form>
<script>
$(function(){
    $('a[nctype="btn_submit"]').click(function(){
        ajaxpost('xianshi-check-from', '', '', 'onerror');
    });

    $('a[nctype="btn_cancel"]').click(function(){
        $('#fwin_xianshi-check .dialog_close_button').click();
    });
});
</script>

<style>
    #fwin_xianshi-check .dialog_title {
        text-align: center;
        display: block;
    }
</style>