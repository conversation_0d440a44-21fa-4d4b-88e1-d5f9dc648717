<?php
/**
 * 手机端设置
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');
class mb_settingControl extends SystemControl{
    public function __construct(){
        parent::__construct();
    }

    public function indexOp() {
        $this->settingOp();
    }

    /**
     * 基本设置
     */
    public function settingOp(){
        $model_setting = Model('setting');
        if (chksubmit()){
            $update_array = array();
            $update_array['signin_isuse'] = intval($_POST['signin_isuse'])==1?1:0;
            $update_array['points_signin'] = intval($_POST['points_signin'])?$_POST['points_signin']:0;
            $update_array['xcx_version'] = floatval($_POST['xcx_version'])?$_POST['xcx_version']:0;
            $update_array['xcx_asq_version'] = floatval($_POST['xcx_asq_version'])?$_POST['xcx_asq_version']:0;
            $update_array['xcx_mall_version'] = floatval($_POST['xcx_mall_version'])?$_POST['xcx_mall_version']:0;
            $update_array['xcx_mall_special_id'] = intval($_POST['xcx_mall_special_id'])?$_POST['xcx_mall_special_id']:0;
            $update_array['xcx_mall_nav_img'] = trim($_POST['xcx_mall_nav_img'])?$_POST['xcx_mall_nav_img']:'';
            $update_array['xcx_mall_original_id'] = trim($_POST['xcx_mall_original_id'])?$_POST['xcx_mall_original_id']:'';
            $result = $model_setting->updateSetting($update_array);
            if ($result === true){
                $this->log('编辑手机端设置',1);
                showDialog(L('nc_common_save_succ'));
            } else {
                showDialog(L('nc_common_save_fail'));
            }
        }
        $list_setting = $model_setting->getListSetting();
        Tpl::output('list_setting',$list_setting);
        Tpl::showpage('mb_setting');
    }
}
