<?php defined('InShopNC') or exit('Access Invalid!');?>

  <form id="admin_form" method="post" action='<?php echo urlAdminShop('order', 'refund_freight');?>&type=<?php echo $output['type'];?>&order_id=<?php echo $output['order']['order_id']; ?>&goods_id=<?php echo $output['goods']['rec_id']; ?>' name="adminForm">
    <input type="hidden" name="form_submit" value="ok" />
    <div class="ncap-form-default">
      <dl class="row">
        <dt class="tit">
          退款原因：
        </dt>
          <dd class="opt">
              <select class="w150" name="reason_id">
                  <?php if (is_array($output['reason_list']) && !empty($output['reason_list'])) { ?>
                      <?php foreach ($output['reason_list'] as $key => $val) { ?>
                          <option value="<?php echo $val['reason_id'];?>"><?php echo $val['reason_info'];?></option>
                      <?php } ?>
                  <?php } ?>
                  <option value="0" selected>其他</option>
              </select>
              <span class="err"></span>
              <p class="notic"></p>
          </dd>
      </dl>
      <dl class="row">
        <dt class="tit">
          退款金额：
        </dt>
        <dd class="opt">
          <input type="text" class="text w50" name="refund_amount" value="<?php echo $output['allow_refund_amount']; ?>" /> 元
          （最多 <strong class="green" title="可退金额由系统根据订单运费实际支付和已退款金额自动计算得出。"><?php echo $output['allow_refund_amount']; ?></strong> 元）
          <span class="err"></span>
          <p class="notic">退款金额不能超过可退金额。</p>
        </dd>
      </dl>
      <dl class="row">
        <dt class="tit">
          退款说明：
        </dt>
        <dd class="opt">
          <textarea name="buyer_message" rows="3" class="textarea w300">平台管理员替买家退运费</textarea>
          <span class="err"></span>
          <p class="notic"></p>
        </dd>
      </dl>
        <input type="hidden" class="text w50" name="type" value="<?php echo $output['type']; ?>" />
      <div class="bot"><a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="submitBtn"><?php echo $lang['nc_submit'];?></a></div>
    </div>
  </form>
<script type="text/javascript">
    $(function(){$("#submitBtn").click(function(){
        ajaxpost('admin_form', '', '', 'onerror');
    });
        $(".cb-enable").click(function(){
            var parent = $(this).parents('.onoff');
            $('.cb-disable',parent).removeClass('selected');
            $(this).addClass('selected');
            $('.checkbox',parent).attr('checked', true);
        });
        $(".cb-disable").click(function(){
            var parent = $(this).parents('.onoff');
            $('.cb-enable',parent).removeClass('selected');
            $(this).addClass('selected');
            $('.checkbox',parent).attr('checked', false);
        });
    });
</script>