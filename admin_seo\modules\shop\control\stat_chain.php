<?php
/**
 * 门店管理
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;
use Upet\Queues\DeleteFileQueue;

defined('InShopNC') or exit('Access Invalid!');
class stat_chainControl extends SystemControl{

    const EXPORT_SIZE = 2000;

    private $_links = array(
        array('url'=>'act=chain&op=chainlist','text'=>'销售统计'),
    );

    public function __construct()
    {
        parent::__construct();
        if(C('chain_earnest_money') > 0){
            $this->_links[] = array('url'=>'act=chain&op=chain_unpay','text'=>'待付款门店');
        }
    }

    /**
    * 默认操作列出门店
    */
    public function indexOp(){
        $this->chainlistOp();
    }

    /**
     * 门店列表
     */
    public function chainlistOp(){
        $model_chain = Model('chain');

        $chain_brand_list =  $model_chain->getChainBrandList(['status'=>1]);
        Tpl::output('chain_brand_list',$chain_brand_list);
        Tpl::output('region_list',Model('region')->getRegionList());
        Tpl::output('top_link',$this->sublink($this->_links,'chainlist'));
        Tpl::showpage('stat_chain.index');
    }

    /***
     * 门店申请列表
     */
    public function chain_joininOp(){
        Tpl::output('top_link',$this->sublink($this->_links,'chain_joinin'));
        Tpl::showpage('chain_joinin');
    }

    public function chain_unpayOp(){
        Tpl::output('top_link',$this->sublink($this->_links,'chain_unpay'));
        Tpl::showpage('chain_unpay');
    }

    /**
     * 门店运营设置
     */
    public function chain_settingOp(){
        $model_setting = Model('setting');
        if(chksubmit()){
            $update_array = array();
            $update_array['chain_allow'] = $_POST['chain_allow'];
            $update_array['chain_check_allow'] = $_POST['chain_check_allow'];
            $update_array['chain_earnest_money'] = intval($_POST['chain_earnest_money']);
            $update_array['chain_hot_keyword'] = trim($_POST['chain_hot_keyword']);
            $result = $model_setting->updateSetting($update_array);
            if ($result === true){
                $this->log('编辑门店设置',1);
                showMessage(L('nc_common_save_succ'));
            }else {
                showMessage(L('nc_common_save_fail'));
            }
        }
        $list_setting = $model_setting->getListSetting();
        Tpl::output('top_link',$this->sublink($this->_links,'chain_setting'));
        Tpl::output('list_setting',$list_setting);
        Tpl::showpage('chain.setting');
    }

    /**
     * 门店入驻详细信息&审核
     */
    public function chain_joinin_detailOp(){
        $chain_id = intval($_REQUEST['chain_id']);
        if($chain_id <= 0){
            showMessage('参数错误');
        }

        $model_chain = Model('chain');
        $condition = array('chain_id'=>$chain_id);
        $chain_info = $model_chain->getChainInfo($condition, '*');
       
        if(chksubmit()){  
            $param = array();
            $param['chain_check_info'] = trim($_POST['chain_check_info']);
            //审核结果处理
            switch(trim($_POST['verify_type'])){
                case 'pass':                
                    if(intval($chain_info['chain_state']) == 2){
                        $param['chain_state'] = 4;
                        if(floatval(C('chain_earnest_money')) == 0){
                            $param['chain_state'] = 1;
                        }
                    }else if(intval($chain_info['chain_state']) == 5){
                        $param['chain_state'] = 1;
                    }
                    break;
                case 'fail':
                    $param['chain_state'] = 3;
                    break;
            }           
        }
        //店铺信息
        $model_store = Model('store');
        $store_info = $model_store->getStoreInfoByID($chain_info['store_id']);
        //店铺等级
        $model_grade = Model('store_grade');
        $grade_info = $model_grade->getOneGrade($store_info['grade_id']);
        Tpl::output('grade_info',$grade_info);
        Tpl::output('store_info',$store_info);

        unset($chain_info['chain_pwd']);
        Tpl::output('chain_info',$chain_info);
        Tpl::showpage('chain_joinin_detail');
    }

    /**
     * 门店编辑
     */
    public function chain_editOp(){
        $chain_id = intval($_REQUEST['chain_id']);
        if($chain_id <= 0){
            showMessage('参数错误');
        }

        $model_chain = Model('chain');
        $condition = array('chain_id'=>$chain_id);
        $chain_info = $model_chain->getChainInfo($condition, '*');
        if(chksubmit()){
            /**
             * 上传图片
             */
            $upload = new UploadFile();
            if (!empty($_FILES['chain_img']['name'])){
                $upload->set('default_dir', ATTACH_CHAIN.DS.$chain_info['store_id']);
                $upload->set('thumb_ext',   '');
                $upload->set('file_name','');
                $upload->set('ifremove',false);
                $result = $upload->upfile('chain_img',true);
                if ($result){
                    $_POST['chain_img'] = $upload->file_name;
                }else {
                    showDialog($upload->error);
                }
            }

            $upload = new UploadFile();
            if (!empty($_FILES['chain_banner']['name'])){
                $upload->set('default_dir', ATTACH_CHAIN.DS.$chain_info['store_id']);
                $upload->set('thumb_ext',   '');
                $upload->set('file_name','');
                $upload->set('ifremove',false);
                $result = $upload->upfile('chain_banner',true);
                if ($result){
                    $_POST['chain_banner'] = $upload->file_name;
                }else {
                    showDialog($upload->error);
                }
            }

            $upload = new UploadFile();
            if (!empty($_FILES['chain_logo']['name'])){
                $upload->set('default_dir', ATTACH_CHAIN.DS.$chain_info['store_id']);
                $upload->set('thumb_ext',   '');
                $upload->set('file_name','');
                $upload->set('ifremove',false);
                $result = $upload->upfile('chain_logo',true);
                if ($result){
                    $_POST['chain_logo'] = $upload->file_name;
                }else {
                    showDialog($upload->error);
                }
            }

            //删除旧图片
            $paths = [];
            if (!empty($_POST['chain_img']) && !empty($chain_info['chain_img'])){
                $paths[] = ATTACH_CHAIN.DS.$chain_info['store_id'].DS.$chain_info['chain_img'];
            }
            if (!empty($_POST['chain_banner']) && !empty($chain_info['chain_banner'])){
                $paths[] = ATTACH_CHAIN.DS.$chain_info['store_id'].DS.$chain_info['chain_banner'];
            }
            if (!empty($_POST['chain_logo']) && !empty($chain_info['chain_logo'])){
                $paths[] = ATTACH_CHAIN.DS.$chain_info['store_id'].DS.$chain_info['chain_logo'];
            }

            $update = array();
            if (!empty($_POST['chain_img'])) {
                $update['chain_img']    = $_POST['chain_img'];
            }
            if (!empty($_POST['chain_banner'])) {
                $update['chain_banner']    = $_POST['chain_banner'];
            }
            if (!empty($_POST['chain_logo'])) {
                $update['chain_logo']    = $_POST['chain_logo'];
            }
            $update['chain_name']    = trim($_POST['chain_name']);
            $update['area_id_1']    = $_POST['area_id_1'];
            $update['area_id_2']    = $_POST['area_id_2'];
            $update['area_id_3']    = $_POST['area_id_3'];
            $update['area_id_4']    = $_POST['area_id_4'];
            $update['area_id']      = $_POST['area_id'];
            $update['area_info']    = $_POST['area_info'];
            $update['chain_address']= $_POST['chain_address'];
            $update['chain_phone']  = $_POST['chain_phone'];
            $update['chain_opening_hours']  = $_POST['chain_opening_hours'];
            $update['chain_traffic_line']   = $_POST['chain_traffic_line'];
            $update['chain_cycle'] = intval($_POST['chain_cycle']);
            $update['chain_state'] = intval($_POST['chain_state']);
            $update['chain_close_info'] = $_POST['chain_close_info'];


            //处理门店地址坐标信息
            if($chain_info['chain_lat'] == 0 || $chain_info['area_info'] != $update['area_info'] || $chain_info['chain_address'] != $update['chain_address']){
                $area_info = explode(" ", $update['area_info']);
                $city = $area_info[1];
                $area = $area_info[1] . $area_info[2];
                $address = $area . $update['chain_address'];
                $location = getGeoByAddress($address, $city);
                if(!empty($location)){
                    $update['chain_lat'] = $location['location']['lat'];
                    $update['chain_lng'] = $location['location']['lng'];
                }
            }
            $result = $model_chain->editChain($update, array('chain_id' => $chain_id, 'store_id' => $chain_info['store_id']));
            if ($result) {
                if($paths){
                    DeleteFileQueue::dispatch($paths);
                }
                showMessage('编辑成功','index.php?act=chain&op=index','succ');
            } else {
                showMessage('编辑失败', 'reload');
            }
        }
        $this->_getChianTransportAreas($chain_info);
        $chain_info['transport_areas'] = $chain_info['transport_areas'] !='' ? unserialize($chain_info['transport_areas']) : array();
        unset($chain_info['chain_pwd']);
        Tpl::output('chain_info',$chain_info);
        Tpl::showpage('chain.edit');
    }

    /**
     * 读取门店XML数据[管理]
     */
    public function get_xml_chain_listOp(){
        $chain_model = Model('chain');
        // 设置页码参数名称
        $condition = array();
        $condition['chain_state'] = array('in',array('0','1','3'));
        if ($_GET['chain_name'] != '') {
            $condition['chain_name'] = array('like', '%' . $_GET['chain_name'] . '%');
        }
        if (!empty($_GET['chain_brand_id'])) {
            $condition['chain_brand_id'] = $_GET['chain_brand_id'];
        }
        if (!empty($_GET['region_id'])) {
            $condition['region_id'] = $_GET['region_id'];
        }
        $order = '';
        $param = array('chain_id','chain_name','chain_user','chain_state','is_transport','is_forward_order','is_self_take','is_collection','chain_time','chain_close_time');
        if (in_array($_POST['sortname'], $param) && in_array($_POST['sortorder'], array('asc', 'desc'))) {
            $order = $_POST['sortname'] . ' ' . $_POST['sortorder'];
        }
        $condition['store_id'] = $_SESSION['store_id'];
        $page = $_POST['rp'];

        $fields = "chain_id,store_id,chain_user,chain_name,chain_img,area_info,chain_address,chain_state,chain_logo,is_transport,is_forward_order,is_self_take,is_collection,chain_time,chain_close_time,store_name,is_own,chain_erp_status";

        //门店列表
        $chain_list =$chain_model->getChainList($condition, $fields, $page, $order);

        $data = array();
        $data['now_page'] = $chain_model->shownowpage();
        $data['total_num'] = $chain_model->gettotalnum();
        $chain_id_arr = [];
        foreach ($chain_list as $val){
            $chain_id_arr[] = $val['chain_id'];
        }
        $condition = [];
        $condition['chain_id'] = ['in',implode(',',$chain_id_arr)];
        $condition['order_state'] = ['egt',ORDER_STATE_PAY];
        if (trim($_GET['query_start_date']) && trim($_GET['query_end_date'])) {
            $sdate = strtotime($_GET['query_start_date']);
            $edate = strtotime($_GET['query_end_date']) + 86400 -1;
            $condition['payment_time'] = array('between', array($sdate,$edate));

        } elseif (trim($_GET['query_start_date'])) {
            $sdate = strtotime($_GET['query_start_date']);
            $condition['payment_time'] = array('egt', $sdate);

        } elseif (trim($_GET['query_end_date'])) {
            $edate = strtotime($_GET['query_end_date']) + 86400 -1;
            $condition['payment_time'] = array('elt', $edate);
        }else{
            /*$sdate = strtotime(date('Y-m-01',strtotime(date("Y-m-d"))));
            $edate = time();
            $condition['payment_time'] = array('between', array($sdate,$edate));*/
        }
        $order_user_num = Model()->table('orders')->field('chain_id,count(order_id) as order_num , sum(order_amount) as order_amount_num,count(distinct(buyer_id)) as order_buyer_num')->where($condition)->group('chain_id')->limit(false)->key('chain_id')->select();

        $vr_order_usr_num = Model()->table('vr_order')->field('chain_id,count(order_id) as order_num , sum(order_amount) as order_amount_num,count(distinct(buyer_id)) as order_buyer_num')->where($condition)->group('chain_id')->limit(false)->key('chain_id')->select();

        foreach ($chain_list as $value) {
            $param = array();
            $chain_state = $this->_getChainState($value['chain_state']);
/*            $operation = "<a class='btn green' href='index.php?act=chain&op=chain_joinin_detail&chain_id=".$value['chain_id']."'><i class='fa fa-list-alt'></i>查看</a><span class='btn'><em><i class='fa fa-cog'></i>" . L('nc_set') . " <i class='arrow'></i></em><ul><li><a href='index.php?act=chain&op=chain_edit&chain_id=" . $value['chain_id'] . "'>编辑门店信息</a></li>";

            $operation = "</ul></span>";
            $param['operation'] = $operation;
*/

            $param['chain_id'] = $value['chain_id'];
            $chain_name = "<a href='". urlShop('show_chain', 'index', array('chain_id' => $value['chain_id'])) ."' target='blank'>";
            $chain_name .= $value['chain_name'] . "<i class='fa fa-external-link ' title='新窗口打开'></i></a>";
            $param['chain_name'] = $chain_name;
            $param['is_own'] = $value['is_own'] ==  '1' ? '<span class="yes"><i class="fa fa-check-circle"></i>是</span>' : '<span class="no"><i class="fa fa-ban"></i>否</span>';
            $param['chain_user'] = $value['chain_user'];

            //实物
            $param['chain_order_user'] = $order_user_num[$value['chain_id']]['order_buyer_num']?$order_user_num[$value['chain_id']]['order_buyer_num']:0;
            $param['chain_order_num'] =  $order_user_num[$value['chain_id']]['order_num']?$order_user_num[$value['chain_id']]['order_num']:0;
            $param['chain_order_amount_num'] =  $order_user_num[$value['chain_id']]['order_amount_num']?$order_user_num[$value['chain_id']]['order_amount_num']:0;
            //虚拟
            $param['chain_vr_order_user'] = $vr_order_usr_num[$value['chain_id']]['order_buyer_num']?$vr_order_usr_num[$value['chain_id']]['order_buyer_num']:0;
            $param['chain_vr_order_num'] =  $vr_order_usr_num[$value['chain_id']]['order_num']?$vr_order_usr_num[$value['chain_id']]['order_num']:0;
            $param['chain_vr_order_amount_num'] =  $vr_order_usr_num[$value['chain_id']]['order_amount_num']?$vr_order_usr_num[$value['chain_id']]['order_amount_num']:0;

            $param['chain_state'] = $chain_state;
            $data['list'][$value['chain_id']] = $param;
        }
        echo Tpl::flexigridXML($data);exit();
    }

    /**
     * 读取门店XML数据[待审核门店]
     */
    public function get_xml_chain_check_listOp(){
        $chain_model = Model('chain');
        // 设置页码参数名称
        $condition = array();
        $condition['chain_state'] = array('in',array('2','5'));
        if ($_GET['chain_name'] != '') {
            $condition['chain_name'] = array('like', '%' . $_GET['chain_name'] . '%');
        }
        if ($_GET['chain_user'] != '') {
            $condition['chain_user'] = array('like', '%' . $_GET['chain_user'] . '%');
        }
        if ($_GET['store_name'] != '') {
            $condition['store_name'] = array('like', '%' . $_GET['store_name'] . '%');
        }
        if ($_POST['query'] != '') {
            $condition[$_POST['qtype']] = array('like', '%' . $_POST['query'] . '%');
        }
        $order = '';
        $param = array('chain_id','chain_name','chain_user','is_transport','is_forward_order','is_self_take','is_collection','chain_apply_time');
        if (in_array($_POST['sortname'], $param) && in_array($_POST['sortorder'], array('asc', 'desc'))) {
            $order = $_POST['sortname'] . ' ' . $_POST['sortorder'];
        }

        $page = $_POST['rp'];

        $fields = "chain_id,store_id,chain_user,chain_name,chain_img,area_info,chain_address,chain_state,chain_logo,is_transport,is_forward_order,is_self_take,is_collection,chain_time,chain_close_time,store_name,chain_erp_status";

        //门店列表
        $chain_list =$chain_model->getChainList($condition, $fields, $page, $order);

        $data = array();
        $data['now_page'] = $chain_model->shownowpage();
        $data['total_num'] = $chain_model->gettotalnum();

        foreach ($chain_list as $value) {
            $param = array();
            $operation = "<a class='btn orange' href='index.php?act=chain&op=chain_joinin_detail&chain_id=".$value['chain_id']."'><i class='fa fa-check-circle'></i>审核</a>";
            $param['operation'] = $operation;
            $param['chain_id'] = $value['chain_id'];
            $param['chain_name'] = $value['chain_name'];
            $param['chain_user'] = $value['chain_user'];
            $param['chain_img'] = "<a href='javascript:void(0);' class='pic-thumb-tip' onMouseOut='toolTip()' onMouseOver='toolTip(\"<img src=".getChainImage($value['chain_img'],$value['store_id'],$value['chain_erp_status']).">\")'><i class='fa fa-picture-o'></i></a>";
            $param['chain_logo'] = "<a href='javascript:void(0);' class='pic-thumb-tip' onMouseOut='toolTip()' onMouseOver='toolTip(\"<img src=".getChainImage($value['chain_logo'], $value['store_id'],$value['chain_erp_status']).">\")'><i class='fa fa-picture-o'></i></a>";
            $param['is_transport'] = $value['is_transport'] ==  '1' ? '<span class="yes"><i class="fa fa-check-circle"></i>是</span>' : '<span class="no"><i class="fa fa-ban"></i>否</span>';
            $param['is_forward_order'] = $value['is_forward_order'] ==  '1' ? '<span class="yes"><i class="fa fa-check-circle"></i>是</span>' : '<span class="no"><i class="fa fa-ban"></i>否</span>';
            $param['is_self_take'] = $value['is_self_take'] ==  '1' ? '<span class="yes"><i class="fa fa-check-circle"></i>是</span>' : '<span class="no"><i class="fa fa-ban"></i>否</span>';
            $param['is_collection'] = $value['is_collection'] ==  '1' ? '<span class="yes"><i class="fa fa-check-circle"></i>是</span>' : '<span class="no"><i class="fa fa-ban"></i>否</span>';
            $param['chain_apply_time'] = date('Y-m-d H:i:s', $value['chain_apply_time']);
            $param['area_info'] = $value['area_info'];
            $param['chain_address'] = $value['chain_address'];
            $param['store_name'] = $value['store_name'];
            $data['list'][$value['chain_id']] = $param;
        }
        echo Tpl::flexigridXML($data);exit();
    }

    /**
     * 读取门店XML数据[待付款门店]
     */
    public function get_xml_chain_unpay_listOp(){
        $chain_model = Model('chain');
        // 设置页码参数名称
        $condition = array();
        $condition['chain_state'] = 4;
        if ($_GET['chain_name'] != '') {
            $condition['chain_name'] = array('like', '%' . $_GET['chain_name'] . '%');
        }
        if ($_GET['chain_user'] != '') {
            $condition['chain_user'] = array('like', '%' . $_GET['chain_user'] . '%');
        }
        if ($_GET['store_name'] != '') {
            $condition['store_name'] = array('like', '%' . $_GET['store_name'] . '%');
        }
        if ($_POST['query'] != '') {
            $condition[$_POST['qtype']] = array('like', '%' . $_POST['query'] . '%');
        }
        $order = '';
        $param = array('chain_id','chain_name','chain_user','is_transport','is_forward_order','is_self_take','is_collection','chain_apply_time');
        if (in_array($_POST['sortname'], $param) && in_array($_POST['sortorder'], array('asc', 'desc'))) {
            $order = $_POST['sortname'] . ' ' . $_POST['sortorder'];
        }

        $page = $_POST['rp'];

        $fields = "chain_id,store_id,chain_user,chain_name,chain_img,area_info,chain_address,chain_state,chain_logo,is_transport,is_forward_order,is_self_take,is_collection,chain_time,chain_close_time,store_name,chain_erp_status";

        //门店列表
        $chain_list =$chain_model->getChainList($condition, $fields, $page, $order);

        $data = array();
        $data['now_page'] = $chain_model->shownowpage();
        $data['total_num'] = $chain_model->gettotalnum();

        foreach ($chain_list as $value) {
            $param = array();
            $operation = "<a class='btn green' href='index.php?act=chain&op=chain_joinin_detail&chain_id=".$value['chain_id']."'><i class='fa fa-list-alt'></i>查看</a>";
            $param['operation'] = $operation;
            $param['chain_id'] = $value['chain_id'];
            $param['chain_name'] = $value['chain_name'];
            $param['chain_user'] = $value['chain_user'];
            $param['chain_img'] = "<a href='javascript:void(0);' class='pic-thumb-tip' onMouseOut='toolTip()' onMouseOver='toolTip(\"<img src=".getChainImage($value['chain_img'],$value['store_id'],$value['chain_erp_status']).">\")'><i class='fa fa-picture-o'></i></a>";
            $param['chain_logo'] = "<a href='javascript:void(0);' class='pic-thumb-tip' onMouseOut='toolTip()' onMouseOver='toolTip(\"<img src=".getChainImage($value['chain_logo'], $value['store_id'],$value['chain_erp_status']).">\")'><i class='fa fa-picture-o'></i></a>";
            $param['is_transport'] = $value['is_transport'] ==  '1' ? '<span class="yes"><i class="fa fa-check-circle"></i>是</span>' : '<span class="no"><i class="fa fa-ban"></i>否</span>';
            $param['is_forward_order'] = $value['is_forward_order'] ==  '1' ? '<span class="yes"><i class="fa fa-check-circle"></i>是</span>' : '<span class="no"><i class="fa fa-ban"></i>否</span>';
            $param['is_self_take'] = $value['is_self_take'] ==  '1' ? '<span class="yes"><i class="fa fa-check-circle"></i>是</span>' : '<span class="no"><i class="fa fa-ban"></i>否</span>';
            $param['is_collection'] = $value['is_collection'] ==  '1' ? '<span class="yes"><i class="fa fa-check-circle"></i>是</span>' : '<span class="no"><i class="fa fa-ban"></i>否</span>';
            $param['chain_apply_time'] = date('Y-m-d H:i:s', $value['chain_apply_time']);
            $param['area_info'] = $value['area_info'];
            $param['chain_address'] = $value['chain_address'];
            $param['store_name'] = $value['store_name'];
            $data['list'][$value['chain_id']] = $param;
        }
        echo Tpl::flexigridXML($data);exit();
    }

    /**
     * 导出门店CSV列表
     */
    public function export_csvOp(){
        $chain_model = Model('chain');
        // 设置页码参数名称
        $condition = array();
        $condition['chain_state'] = array('in',array('0','1','3'));
        if ($_GET['chain_name'] != '') {
            $condition['chain_name'] = array('like', '%' . $_GET['chain_name'] . '%');
        }
        if ($_GET['chain_user'] != '') {
            $condition['chain_user'] = array('like', '%' . $_GET['chain_user'] . '%');
        }
        if ($_GET['store_name'] != '') {
            $condition['store_name'] = array('like', '%' . $_GET['store_name'] . '%');
        }
        if ($_GET['is_own'] != '') {
            $condition['is_own'] = $_GET['is_own'];
        }
        if ($_GET['chain_state'] != '') {
            $condition['chain_state'] = $_GET['chain_state'];
        }
        if ($_POST['query'] != '') {
            $condition[$_POST['qtype']] = array('like', '%' . $_POST['query'] . '%');
        }
        $order = '';
        $param = array('chain_id','chain_name','chain_user','chain_state','is_transport','is_forward_order','is_self_take','is_collection','chain_time','chain_close_time');
        if (in_array($_POST['sortname'], $param) && in_array($_POST['sortorder'], array('asc', 'desc'))) {
            $order = $_POST['sortname'] . ' ' . $_POST['sortorder'];
        }

        $fields = "chain_id,store_id,account_id,chain_user,chain_name,chain_phone,chain_img,area_info,chain_address,chain_state,chain_logo,is_transport,is_forward_order,is_self_take,is_collection,chain_time,chain_close_time,store_name,chain_brand_id";

        if (!is_numeric($_GET['curpage'])){
            $count = $chain_model->getChainCount($condition);
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $array = array();
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=chain&op=index');
                Tpl::showpage('export.excel');
                exit();
            }
        } else {
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $limit = $limit1 .','. $limit2;
        }

        $chain_list = $chain_model->getChainList($condition, $fields, null, $order, $limit);
        $this->createCsv($chain_list);
    }

    /**
     * 生成csv文件
     */
    private function createCsv($chain_list) {
        $chain_model = Model('chain');
        $data = array();
        foreach ($chain_list as $value) {
            $param = array();
            $chain_state = $this->_getChainState($value['chain_state']);
            $chain_info = $chain_model->getChainBrandInfo(array('chain_brand_id' => $value['chain_brand_id']),"brand_name");
            if ($chain_info['brand_name']) {
                $param['brand_name'] = $chain_info['brand_name'];
            }else{
                $param['brand_name'] = "暂无";
            }
            $param['chain_id'] = $value['chain_id'];
            $param['chain_name'] = $value['chain_name'];
            $param['account_id'] = $value['account_id'];
            $param['chain_phone'] = $value['chain_phone'];
            $param['is_own'] = $value['is_own'] ==  '1' ? '是' : '否';
            $param['chain_user'] = $value['chain_user'];
            $param['chain_state'] = $chain_state;
            $param['is_transport'] = $value['is_transport'] ==  '1' ? '是' : '否';
            $param['is_forward_order'] = $value['is_forward_order'] ==  '1' ? '是' : '否';
            $param['is_self_take'] = $value['is_self_take'] ==  '1' ? '是' : '否';
            $param['is_collection'] = $value['is_collection'] ==  '1' ? '是' : '否';
            $param['chain_time'] = date('Y-m-d', $value['chain_time']);
            $param['chain_close_time'] = $value['chain_close_time'] ? date('Y-m-d', $value['chain_close_time']) : '--';
            $param['area_info'] = $value['area_info'];
            $param['chain_address'] = $value['chain_address'];
            $param['store_name'] = $value['store_name'];
            $data[$value['chain_id']] = $param;
        }

        $header = array(
            'chain_id' => '门店ID',
            'chain_name' => '门店名称',
        	'account_id' => '财务编码',
        	'chain_phone' => '联系电话',
            'is_own' => '自营',
            'chain_user' => '门店账号',
            'chain_state' => '当前状态',
            'is_transport' => '支持配送',
            'is_forward_order' => '转接订单',
            'is_self_take' => '支持自提',
            'is_collection' => '支持代收货',
            'chain_time' => '开店时间',
            'chain_close_time' => '关闭时间',
            'area_info' => '所在地区',
            'chain_address' => '详细地址',
            'store_name' => '所属店铺',
            'brand_name' => '品牌'
        );
        \Shopnc\Lib::exporter()->output('chain_list' .$_GET['curpage'] . '-'.date('Y-m-d'), $data, $header);
    }


    /**
     * 导出门店核销CSV列表
     */
    public function export_csv_codeOp(){
        $chain_model = Model('chain');
        // 设置页码参数名称
        $condition = array();
        $condition['chain_state'] = array('in',array('0','1','3'));
        if ($_GET['chain_name'] != '') {
            $condition['chain_name'] = array('like', '%' . $_GET['chain_name'] . '%');
        }
        if (!empty($_GET['chain_brand_id'])) {
            $condition['chain_brand_id'] = $_GET['chain_brand_id'];
        }
        if (!empty($_GET['region_id'])) {
            $condition['region_id'] = $_GET['region_id'];
        }
        $order = '';
        $param = array('chain_id','chain_name','chain_user','chain_state','is_transport','is_forward_order','is_self_take','is_collection','chain_time','chain_close_time');
        if (in_array($_POST['sortname'], $param) && in_array($_POST['sortorder'], array('asc', 'desc'))) {
            $order = $_POST['sortname'] . ' ' . $_POST['sortorder'];
        }

        $fields = "chain_id,store_id,account_id,area_id_2,region_id,chain_user,chain_name,chain_phone,chain_brand_id";

        if (!is_numeric($_GET['curpage'])){
            $count = $chain_model->getChainCount($condition);
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $array = array();
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=chain&op=index');
                Tpl::showpage('export.excel');
                exit();
            }
        } else {
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $limit = $limit1 .','. $limit2;
        }
       $chain_list = $chain_model->field($fields)->where($condition)->limit(false)->key('chain_id')->select();
        //$chain_list = $chain_model->getChainList($condition, $fields, null, $order, $limit);
        $chain_id_arr = [];
        foreach ($chain_list as $val){
            $chain_id_arr[] = $val['chain_id'];
        }
        $condition = [];
        $condition['vr_order_code.chain_id'] = ['in',implode(',',$chain_id_arr)];
        $condition['vr_order_code.vr_state'] = 1;
        if (trim($_GET['query_start_date']) && trim($_GET['query_end_date'])) {
            $sdate = strtotime($_GET['query_start_date']);
            $edate = strtotime($_GET['query_end_date']) + 86400 -1;
            $condition['vr_order_code.vr_usetime'] = array('between', array($sdate,$edate));

        } elseif (trim($_GET['query_start_date'])) {
            $sdate = strtotime($_GET['query_start_date']);
            $condition['vr_order_code.vr_usetime'] = array('egt', $sdate);

        } elseif (trim($_GET['query_end_date'])) {
            $edate = strtotime($_GET['query_end_date']) + 86400 -1;
            $condition['vr_order_code.vr_usetime'] = array('elt', $edate);
        }else{
            $sdate = strtotime(date('Y-m-01',strtotime(date("Y-m-d"))));
            $edate = time();
            $condition['vr_order_code.vr_usetime'] = array('between', array($sdate,$edate));
        }
        $fields = 'vr_order_code.chain_id,vr_order_code.order_id,vr_order_code.buyer_id,vr_order_code.chain_name,vr_order_code.vr_usetime,
                    vr_order_code.vr_code,vr_order_code.erp_mobile,vr_order_code.pay_price,vr_order.order_sn,vr_order.goods_id,vr_order.buyer_phone,vr_order.goods_name,
                    vr_order.goods_spec,vr_order.goods_price,goods.goods_commonid,goods.spec_name';
        $on = 'vr_order_code.order_id = vr_order.order_id,vr_order.goods_id = goods.goods_id';
        $order_code_list = Model()->table('vr_order_code,vr_order,goods')->field($fields)->join('left,left')->on($on)->where($condition)->limit(false)->select();
        $this->_chain_code_export($chain_list,$order_code_list);
    }
    /**
     * 导出
     */
    private function _chain_code_export($chain_list, $order_code_list){
        require_once  BASE_ROOT_PATH.'/vendor/phpoffice/phpexcel/Classes/PHPExcel.php';
        require_once  BASE_ROOT_PATH.'/vendor/phpoffice/phpexcel/Classes/PHPExcel/Writer/Excel2007.php';

        $indexKey=[
            'order_sn' => '订单号',
            'goods_id' => '商品ID',
            'goods_name' => '商品名称',
            'goods_commonid' => 'SPU/SKU',
            'buyer_phone'  => '买家帐号',
            'chain_user' => '核销帐号',
            'erp_mobile' => '接收手机号',
            'goods_price'=>'价格',
            'pay_price' => '结算价格',
            'goods_num' => '核销份数',
            'vr_usetime' => '核销时间',
            'account_id' => '财务编码',
            'chain_name' => '核销医院',
            'vr_code' => '核销码',
            'chain_code_text' => '交易状态',

        ];
        if(empty($filename)) $filename = '门店核销数据';
        if( !is_array($indexKey)) return false;
        $excel2007=true;
        $header_arr = [
            'order_sn' => 'A',
            'goods_id' => 'B',
            'goods_name' => 'C',
            'goods_commonid' => 'D',
            'buyer_phone' => 'E',
            'chain_user' => 'F',
            'erp_mobile' => 'G',
            'goods_price' => 'H',
            'pay_price' => 'I',
            'goods_num' => 'J',
            'vr_usetime' => 'K',
            'account_id' =>'L',
            'chain_name' => 'M',
            'vr_code' =>'N',
            'chain_code_text' => 'O',

        ];
        //初始化PHPExcel()
        $objPHPExcel = new PHPExcel();
       //设置保存版本格式
        if($excel2007){
            $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
            $filename = $filename.'.xlsx';
        }else{
            $objWriter = new PHPExcel_Writer_Excel5($objPHPExcel);
            $filename = $filename.'.xls';
        }

        //接下来就是写数据到表格里面去
        $objActSheet = $objPHPExcel->getActiveSheet();
        $startRow = 1;
        foreach ($indexKey as $key => $val) {

            $objActSheet->setCellValue($header_arr[$key].$startRow,$val);
        }

        $data = array();
        foreach ($order_code_list as $value) {
            $param = array();
            $param['order_sn'] = $value['order_sn'];
            $param['goods_id'] = $value['goods_id'];
            $param['goods_name'] = $value['goods_name'];
            $param['goods_commonid'] = $value['goods_commonid'].'|'.$value['goods_spec'];
            $param['buyer_phone'] = $value['buyer_phone'];
            $param['chain_user'] = $chain_list[$value['chain_id']]['chain_user'];
            $param['erp_mobile'] = $value['erp_mobile']?$value['erp_mobile']:$value['buyer_phone'];
            $param['goods_price'] = $value['goods_price'];
            $param['pay_price'] = $value['pay_price'];
            $param['goods_num'] = 1;
            $param['vr_usetime'] = date('Y/m/d H:i:s',$value['vr_usetime']);
            $param['account_id'] = $chain_list[$value['chain_id']]['account_id'];
            $param['chain_name'] =  $value['chain_name'];
            $param['vr_code'] = $value['vr_code'];
            $param['chain_code_text'] = '已完成';
            $data[]=$param;

        }

        foreach ($data as $k=> $row) {

            foreach ($indexKey as $key => $value){

                //这里是设置单元格的内容

                if(in_array($header_arr[$key],['A','E','G','N'])){
                    $objPHPExcel->setActiveSheetIndex(0);
                    $objActSheet->setTitle('Simple');

                    $objActSheet->setCellValueExplicit($header_arr[$key].($k+2),$row[$key],PHPExcel_Cell_DataType::TYPE_STRING);
                }else{
                    $objActSheet->setCellValue($header_arr[$key].($k+2),$row[$key]);
                }
            }
            $startRow++;
        }

        // 下载这个表格，在浏览器输出
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");;
        header('Content-Disposition:attachment;filename='.$filename.'');
        header("Content-Transfer-Encoding:binary");
        $objWriter->save('php://output');

    }


    /**
     * 导出门店销售数据CSV列表
     */
    public function export_csv_chainOp(){
        $chain_model = Model('chain');
        // 设置页码参数名称
        $condition = array();
        $condition['chain_state'] = array('in',array('0','1','3'));
        if ($_GET['chain_name'] != '') {
            $condition['chain_name'] = array('like', '%' . $_GET['chain_name'] . '%');
        }
        if (!empty($_GET['chain_brand_id'])) {
            $condition['chain_brand_id'] = $_GET['chain_brand_id'];
        }
        if (!empty($_GET['region_id'])) {
            $condition['region_id'] = $_GET['region_id'];
        }
        $order = '';
        $param = array('chain_id','chain_name','chain_user','chain_state','is_transport','is_forward_order','is_self_take','is_collection','chain_time','chain_close_time');
        if (in_array($_POST['sortname'], $param) && in_array($_POST['sortorder'], array('asc', 'desc'))) {
            $order = $_POST['sortname'] . ' ' . $_POST['sortorder'];
        }

        $fields = "chain_id,store_id,account_id,area_id_2,region_id,chain_user,chain_name,chain_phone,chain_brand_id";

        if (!is_numeric($_GET['curpage'])){
            $count = $chain_model->getChainCount($condition);
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $array = array();
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=chain&op=index');
                Tpl::showpage('export.excel');
                exit();
            }
        } else {
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $limit = $limit1 .','. $limit2;
        }

        $chain_list = $chain_model->getChainList($condition, $fields, null, $order, $limit);
        $area_id_arr = [];
        foreach ($chain_list as $val){
            $chain_id_arr[] = $val['chain_id'];
            $area_id_arr[] = $val['area_id_2'];
        }
        $area_data =Model('area')->field('area_id,area_name')->where(array('area_id'=>['in',array_unique($area_id_arr)]))->key('area_id')->select();
        $region_data = Model('region')->getRegionList();
        $condition = [];
        $vr_condition = [];
        $refund_condition = [];
        $vr_refund_condition = [];
        $vr_condition['chain_id'] = $condition['chain_id'] = ['in',implode(',',$chain_id_arr)];
        $condition['order_state'] = ['egt',ORDER_STATE_PAY];
        if (trim($_GET['query_start_date']) && trim($_GET['query_end_date'])) {
            $sdate = strtotime($_GET['query_start_date']);
            $edate = strtotime($_GET['query_end_date']) + 86400 -1;
            $condition['payment_time'] = array('between', array($sdate,$edate));
            $vr_condition['vr_usetime'] =  array('between', array($sdate,$edate));
            $refund_condition['orders.payment_time'] =  array('between', array($sdate,$edate));
            $vr_refund_condition['vr_order.payment_time'] =  array('between', array($sdate,$edate));
        } elseif (trim($_GET['query_start_date'])) {
            $sdate = strtotime($_GET['query_start_date']);
            $condition['payment_time'] = array('egt', $sdate);
            $vr_condition['vr_usetime'] = array('egt', $sdate);
            $refund_condition['orders.payment_time'] =  array('egt', $sdate);
            $vr_refund_condition['vr_order.payment_time'] =  array('egt', $sdate);
        } elseif (trim($_GET['query_end_date'])) {
            $edate = strtotime($_GET['query_end_date']) + 86400 -1;
            $condition['payment_time'] = array('elt', $edate);
            $vr_condition['vr_usetime'] = array('elt', $edate);
            $refund_condition['orders.payment_time'] =  array('elt', $edate);
            $vr_refund_condition['vr_order.payment_time'] =  array('elt', $edate);
        }else{
           /* $sdate = strtotime(date('Y-m-01',strtotime(date("Y-m-d"))));
            $edate = time();
            $condition['payment_time'] = array('between', array($sdate,$edate));
            $vr_condition['vr_usetime'] = array('between', array($sdate,$edate));*/
        }
        //实物销售订单数据
        $order_user_num = Model()->table('orders')->field('chain_id,count(order_id) as order_num , sum(order_amount) as order_amount_num,count(distinct(buyer_id)) as order_buyer_num')->where($condition)->group('chain_id')->limit(false)->key('chain_id')->select();

        //医疗团餐销售数据
        $vr_order_usr_num = Model()->table('vr_order')->field('chain_id,count(order_id) as order_num , sum(order_amount) as order_amount_num,count(distinct(buyer_id)) as order_buyer_num')->where($condition)->group('chain_id')->limit(false)->key('chain_id')->select();
        //已核销团餐数据

        $order_code_user_num = Model()->table('vr_order_code')->field('chain_id,count(order_id) as order_num , sum(pay_price) as order_amount_num')->where($vr_condition)->group('chain_id')->limit(false)->key('chain_id')->select();
        //实物退单数据


        $refund_condition['orders.chain_id'] = ['in',implode(',',$chain_id_arr)];
        $refund_condition['refund_return.seller_state'] = 2;//商家处理状态:1为待审核,2为同意,3为不同意
        $refund_condition['refund_return.refund_state'] = 3;//管理员处理状态:1为处理中,2为待处理,3为已完成
        $refund_list = Model()->table('orders,refund_return')->field('orders.chain_id,sum(refund_return.refund_amount) as order_refund_amount,count(refund_return.order_id) as order_num')
            ->join('left')->on('orders.order_id=refund_return.order_id')
            ->group('orders.chain_id')->where($refund_condition)->limit(false)->key('chain_id')->select();

        //团餐退单数据

        $vr_refund_condition['vr_order.chain_id'] = ['in',implode(',',$chain_id_arr)];
        $vr_refund_condition['vr_refund.admin_state'] = 2;//管理员处理状态:1为处理中,2为待处理,3为已完成
        $vr_refund_list = Model()->table('vr_order,vr_refund')->field('vr_order.chain_id,sum(vr_refund.refund_amount) as order_refund_amount,count(vr_refund.order_id) as order_num')
            ->join('left')->on('vr_order.order_id=vr_refund.order_id')
            ->group('vr_order.chain_id')->where($vr_refund_condition)->limit(false)->key('chain_id')->select();

        $this->chain_export($chain_list, $order_user_num, $vr_order_usr_num,$refund_list,$vr_refund_list,$order_code_user_num,$area_data,$region_data);
    }
    /**
     * 导出
     */
    public function chain_export($chain_list, $order_user_num, $vr_order_usr_num, $refund_list, $vr_refund_list, $order_code_user_num,$area_data,$region_data){
        require_once  BASE_ROOT_PATH.'/vendor/phpoffice/phpexcel/Classes/PHPExcel.php';
        require_once  BASE_ROOT_PATH.'/vendor/phpoffice/phpexcel/Classes/PHPExcel/Writer/Excel2007.php';

        $indexKey=[
            'chain_id' => '门店ID',
            'chain_name' => '门店名称',
            'account_id' => '财务编码',
            'chain_user' => '登录用户名',
            'area_name'  => '城市',
            'region_name' => '大区',
            'chain_buyer' => '实物下单用户数',
            'chain_order_num' => '实物订单数量',
            'chain_order_amount_num' => '实物订单总额',
            'chain_vr_buyer' => '医疗下单用户数',
            'chain_vr_order_num' => '医疗订单数量',
            'chain_vr_order_amount_num' => '医疗订单总额',

            'chain_order_refund_num' => '实物退款数量',
            'chain_order_refund_amount' => '实物退款总额',

            'chain_vr_order_refund_num' => '医疗退款数量',
            'chain_vr_order_refund_amount' => '医疗退款总额',

            'chain_vr_order_code_num' => '实际核销数量',
            'chain_vr_order_code_amount' => '实际核销金额',
            'chain_order_count_amount_num' => '订单支付总金额',
            'chain_order_refunf_count_amount_num' => '订单退款总金额',
            'query_start_date' => '开始时间',
            'query_end_date' => '结束时间'
        ];
        if(empty($filename)) $filename = '门店销售数据';
        if( !is_array($indexKey)) return false;
        $excel2007=true;
        $header_arr = [
            'chain_id' => 'A',
            'chain_name' => 'B',
            'account_id' => 'C',
            'chain_user' => 'D',
            'area_name' => 'E',
            'region_name' => 'F',
            'chain_buyer' => 'G',
            'chain_order_num' => 'H',
            'chain_order_amount_num' => 'I',
            'chain_vr_buyer' => 'J',
            'chain_vr_order_num' => 'K',
            'chain_vr_order_amount_num' => 'L',

            'chain_order_refund_num' => 'M',
            'chain_order_refund_amount' => 'N',

            'chain_vr_order_refund_num' => 'O',
            'chain_vr_order_refund_amount' => 'P',

            'chain_vr_order_code_num' => 'Q',
            'chain_vr_order_code_amount' => 'R',
            'chain_order_count_amount_num' => 'S',
            'chain_order_refunf_count_amount_num' => 'T',
            'query_start_date' => 'U',
            'query_end_date' => 'V'
        ];
        //初始化PHPExcel()
        $objPHPExcel = new PHPExcel();

        //设置保存版本格式
        if($excel2007){
            $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
            $filename = $filename.'.xlsx';
        }else{
            $objWriter = new PHPExcel_Writer_Excel5($objPHPExcel);
            $filename = $filename.'.xls';
        }

        //接下来就是写数据到表格里面去
        $objActSheet = $objPHPExcel->getActiveSheet();
        $startRow = 1;
        foreach ($indexKey as $key => $val) {

            $objActSheet->setCellValue($header_arr[$key].$startRow,$val);
        }

        $data = array();
        foreach ($chain_list as $value) {
            $param = array();

            $param['chain_id'] = $value['chain_id'];
            $param['chain_name'] = $value['chain_name'];
            $param['account_id'] = $value['account_id'];
            $param['chain_user'] = $value['chain_user'];
            $param['area_name'] = $area_data[$value['area_id_2']]['area_name'];
            foreach ($region_data as $val){
                if($val['region_id'] ==$value['region_id']){

                    $param['region_name'] = $val['region_name'];
                }
            }
            $param['chain_buyer'] = $order_user_num[$value['chain_id']]['order_buyer_num']?$order_user_num[$value['chain_id']]['order_buyer_num']:0;
            $param['chain_order_num'] = $order_user_num[$value['chain_id']]['order_num']?$order_user_num[$value['chain_id']]['order_num']:0;
            $param['chain_order_amount_num'] = $order_user_num[$value['chain_id']]['order_amount_num']?$order_user_num[$value['chain_id']]['order_amount_num']:0;

            $param['chain_vr_buyer'] = $vr_order_usr_num[$value['chain_id']]['order_buyer_num']?$vr_order_usr_num[$value['chain_id']]['order_buyer_num']:0;
            $param['chain_vr_order_num'] = $vr_order_usr_num[$value['chain_id']]['order_num']?$vr_order_usr_num[$value['chain_id']]['order_num']:0;
            $param['chain_vr_order_amount_num'] = $vr_order_usr_num[$value['chain_id']]['order_amount_num']?$vr_order_usr_num[$value['chain_id']]['order_amount_num']:0;
            $param['chain_order_refund_num'] = $refund_list[$value['chain_id']]['order_num']?$refund_list[$value['chain_id']]['order_num']:0;
            $param['chain_order_refund_amount'] = $refund_list[$value['chain_id']]['order_refund_amount']?$refund_list[$value['chain_id']]['order_refund_amount']:0;

            $param['chain_vr_order_refund_num'] = $vr_refund_list[$value['chain_id']]['order_num']?$vr_refund_list[$value['chain_id']]['order_num']:0;

            $param['chain_vr_order_refund_amount'] = $vr_refund_list[$value['chain_id']]['order_refund_amount']?$vr_refund_list[$value['chain_id']]['order_refund_amount']:0;

            $param['chain_vr_order_code_num'] = $order_code_user_num[$value['chain_id']]['order_num']?$order_code_user_num[$value['chain_id']]['order_num']:0;

            $param['chain_vr_order_code_amount'] = $order_code_user_num[$value['chain_id']]['order_amount_num']?$order_code_user_num[$value['chain_id']]['order_amount_num']:0;
            $param['chain_order_count_amount_num'] = $param['chain_order_amount_num'] +  $param['chain_vr_order_amount_num'];
            $param['chain_order_refunf_count_amount_num'] = $param['chain_order_refund_amount'] +  $param['chain_vr_order_refund_amount'];

            $param['query_start_date'] = $_GET['query_start_date'];
            $param['query_end_date'] = $_GET['query_end_date'];
            $data[]=$param;

        }

        foreach ($data as $k=> $row) {

            foreach ($indexKey as $key => $value){

                //这里是设置单元格的内容
                $objActSheet->setCellValue($header_arr[$key].($k+2),$row[$key]);
            }
            $startRow++;
        }

        // 下载这个表格，在浏览器输出
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");;
        header('Content-Disposition:attachment;filename='.$filename.'');
        header("Content-Transfer-Encoding:binary");
        $objWriter->save('php://output');

    }


    //获取门店状态
    private function _getChainState($state){
        $return = '正常';
        switch($state){
            case 0:
                $return = "关闭";
                break;
            case 2:
                $return = "审核中";
                break;
            case 3:
                $return = "未通过";
                break;
            case 4:
                $return = "待付保证金";
                break;
            case 5:
                $return = "待保证金审核";
                break;
        }
        return $return;
    }

    //获取门店配送区域数据
    private function _getChianTransportAreas(& $chain_info){
        $chain_info['transport_area_arr'] = array();
        $model_area = Model('area');
        $area_all_info = $model_area->getAreas();
        $area_children_arr = $area_all_info['children'];
        if ($chain_info['area_id'] == $chain_info['area_id_3'] || $chain_info['area_id'] == $chain_info['area_id_4']) {
            $area_ids_3 = $area_children_arr[$chain_info['area_id_2']];
            $chain_info['transport_area_arr'] = $this->_makeAreaData($area_ids_3);
        } elseif ($chain_info['area_id'] == $chain_info['area_id_2']) {
            $area_ids_2 = $area_children_arr[$chain_info['area_id_1']];
            $chain_info['transport_area_arr'] = $this->_makeAreaData($area_ids_2);
        } else {
            $chain_info['transport_area_arr'] = $this->_makeAreaData(array($chain_info['area_id_1']));
        }
    }
    private function _makeAreaData($area_ids){
        $model_area = Model('area');
        $area_list = $model_area->getAreaNames();
        $return_data = array();
        foreach($area_ids as $v){
            $tmp_area = array();
            $tmp_area['area_id'] = $v;
            $tmp_area['area_name'] = $area_list[$v];
            $return_data[] = $tmp_area;
        }
        return $return_data;
    }
}