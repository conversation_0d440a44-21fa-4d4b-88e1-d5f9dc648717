<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title">
      <div class="subject">
        <h3><?php echo $lang['stat_order_account_check'];?></h3>
        <h5><?php echo $lang['stat_order_account_check_subhead'];?></h5>
      </div>
    </div>
  </div>
  <!-- 操作说明 -->
  <div class="explanation" id="explanation">
    <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
      <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
      <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
    <ul>
      <li><?php echo $lang['stat_order_account_check_help1'];?></li>
      <li><?php echo $lang['stat_order_account_check_help2'];?></li>
      <li><?php echo $lang['stat_order_account_check_help3'];?></li>
    </ul>
  </div> 
  <div id="flexigrid"></div>
<div class="ncap-search-ban-s" id="searchBarOpen"><i class="fa fa-search-plus"></i>高级搜索</div>
  <div class="ncap-search-bar">
    <div class="handle-btn" id="searchBarClose"><i class="fa fa-search-minus"></i>收起边栏</div>
    <div class="title">
      <h3>高级搜索</h3>
    </div>
    <form method="get" name="formSearch" id="formSearch">
      <div id="searchCon" class="content">
        <div class="layout-box">
          <dl>
            <dt>日期筛选</dt>
            <dd>
              <label>
                <select class="s-select" name="qtype_time">
                  <option selected="selected" value="">-请选择-</option>
                  <option value="orders.add_time">下单时间</option>
                  <option value="orders.payment_time">支付时间</option>
                  <option value="orders.finnshed_time">完成时间 </option>
                  <option value="refund_return.refund_time">退款时间 </option>
                </select>
              </label>
              <label>
                <input readonly id="query_start_date" placeholder="请选择起始时间" name=query_start_date value="" type="text" class="s-input-txt" />
              </label>
              <label>
                <input readonly id="query_end_date" placeholder="请选择结束时间" name="query_end_date" value="" type="text" class="s-input-txt" />
              </label>
            </dd>
          </dl>

            <?php  require_once("sale_channel.php");?>

        </div>
      </div>
      <div class="bottom"> <a href="javascript:void(0);" id="ncsubmit" class="ncap-btn ncap-btn-green mr5">提交查询</a><a href="javascript:void(0);" id="ncreset" class="ncap-btn ncap-btn-orange" title="撤销查询结果，还原列表项所有内容"><i class="fa fa-retweet"></i><?php /*echo $lang['nc_cancel_search'];*/?></a></div>
    </form>
  </div>
</div>
<script type="text/javascript">
$(function(){
	$('#query_start_date').datepicker();
    $('#query_end_date').datepicker();
    // 高级搜索提交
    $('#ncsubmit').click(function(){
        $("#flexigrid").flexOptions({url: 'index.php?act=stat_order_account_check&op=get_xml&'+$("#formSearch").serialize(),query:'',qtype:''}).flexReload();
    });
    // 高级搜索重置
    $('#ncreset').click(function(){
        $("#flexigrid").flexOptions({url: 'index.php?act=stat_order_account_check&op=get_xml'}).flexReload();
        $("#formSearch")[0].reset();
    });
    $("#flexigrid").flexigrid({
        url: 'index.php?act=stat_order_account_check&op=get_xml',
        colModel : [
            //{display: '操作', name : 'operation', width : 150, sortable : false, align: 'center', className: 'handle'},
            {display: '生成订单时间', name : 'add_time', width : 150, sortable : false, align: 'left'},
            {display: '支付时间', name : 'payment_time', width : 150, sortable : false, align: 'left'},
            /*{display: '支付单号', name : 'pay_sn', width : 150, sortable : false, align: 'left'},
            {display: '交易单号', name : 'trade_no', width : 150, sortable : false, align: 'left'},*/
            {display: '订单名称', name : 'order_name', width : 150, sortable : false, align: 'left'},
            {display: '支付方式', name : 'payment_code', width : 150, sortable : false, align: 'left'},
            {display: '尾款支付方式', name : 'last_payment_code', width : 150, sortable : false, align: 'left'},
            {display: '支付单号', name : 'pay_sn', width : 150, sortable : false, align: 'left'},
            {display: '尾款支付单号', name : 'last_pay_sn', width : 150, sortable : false, align: 'left'},
            {display: '主单金额', name : 'order_amount', width : 300, sortable : false, align: 'left'},
            {display: '主订单号', name : 'main_order_sn', width : 150, sortable : false, align: 'left'},
            {display: '子订单号', name : 'child_order_sn', width : 150, sortable : false, align: 'left'},
            {display: '退款单号', name : 'refund_order_sn', width : 150, sortable : false, align: 'left'},
            {display: '退款时间', name : 'refund_time', width : 150, sortable : false, align: 'left'},
            {display: '退款金额', name : 'refund_amount', width : 150, sortable : false, align: 'left'},
            {display: '主订单状态', name : 'order_state', width : 150, sortable : false, align: 'left'},
            {display: '子订单状态', name : 'child_order_state', width : 150, sortable : false, align: 'left'},
            {display: '是否分销', name : 'is_distribute', width : 150, sortable : false, align: 'left'},
            {display: '门店', name : 'store_name', width : 150, sortable : false, align: 'left'},
            {display: '分销员ID', name : 'dis_member_id', width : 150, sortable : false, align: 'left'},
            ],
        <?php if(true){?>
        buttons : [
			{display: '<i class="fa fa-file-excel-o"></i>导出订单数据', name : 'csv', bclass : 'csv', title : '将选定行数据导出excel文件,如果不选中行，将导出列表所有数据', onpress : fg_operate_order },
        ],
        <?php }?>
        <?php if( in_array($output['admin_id'],[26,6])){?>
        buttons : [
            {display: '<i class="fa fa-file-excel-o"></i>导出订单数据', name : 'csv', bclass : 'csv', title : '将选定行数据导出excel文件,如果不选中行，将导出列表所有数据', onpress : fg_operate_order },
        ],
        <?php }?>

        searchitems : [
            {display: '主订单号', name : 'main_order_sn', isdefault: true},
            {display: '子订单号', name : 'sub_order_sn'},
            /*{display: '商户单号', name : 'sub_order_sn'},*/
            {display: '支付单号', name : 'orders.pay_sn'},
            {display: '退款单号', name : 'refund_return.refund_sn'},
            ],
        sortname: "orders.order_id",
        sortorder: "desc",
        title: '财务对账明细-实物',
        onSuccess:function(){
            // v7.0 注释掉 根本没有用到
            // $.ajax({
            //     type: "get",
            //     dataType: "json",
            //     url: "index.php?act=stat_order_account_check&op=get_order_sellnum",
            //     data: {},
            //     success: function(data){
            //         if (data.order_all_num){
            //             $('#order_sellnum').html(data.order_all_num);
            //         } else {
            //
            //         }
            //     }
            // });
        }
    });
});
function fg_operate(name, grid) {
    if (name == 'csv') {
    	var itemlist = new Array();
        if($('.trSelected',grid).length>0){
            $('.trSelected',grid).each(function(){
            	itemlist.push($(this).attr('data-id'));
            });
        }
        fg_csv(itemlist);
    }
   
}

function fg_operate_order(name, grid) {
    if (name == 'csv') {
    	var itemlist = new Array();
        if($('.trSelected',grid).length>0){
            $('.trSelected',grid).each(function(){
            	itemlist.push($(this).attr('data-id'));
            });
        }
        fg_csv2(itemlist);
    }
   
}


function fg_csv(ids) {
    id = ids.join(',');
    window.location.href = $("#flexigrid").flexSimpleSearchQueryString()+'&op=export_step1&order_id=' + id;
}

function fg_csv2(ids) {
    id = ids.join(',');
    window.location.href = $("#flexigrid").flexSimpleSearchQueryString()+'&op=export_step2&order_id=' + id;
}
</script> 
