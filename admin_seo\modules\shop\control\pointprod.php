<?php
/**
 * 兑换礼品管理
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;
use Upet\Queues\DeleteFileQueue;

defined('InShopNC') or exit('Access Invalid!');
class pointprodControl extends SystemControl{
    /**
     * 每次导出订单数量
     * @var int
     */
    const EXPORT_SIZE = 10000;
    public function __construct(){
        parent::__construct();
        Language::read('pointprod,pointorder');
    }

    public function indexOp() {
        $this->pointprodOp();
    }

    /**
     * 积分礼品列表
     */
    public function pointprodOp()
    {
        Tpl::showpage('pointprod.list');
    }

    /**
     * 积分礼品列表
     */
    public function pointprod_xmlOp()
    {
        $condition = array();

        if ($_REQUEST['advanced']) {
            if (strlen($q = trim((string) $_REQUEST['pgoods_name']))) {
                $condition['pgoods_name'] = array('like', '%' . $q . '%');
            }
            if (strlen($q = trim((string) $_REQUEST['pgoods_show']))) {
                $condition['pgoods_show'] = (int) $q;
            }
            if (strlen($q = trim((string) $_REQUEST['pgoods_commend']))) {
                $condition['pgoods_commend'] = (int) $q;
            }
        } else {
            if (strlen($q = trim($_REQUEST['query']))) {
                switch ($_REQUEST['qtype']) {
                    case 'pgoods_name':
                        $condition['pgoods_name'] = array('like', '%'.$q.'%');
                        break;
                }
            }
        }

        switch ($_REQUEST['sortname']) {
            case 'pgoods_points':
            case 'pgoods_price':
            case 'pgoods_storage':
            case 'pgoods_view':
            case 'pgoods_salenum':
                $sort = $_REQUEST['sortname'];
                break;
            default:
                $sort = 'pgoods_sort asc, pgoods_id';
                break;
        }
        if ($_REQUEST['sortorder'] != 'asc') {
            $sort .= ' desc';
        }

        $pointprod_model = Model('pointprod');
        $prod_list = (array) $pointprod_model->getPointProdList(
            $condition,
            '*',
            $sort,
            0,
            $_REQUEST['rp']
        );

        $data = array();
        $data['now_page'] = $pointprod_model->shownowpage();
        $data['total_num'] = $pointprod_model->gettotalnum();

        foreach ($prod_list as $val) {
            $o = '<a class="btn red confirm-del-on-click" href="javascript:;" data-href="' . urlAdminShop('pointprod', 'prod_drop', array(
                'pg_id' => $val['pgoods_id'],
            )) . '"><i class="fa fa-trash-o"></i>删除</a>';

            $o .= '<span class="btn"><em><i class="fa fa-cog"></i>设置<i class="arrow"></i></em><ul>';

            $o .= '<li><a href="' . urlAdminShop('pointprod', 'prod_edit', array(
                'pg_id' => $val['pgoods_id'],
            )) . '">编辑礼品</a></li>';

            if ($val['pgoods_show'] == '1') {
                $o .= '<li><a href="javascript:;" data-ie-column="pgoods_show" data-ie-value="0">下架礼品</a></li>';
            } else {
                $o .= '<li><a href="javascript:;" data-ie-column="pgoods_show" data-ie-value="1">上架礼品</a></li>';
            }

            if ($val['pgoods_commend'] == '1') {
                $o .= '<li><a href="javascript:;" data-ie-column="pgoods_commend" data-ie-value="0">取消推荐</a></li>';
            } else {
                $o .= '<li><a href="javascript:;" data-ie-column="pgoods_commend" data-ie-value="1">推荐礼品</a></li>';
            }

            $o .= '</ul></span>';

            $i = array();
            $i['operation'] = $o;

            $i['pgoods_name'] = '<a target="_blank" href="' . urlShop('pointprod', 'pinfo', array( 'id' => $val['pgoods_id'])) . '">'.
                $val['pgoods_name'].'<i class="fa fa-external-link " title="新窗口打开"></i></a>';
            if($val['pggoods_type'] == 2) {
                $i['pggoods_type'] = "服务套餐";
            }elseif ($val['pggoods_type'] == 3) {
                $i['pggoods_type'] = "优惠券";
            }else {
                $i['pggoods_type'] = "实物商品";
            }
            $i['pgoods_image_url'] = <<<EOB
<a href="javascript:;" class="pic-thumb-tip"
onmouseout="toolTip()" onmouseover="toolTip('<img src=\'{$val['pgoods_image_small']}\'>')">
<i class='fa fa-picture-o'></i></a>
EOB;

            $i['pgoods_points'] = $val['pgoods_points'];
            $i['pgoods_price'] = $val['pgoods_price'];
            $i['pgoods_storage'] = $val['pgoods_storage'];
            $i['pgoods_view'] = $val['pgoods_view'];
            $i['pgoods_salenum'] = $val['pgoods_salenum'];

            $i['pgoods_show_onoff'] = $val['pgoods_show'] == '1'
                ? '<span class="yes"><i class="fa fa-check-circle"></i>是</span>'
                : '<span class="no"><i class="fa fa-ban"></i>否</span>';

            $i['pgoods_commend_onoff'] = $val['pgoods_commend'] == '1'
                ? '<span class="yes"><i class="fa fa-check-circle"></i>是</span>'
                : '<span class="no"><i class="fa fa-ban"></i>否</span>';

            $data['list'][$val['pgoods_id']] = $i;
        }

        echo Tpl::flexigridXML($data);
        exit;
    }

    /**
     * 积分礼品添加
     */
    public function prod_addOp(){
        $hourarr = array('00','01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','18','19','20','21','22','23');
        $upload_model = Model('upload');
        if (chksubmit()){
            //验证表单
            $obj_validate = new Validate();
            $validate_arr[] = array("input"=>$_POST["goodsname"],"require"=>"true","message"=>L('admin_pointprod_add_goodsname_error'));
            $validate_arr[] = array("input"=>$_POST["goodsprice"],"require"=>"true","validator"=>"DoublePositive","message"=>L('admin_pointprod_add_goodsprice_number_error'));
            $validate_arr[] = array('input'=>$_POST['goodspoints'],'require'=>'true','validator'=>'IntegerPositive','message'=>L('admin_pointprod_add_goodspoint_number_error'));
            $validate_arr[] = array('input'=>$_POST['goodsserial'],'require'=>'true','message'=>L('admin_pointprod_add_goodsserial_null_error'));
            $validate_arr[] = array('input'=>$_POST['goodsstorage'],'require'=>'true','validator'=>'IntegerPositive','message'=>L('admin_pointprod_add_storage_number_error'));
            $validate_arr[] = array('input'=>$_POST['sort'],'require'=>'true','validator'=>'IntegerPositive','message'=>L('admin_pointprod_add_sort_number_error'));
            if ($_POST['islimit'] == 1){
                $validate_arr[] = array('input'=>$_POST['limitnum'],'validator'=>'IntegerPositive','message'=>L('admin_pointprod_add_limitnum_digits_error'));
            }
            if ($_POST['islimittime']){
                $validate_arr[] = array('input'=>$_POST['starttime'],'require'=>'true','message'=>L('admin_pointprod_add_limittime_null_error'));
                $validate_arr[] = array('input'=>$_POST['endtime'],'require'=>'true','message'=>L('admin_pointprod_add_limittime_null_error'));
            }
            $obj_validate->validateparam = $validate_arr;
            $error = $obj_validate->validate();
            if ($error != '') {
                showDialog(L('error') . $error, '', 'error');
            }

            $model_pointprod = Model('pointprod');
            $goods_array = array();
            $goods_array['pgoods_name'] = trim($_POST['goodsname']);
            $goods_array['pgoods_jingle'] = trim($_POST['pgoods_jingle']);
            $goods_array['pgoods_tag'] = trim($_POST['goodstag']);
            $goods_array['pgoods_price'] = trim($_POST['goodsprice']);//礼品原价

            $goods_array['pgoods_points'] = trim($_POST['goodspoints']);
            $goods_array['pgoods_serial'] = trim($_POST['goodsserial']);//礼品编号
            $goods_array['pgoods_storage'] = intval($_POST['goodsstorage']);
            $goods_array['pggoods_type'] = intval($_POST['pggoods_type']);
            if ($goods_array['pggoods_type'] == "2") { //虚拟商品
                $goods_array['pggoods_scrminfo'] = trim($_POST['goods_sku']);
                $goods_array['pggoods_orderprice'] = floatval($_POST['vr_orderprice']);
                $goods_array['pggoods_valid_days'] = intval($_POST['vr_valid_days']);
            } elseif ($goods_array['pggoods_type'] == "3") { //优惠券
                $goods_array['pggoods_scrminfo'] = trim($_POST['coupons_id']);
                $goods_array['pggoods_orderprice'] = floatval($_POST['coupons_orderprice']);
                $goods_array['pggoods_valid_days'] = intval($_POST['coupons_valid_days']);
            } else {
                $goods_array['pggoods_goodtype'] = intval($_POST['pggoods_goodtype']);
            }
            $goods_array['pgoods_islimit'] = intval($_POST['islimit']);
            if ($goods_array['pgoods_islimit'] == 1){
                $goods_array['pgoods_limitnum'] = intval($_POST['limitnum']);
            }else {
                $goods_array['pgoods_limitnum'] = 0;
            }
            $goods_array['pgoods_islimittime'] = intval($_POST['islimittime']);
            if ($goods_array['pgoods_islimittime'] == 1){
                //如果添加了开始时间
                if (trim($_POST['starttime'])){
                    $starttime = trim($_POST['starttime']);
                    $sdatearr = explode('-',$starttime);
                    $starttime = mktime(intval($_POST['starthour']),0,0,$sdatearr[1],$sdatearr[2],$sdatearr[0]);
                    unset($sdatearr);
                }
                if (trim($_POST['endtime'])){
                    $endtime = trim($_POST['endtime']);
                    $edatearr = explode('-',$endtime);
                    $endtime = mktime(intval($_POST['endhour']),0,0,$edatearr[1],$edatearr[2],$edatearr[0]);
                }
                $goods_array['pgoods_starttime'] = $starttime;
                $goods_array['pgoods_endtime'] = $endtime;
            }else {
                $goods_array['pgoods_starttime'] = '';
                $goods_array['pgoods_endtime'] = '';
            }
            $goods_array['pgoods_show']     = trim($_POST['showstate']);
            $goods_array['pgoods_commend']  = trim($_POST['commendstate']);
            $goods_array['pgoods_add_time'] = time();
            $goods_array['pgoods_state']        = intval($_POST['forbidstate']);
            $goods_array['pgoods_close_reason']     = trim($_POST['forbidreason']);
            $goods_array['pgoods_keywords']     = trim($_POST['keywords']);
            $goods_array['pgoods_description']   = trim($_POST['description']);
            $goods_array['pgoods_body']   = trim($_POST['pgoods_body']);
            $goods_array['pgoods_sort']   = intval($_POST['sort']);
            $goods_array['pgoods_limitmgrade']   = intval($_POST['limitgrade']);

            //添加礼品代表图片
            $indeximg_succ = false;
            if (!empty($_FILES['goods_image']['name'])){
                $upload = new UploadFile();
                $upload->set('default_dir',ATTACH_POINTPROD);
                $upload->set('thumb_width', '60,240');
                $upload->set('thumb_height','60,240');
                $upload->set('thumb_ext',   '_small,_mid');
                $result = $upload->upfile('goods_image', true);
                if ($result){
                    $indeximg_succ = true;
                    $goods_array['pgoods_image'] = $upload->file_name;
                }else {
                    showDialog($upload->error,'','error');
                }
            }
            $state = $model_pointprod->addPointGoods($goods_array);
            if($state){
                //礼品代表图片数据入库
                if ($indeximg_succ){
                    $insert_array = array();
                    $insert_array['file_name'] = $upload->file_name;
                    $insert_array['file_thumb'] = $upload->thumb_image;
                    $insert_array['upload_type'] = 5;
                    $insert_array['file_size'] = intval($_FILES[trim($_POST['goods_image'])]['size']);
                    $insert_array['item_id'] = $state;
                    $insert_array['upload_time'] = time();
                    $upload_model->add($insert_array);
                }
                //更新积分礼品描述图片
                $file_idstr = '';
                if (is_array($_POST['file_id']) && count($_POST['file_id'])>0){
                    $file_idstr = "'".implode("','",$_POST['file_id'])."'";
                }
                $upload_model->updatebywhere(array('item_id'=>$state),array('upload_type'=>6,'item_id'=>'0','upload_id_in'=>"{$file_idstr}"));
                $this->log(L('admin_pointprod_add_title').'['.$_POST['goodsname'].']');
                showDialog(L('admin_pointprod_add_success'),'index.php?act=pointprod&op=pointprod','succ');
            }
        }
        //模型实例化
        $where = array();
        $where['upload_type'] = '6';
        $where['item_id'] = '0';
        $file_upload = $upload_model->getUploadList($where);
        if (is_array($file_upload)){
            foreach ($file_upload as $k => $v){
                $file_upload[$k]['upload_path'] = pointprodThumb($file_upload[$k]['file_name']);
            }
        }
        Tpl::output('file_upload',$file_upload);
        Tpl::output('PHPSESSID',session_id());
        $hourarr = array('00','01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','18','19','20','21','22','23');
        Tpl::output('hourarr',$hourarr);
        //会员级别
        $member_grade = Model('member')->getMemberGradeArr();
        Tpl::output('member_grade',$member_grade);
        Tpl::showpage('pointprod.add');
    }

    /**
     * 积分礼品编辑
     */
    public function prod_editOp(){
        $hourarr = array('00','01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','18','19','20','21','22','23');
        $upload_model = Model('upload');
        $pg_id = intval($_GET['pg_id']);
        if (!$pg_id){
            showDialog(L('admin_pointprod_parameter_error'),'index.php?act=pointprod&op=pointprod','error');
        }
        $model_pointprod = Model('pointprod');
        //查询礼品记录是否存在
        $prod_info = $model_pointprod->getPointProdInfo(array('pgoods_id'=>$pg_id));
        if (!$prod_info){
            showDialog(L('admin_pointprod_record_error'),'index.php?act=pointprod&op=pointprod','error');
        }
        if (chksubmit()){
            //验证表单
            $obj_validate = new Validate();
            $validate_arr[] = array("input"=>$_POST["goodsname"],"require"=>"true","message"=>L('admin_pointprod_add_goodsname_error'));
            $validate_arr[] = array("input"=>$_POST["goodsprice"],"require"=>"true","validator"=>"DoublePositive","message"=>L('admin_pointprod_add_goodsprice_number_error'));
            $validate_arr[] = array('input'=>$_POST['goodspoints'],'require'=>'true','validator'=>'IntegerPositive','message'=>L('admin_pointprod_add_goodspoint_number_error'));
            $validate_arr[] = array('input'=>$_POST['goodsserial'],'require'=>'true','message'=>L('admin_pointprod_add_goodsserial_null_error'));
            $validate_arr[] = array('input'=>$_POST['goodsstorage'],'require'=>'true','validator'=>'IntegerPositive','message'=>L('admin_pointprod_add_storage_number_error'));
            $validate_arr[] = array('input'=>$_POST['sort'],'require'=>'true','validator'=>'IntegerPositive','message'=>L('admin_pointprod_add_sort_number_error'));
            if ($_POST['islimit'] == 1){
                $validate_arr[] = array('input'=>$_POST['limitnum'],'validator'=>'IntegerPositive','message'=>L('admin_pointprod_add_limitnum_digits_error'));
            }
            if ($_POST['islimittime']){
                $validate_arr[] = array('input'=>$_POST['starttime'],'require'=>'true','message'=>L('admin_pointprod_add_limittime_null_error'));
                $validate_arr[] = array('input'=>$_POST['endtime'],'require'=>'true','message'=>L('admin_pointprod_add_limittime_null_error'));
            }
            $obj_validate->validateparam = $validate_arr;
            $error = $obj_validate->validate();
            if ($error != ''){
                showDialog(L('error').$error,'','error');
            }
            //实例化店铺商品模型
            $model_pointprod    = Model('pointprod');

            $goods_array            = array();
            $goods_array['pgoods_name']     = trim($_POST['goodsname']);
            $goods_array['pgoods_jingle']     = trim($_POST['pgoods_jingle']);
            $goods_array['pgoods_tag']      = trim($_POST['goodstag']);
            $goods_array['pgoods_price']    = trim($_POST['goodsprice']);
            $goods_array['pggoods_type']  = intval($_POST['pggoods_type']);
            if ($goods_array['pggoods_type'] == "2") { //虚拟商品
                $goods_array['pggoods_scrminfo'] = trim($_POST['goods_sku']);
                $goods_array['pggoods_orderprice'] = floatval($_POST['vr_orderprice']);
                $goods_array['pggoods_valid_days'] = intval($_POST['vr_valid_days']);
            } elseif ($goods_array['pggoods_type'] == "3") { //优惠券
                $goods_array['pggoods_scrminfo'] = trim($_POST['coupons_id']);
                $goods_array['pggoods_orderprice'] = floatval($_POST['coupons_orderprice']);
                $goods_array['pggoods_valid_days'] = intval($_POST['coupons_valid_days']);
            } else {
                $goods_array['pggoods_goodtype'] = intval($_POST['pggoods_goodtype']);
            }
            $goods_array['pggoods_goodtype'] = intval($_POST['pggoods_goodtype']);
            $goods_array['pgoods_points'] = trim($_POST['goodspoints']);
            $goods_array['pgoods_serial']   = trim($_POST['goodsserial']);
            $goods_array['pgoods_storage']  = intval($_POST['goodsstorage']);
            $goods_array['pgoods_islimit'] = intval($_POST['islimit']);
            if ($goods_array['pgoods_islimit'] == 1){
                $goods_array['pgoods_limitnum'] = intval($_POST['limitnum']);
            }else {
                $goods_array['pgoods_limitnum'] = 0;
            }
            $goods_array['pgoods_islimittime'] = intval($_POST['islimittime']);
            if ($goods_array['pgoods_islimittime'] == 1){
                //如果添加了开始时间
                if (trim($_POST['starttime'])){
                    $starttime = trim($_POST['starttime']);
                    $sdatearr = explode('-',$starttime);
                    $starttime = mktime(intval($_POST['starthour']),0,0,$sdatearr[1],$sdatearr[2],$sdatearr[0]);
                    unset($sdatearr);
                }
                if (trim($_POST['endtime'])){
                    $endtime = trim($_POST['endtime']);
                    $edatearr = explode('-',$endtime);
                    $endtime = mktime(intval($_POST['endhour']),0,0,$edatearr[1],$edatearr[2],$edatearr[0]);
                }
                $goods_array['pgoods_starttime'] = $starttime;
                $goods_array['pgoods_endtime'] = $endtime;
            }else {
                $goods_array['pgoods_starttime'] = '';
                $goods_array['pgoods_endtime'] = '';
            }
            $goods_array['pgoods_show']     = trim($_POST['showstate']);
            $goods_array['pgoods_commend']  = trim($_POST['commendstate']);
            $goods_array['pgoods_state']        = intval($_POST['forbidstate']);
            $goods_array['pgoods_close_reason']     = trim($_POST['forbidreason']);
            $goods_array['pgoods_keywords']     = trim($_POST['keywords']);
            $goods_array['pgoods_description']   = trim($_POST['description']);
            $goods_array['pgoods_body']   = trim($_POST['pgoods_body']);
            $goods_array['pgoods_sort']   = intval($_POST['sort']);
            $goods_array['pgoods_limitmgrade']   = intval($_POST['limitgrade']);
            //添加礼品代表图片
            $indeximg_succ = false;
            if (!empty($_FILES['goods_image']['name'])){
                $upload = new UploadFile();
                $upload->set('default_dir',ATTACH_POINTPROD);
                $upload->set('thumb_width', '60,240');
                $upload->set('thumb_height','60,240');
                $upload->set('thumb_ext',   '_small,_mid');
                $result = $upload->upfile('goods_image', true);
                if ($result){
                    $indeximg_succ = true;
                    $goods_array['pgoods_image'] = $upload->file_name;
                }else {
                    showDialog($upload->error,'','error');
                }
            }
            $state = $model_pointprod->editPointProd($goods_array,array('pgoods_id'=>$prod_info['pgoods_id']));
            if($state){
                //礼品代表图片数据入库
                if ($indeximg_succ){
                    //删除原有图片
                    $upload_list = $upload_model->getUploadList(array('upload_type'=>5,'item_id'=>$prod_info['pgoods_id']));

                    if (is_array($upload_list) && count($upload_list)>0){
                        $upload_idarr = array();
                        $paths = [];
                        foreach ($upload_list as $v){
                            $paths[] = ATTACH_POINTPROD.DS.$v['file_name'];
                            $paths[] = ATTACH_POINTPROD.DS.$v['file_thumb'];
                            $upload_idarr[] = $v['upload_id'];
                        }
                        //删除图片
                        $upload_model->dropUploadById($upload_idarr);
                        DeleteFileQueue::dispatch($paths);
                    }
                    $insert_array = array();
                    $insert_array['file_name'] = $upload->file_name;
                    $insert_array['file_thumb'] = $upload->thumb_image;
                    $insert_array['upload_type'] = 5;
                    $insert_array['file_size'] = intval($_FILES[trim($_POST['goods_image'])]['size']);
                    $insert_array['item_id'] = $prod_info['pgoods_id'];
                    $insert_array['upload_time'] = time();
                    $upload_model->add($insert_array);
                }
                //更新积分礼品描述图片
                $file_idstr = '';
                if (is_array($_POST['file_id']) && count($_POST['file_id'])>0){
                    $file_idstr = "'".implode("','",$_POST['file_id'])."'";
                }
                $upload_model->updatebywhere(array('item_id'=>$prod_info['pgoods_id']),array('upload_type'=>6,'item_id'=>'0','upload_id_in'=>"{$file_idstr}"));

                $this->log(L('nc_edit,admin_pointprodp').'['.$_POST['goodsname'].']');
                showDialog(L('admin_pointprod_edit_success'),'index.php?act=pointprod&op=pointprod','succ');
            }
        }else {
            $where = array();
            $where['upload_type'] = '6';
            $where['item_id'] = $prod_info['pgoods_id'];
            $file_upload = $upload_model->getUploadList($where);
            if (is_array($file_upload)){
                foreach ($file_upload as $k => $v){
                    $file_upload[$k]['upload_path'] = pointprodThumb($file_upload[$k]['file_name']);
                }
            }
            //会员级别
            $member_grade = Model('member')->getMemberGradeArr();
            Tpl::output('member_grade',$member_grade);
            Tpl::output('file_upload',$file_upload);
            Tpl::output('PHPSESSID',session_id());
            Tpl::output('hourarr',$hourarr);
            Tpl::output('prod_info',$prod_info);
            Tpl::showpage('pointprod.edit');
        }
    }

    /**
     * 删除积分礼品
     */
    public function prod_dropOp(){
        $pg_id = intval($_GET['pg_id']);
        if (!$pg_id){
            showDialog(L('admin_pointprod_parameter_error'),'index.php?act=pointprod&op=pointprod','error');
        }
        $model_pointprod = Model('pointprod');
        //查询礼品是否存在
        $prod_info = $model_pointprod->getPointProdInfo(array('pgoods_id'=>$pg_id));
        if (!is_array($prod_info) || count($prod_info)<=0){
            showDialog(L('admin_pointprod_record_error'),'index.php?act=pointprod&op=pointprod','error');
        }
        //查询积分礼品的下属信息（比如兑换信息）
        //删除操作
        $result = $model_pointprod->delPointProdById($pg_id);
        if($result) {
            $this->log(L('nc_del,admin_pointprodp').'[ID:'.$pg_id.']');
            $this->jsonOutput();
        } else {
            $this->jsonOutput('操作失败');
        }
    }

    /**
     * 批量删除积分礼品
     */
    public function prod_dropallOp()
    {
        $pg_id = array();
        foreach (explode(',', (string) $_REQUEST['pg_id']) as $i) {
            $pg_id[(int) $i] = null;
        }
        unset($pg_id[0]);
        $pg_id = array_keys($pg_id);

        if (!$pg_id){
            showDialog(L('admin_pointprod_parameter_error'),'index.php?act=pointprod&op=pointprod','','error');
        }
        $result = Model('pointprod')->delPointProdById($pg_id);
        if($result) {
            $this->log(L('nc_del,admin_pointprodp').'[ID:'.implode(',',$pg_id).']');
            $this->jsonOutput();
        } else {
            $this->jsonOutput('操作失败');
        }
    }

    /**
     * 积分礼品异步状态修改
     */
    public function ajaxOp()
    {
        //礼品上架,礼品推荐,礼品禁售
        $id = intval($_GET['id']);
        if ($id <= 0){
            echo 'false'; exit;
        }
        $model_pointprod = Model('pointprod');
        $update_array = array();
        $update_array[$_GET['column']] = trim($_GET['value']);
        $model_pointprod->editPointProd($update_array,array('pgoods_id'=>$id));
        echo 'true';exit;
    }
    /**
     * 积分礼品上传
     */
    public function pointprod_pic_uploadOp(){
        /**
         * 上传图片
         */
        $upload = new UploadFile();
        $upload->set('default_dir',ATTACH_POINTPROD);

        $result = $upload->upfile('fileupload',true);
        if ($result){
            $_POST['pic'] = $upload->file_name;
        }else {
            echo 'error';exit;
        }
        /**
         * 模型实例化
         */
        $model_upload = Model('upload');
        /**
         * 图片数据入库
        */
        $insert_array = array();
        $insert_array['file_name'] = $_POST['pic'];
        $insert_array['upload_type'] = '6';
        $insert_array['file_size'] = $_FILES['fileupload']['size'];
        $insert_array['upload_time'] = time();
        $insert_array['item_id'] = intval($_POST['item_id']);
        $result = $model_upload->add($insert_array);
        if ($result){
            $data = array();
            $data['file_id'] = $result;
            $data['file_name'] = $_POST['pic'];
            $data['file_path'] = $_POST['pic'];
            $data['url'] = pointprodThumb($_POST['pic']);
            /**
             * 整理为json格式
             */
            $output = json_encode($data);
            echo $output;
        }
    }
    /**
     * ajax操作删除已上传图片
     */
    public function ajaxdeluploadOp(){
        //删除文章图片
        if (intval($_GET['file_id']) > 0){
            $model_upload = Model('upload');
            /**
             * 删除图片
             */
            $file_array = $model_upload->getOneUpload(intval($_GET['file_id']));
            /**
             * 删除信息
             */
            $model_upload->del(intval($_GET['file_id']));
            DeleteFileQueue::dispatch(ATTACH_POINTPROD.DS.$file_array['file_name']);
            echo 'true';exit;
        }else {
            echo 'false';exit;
        }
    }

    /**
     * 积分兑换列表
     */
    public function pointorder_listOp()
    {
        $states = Model('pointorder')->getPointOrderStateBySign();
        Tpl::output('states', $states);

        Tpl::showpage('pointorder.list');
    }

    /**
     * 服务套餐积分兑换列表
     */
    public function pointvrorder_listOp()
    {
        /*$states = Model('pointorder')->getPointOrderStateBySign();
        Tpl::output('states', $states);*/

        Tpl::showpage('pointvrorder.list');
    }

    /**
     * 积分兑换列表XML
     */
    public function pointorder_list_xmlOp()
    {
        $condition = array();

        if ($_REQUEST['advanced']) {
            if (strlen($q = trim((string) $_REQUEST['point_ordersn']))) {
                $condition['point_ordersn'] = array('like', '%' . $q . '%');
            }
            if (strlen($q = trim((string)$_REQUEST['point_buyername']))) {
                $condition['point_buyername'] = $q;
            }
            if (strlen($q = trim((string)$_REQUEST['point_orderstate']))) {
                $condition['point_orderstate'] = (int)$q;
            }
            if (strlen($q = trim((string)$_REQUEST['point_type']))) {
                $condition['point_type'] = (int)$q;
            }
            if (strlen($q = trim((string)$_REQUEST['point_gtype']))) {
                $condition['point_gtype'] = (int)$q;
            }
            $if_start_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/', $_GET['query_start_date']);
            $if_end_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/', $_GET['query_end_date']);
            $start_unixtime = $if_start_date ? strtotime($_GET['query_start_date']) : null;
            $end_unixtime = $if_end_date ? strtotime($_GET['query_end_date']) : null;
            $end_unixtime = $if_end_date ? $end_unixtime + 86400 - 1 : null;
            if ($if_start_date || $if_end_date) {
                $condition['point_addtime'] = array('between', "{$start_unixtime},{$end_unixtime}");
            } else {
                //$condition['finnshed_time'] = array('between',"{$bill_info['ob_start_date']},{$bill_info['ob_end_date']}");
            }
        } else {
            if (strlen($q = trim($_REQUEST['query']))) {
                switch ($_REQUEST['qtype']) {
                    case 'point_ordersn':
                        $condition['point_ordersn'] = array('like', '%' . $q . '%');
                        break;
                    case 'point_buyername':
                        $condition['point_buyername'] = $q;
                        break;
                }
            }
        }
        $condition['point_goodstype'] = array('eq',1);
        $model_pointorder = Model('pointorder');
        $list = (array) $model_pointorder->getPointOrderList($condition, '*', $_REQUEST['rp'], 0, 'point_orderid desc');

        $data = array();
        $data['now_page'] = $model_pointorder->shownowpage();
        $data['total_num'] = $model_pointorder->gettotalnum();

        foreach ($list as $val) {
            $orderGoodsInfo = $model_pointorder->getPointOrderGoodsInfo(array('point_orderid' => $val['point_orderid']),"point_goodsname");
            if ($val['point_goodstype'] == 2) {
                $o = '<a class="btn green" href="' . urlAdminShop('pointprod', 'vrorder_info', array(
                        'order_id' => $val['point_orderid'],
                    )) . '"><i class="fa fa-list-alt"></i>查看</a>';
            }else {
                $o = '<a class="btn green" href="' . urlAdminShop('pointprod', 'order_info', array(
                        'order_id' => $val['point_orderid'],
                    )) . '"><i class="fa fa-list-alt"></i>查看</a>';
            }

            $o .= "<a class='btn green' href='javascript:void(0);' onclick='ajax_form(\"login\",\"ERP积分订单\",\"". urlAdminShop('pointprod', 'erp_order', array('order_id' => $val['point_orderid'])) ."\",640)'>查看ERP订单</a></li>";
            if (
                $val['point_orderallowship']
                || $val['point_orderalloweditship']
                || $val['point_orderallowcancel']
                || $val['point_orderallowdelete']
            ) {
                $o .= '<span class="btn"><em><i class="fa fa-cog"></i>设置<i class="arrow"></i></em><ul>';

                if ($val['point_orderallowship']) {
                    // 发货（已确认付款，待发货）
                    $o .= '<li><a href="' . urlAdminShop('pointprod', 'order_ship', array(
                        'id' => $val['point_orderid'],
                    )) . '">设置发货</a></li>';
                }

                if ($val['point_orderalloweditship']) {
                    // 修改物流（已发货，待收货）
                    $o .= '<li><a href="' . urlAdminShop('pointprod', 'order_ship', array(
                        'id' => $val['point_orderid'],
                    )) . '">修改物流</a></li>';
                }

                if ($val['point_orderallowcancel']) {
                    // 取消订单（未发货）
                    $o .= '<li><a class="confirm-on-click" href="' . urlAdminShop('pointprod', 'order_cancel', array(
                        'id' => $val['point_orderid'],
                    )) . '">取消订单</a></li>';
                }

                if ($val['point_orderallowdelete']) {
                    // 删除订单
                    $o .= '<li><a class="confirm-on-click" href="' . urlAdminShop('pointprod', 'order_drop', array(
                        'order_id' => $val['point_orderid'],
                    )) . '">删除订单</a></li>';
                }

                $o .= '</ul></span>';
            }

            $i = array();
            $i['operation'] = $o;
            $erp = "<a class='btn green' href='javascript:void(0);' onclick='ajax_form(\"login\",\"ERP系统积分\",\"". urlAdminShop('pointprod', 'get_erp_points', array('order_id' => $val['point_orderid'])) ."\",640)'>查询用户ERP积分</a>";
            $i['point_ordersn'] = $val['point_ordersn'];
            $i['point_buyername'] = $val['point_buyername'];
            $i['point_allpoint'] = $val['point_allpoint'];
            $orderstatetext = $val['point_orderstatetext'];
            if($val['point_goodstype'] == 2) {
                $i['point_goodstype'] = "服务套餐";
                $orderstatetext = $this->getPointStatus($val['point_status']);
            }elseif ($val['point_goodstype'] == 3) {
                $i['point_goodstype'] = "优惠券";
                $orderstatetext = $this->getPointStatus($val['point_status']);
            }else {
                $i['point_goodstype'] = "实物商品";
            }
            $i['point_goodsname'] = $orderGoodsInfo['point_goodsname'];
            $i['point_addtime_text'] = date('Y-m-d H:i:s', $val['point_addtime']);
            $i['point_orderstatetext'] = $orderstatetext;
            $i['erp_points'] = $erp;

            $data['list'][$val['point_orderid']] = $i;
        }

        echo Tpl::flexigridXML($data);
        exit;
    }

    /**
     * 虚拟积分兑换列表XML
     */
    public function point_vrorder_list_xmlOp()
    {
        $condition = array();

        if ($_REQUEST['advanced']) {
            if (strlen($q = trim((string) $_REQUEST['point_ordersn']))) {
                $condition['point_ordersn'] = array('like', '%' . $q . '%');
            }
            if (strlen($q = trim((string) $_REQUEST['point_buyername']))) {
                $condition['point_buyername'] = $q;
            }
            if (strlen($q = trim((string) $_REQUEST['point_status']))) {
                $condition['point_status'] = (int) $q;
            }
            if (strlen($q = trim((string) $_REQUEST['point_type']))) {
                $condition['point_type'] = (int) $q;
            }
            $if_start_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_start_date']);
            $if_end_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_end_date']);
            $start_unixtime = $if_start_date ? strtotime($_GET['query_start_date']) : null;
            $end_unixtime = $if_end_date ? strtotime($_GET['query_end_date']) : null;
            $end_unixtime = $if_end_date ? $end_unixtime+86400-1 : null;
            if ($if_start_date || $if_end_date) {
                $condition['point_addtime'] = array('between',"{$start_unixtime},{$end_unixtime}");
            } else {
                //$condition['finnshed_time'] = array('between',"{$bill_info['ob_start_date']},{$bill_info['ob_end_date']}");
            }
        } else {
            if (strlen($q = trim($_REQUEST['query']))) {
                switch ($_REQUEST['qtype']) {
                    case 'point_ordersn':
                        $condition['point_ordersn'] = array('like', '%' . $q . '%');
                        break;
                    case 'point_buyername':
                        $condition['point_buyername'] = $q;
                        break;
                }
            }
        }
        $condition['point_goodstype'] = array('gt',1);
        $model_pointorder = Model('pointorder');
        $list = (array) $model_pointorder->getPointOrderList($condition, '*', $_REQUEST['rp'], 0, 'point_orderid desc');

        $data = array();
        $data['now_page'] = $model_pointorder->shownowpage();
        $data['total_num'] = $model_pointorder->gettotalnum();

        foreach ($list as $val) {
            $orderGoodsInfo = $model_pointorder->getPointOrderGoodsInfo(array('point_orderid' => $val['point_orderid']),"point_goodsname");
            if ($val['point_goodstype'] == 2) {
                $o = '<a class="btn green" href="' . urlAdminShop('pointprod', 'vrorder_info', array(
                        'order_id' => $val['point_orderid'],
                    )) . '"><i class="fa fa-list-alt"></i>查看</a>';
            }else {
                $o = '<a class="btn green" href="' . urlAdminShop('pointprod', 'order_info', array(
                        'order_id' => $val['point_orderid'],
                    )) . '"><i class="fa fa-list-alt"></i>查看</a>';
            }

            $o .= "<a class='btn green' href='javascript:void(0);' onclick='ajax_form(\"login\",\"ERP积分订单\",\"". urlAdminShop('pointprod', 'erp_order', array('order_id' => $val['point_orderid'])) ."\",640)'>查看ERP订单</a></li>";
            if (
                $val['point_orderallowship']
                || $val['point_orderalloweditship']
                || $val['point_orderallowcancel']
                || $val['point_orderallowdelete']
            ) {
                /*$o .= '<span class="btn"><em><i class="fa fa-cog"></i>设置<i class="arrow"></i></em><ul>';

                if ($val['point_orderallowship']) {
                    // 发货（已确认付款，待发货）
                    $o .= '<li><a href="' . urlAdminShop('pointprod', 'order_ship', array(
                            'id' => $val['point_orderid'],
                        )) . '">设置发货</a></li>';
                }

                if ($val['point_orderalloweditship']) {
                    // 修改物流（已发货，待收货）
                    $o .= '<li><a href="' . urlAdminShop('pointprod', 'order_ship', array(
                            'id' => $val['point_orderid'],
                        )) . '">修改物流</a></li>';
                }

                if ($val['point_orderallowcancel']) {
                    // 取消订单（未发货）
                    $o .= '<li><a class="confirm-on-click" href="' . urlAdminShop('pointprod', 'order_cancel', array(
                            'id' => $val['point_orderid'],
                        )) . '">取消订单</a></li>';
                }

                if ($val['point_orderallowdelete']) {
                    // 删除订单
                    $o .= '<li><a class="confirm-on-click" href="' . urlAdminShop('pointprod', 'order_drop', array(
                            'order_id' => $val['point_orderid'],
                        )) . '">删除订单</a></li>';
                }

                $o .= '</ul></span>';*/
            }

            $i = array();
            $i['operation'] = $o;
            $erp = "<a class='btn green' href='javascript:void(0);' onclick='ajax_form(\"login\",\"ERP系统积分\",\"". urlAdminShop('pointprod', 'get_erp_points', array('order_id' => $val['point_orderid'])) ."\",640)'>查询用户ERP积分</a>";
            $i['point_ordersn'] = $val['point_ordersn'];
            $i['point_buyername'] = $val['point_buyername'];
            $i['point_allpoint'] = $val['point_allpoint'];
            $orderstatetext = $val['point_orderstatetext'];
            if($val['point_goodstype'] == 2) {
                $i['point_goodstype'] = "服务套餐";
                $orderstatetext = $this->getPointStatus($val['point_status']);
            }elseif ($val['point_goodstype'] == 3) {
                $i['point_goodstype'] = "优惠券";
                $orderstatetext = $this->getPointStatus($val['point_status']);
            }else {
                $i['point_goodstype'] = "实物商品";
            }
            $i['point_goodsname'] = $orderGoodsInfo['point_goodsname'];
            $i['point_addtime_text'] = date('Y-m-d H:i:s', $val['point_addtime']);
            $i['point_orderstatetext'] = $orderstatetext;
            $i['erp_points'] = $erp;

            $data['list'][$val['point_orderid']] = $i;
        }

        echo Tpl::flexigridXML($data);
        exit;
    }

    private function getPointStatus($status) {
        switch ($status) {
            case 2:
                $str = "已使用";
                break;
            case 3:
                $str = "已过期";
                break;
            default:
                $str = "未使用";
                break;
        }
        return $str;
    }

    /**
     * 删除兑换订单信息
     */
    public function order_dropOp(){
        $data = Model('pointorder')->delPointOrderByOrderID($_GET['order_id']);
        if ($data['state']){
            showDialog(L('admin_pointorder_del_success'),'index.php?act=pointprod&op=pointorder_list','succ');
        } else {
            showDialog($data['msg'],'index.php?act=pointprod&op=pointorder_list','error');
        }
    }

    /**
     * 取消兑换
     */
    public function order_cancelOp(){
        $model_pointorder = Model('pointorder');
        //取消订单
        $data = $model_pointorder->cancelPointOrder($_GET['id']);
        if ($data['state']){
            showDialog(L('admin_pointorder_cancel_success'),'index.php?act=pointprod&op=pointorder_list','succ');
        }else {
            showDialog($data['msg'],'index.php?act=pointprod&op=pointorder_list','error');
        }
    }

    /**
     * 发货
     */
    public function order_shipOp(){
        $order_id = intval($_GET['id']);
        if ($order_id <= 0){
            showDialog(L('admin_pointorder_parameter_error'),'index.php?act=pointprod&op=pointorder_list','error');
        }
        $model_pointorder = Model('pointorder');
        //获取订单状态
        $pointorderstate_arr = $model_pointorder->getPointOrderStateBySign();

        //查询订单信息
        $where = array();
        $where['point_orderid'] = $order_id;
        $where['point_orderstate'] = array('in',array($pointorderstate_arr['waitship'][0],$pointorderstate_arr['waitreceiving'][0]));//待发货和已经发货状态
        $order_info = $model_pointorder->getPointOrderInfo($where);
        if (!$order_info){
            showDialog(L('admin_pointorderd_record_error'),'index.php?act=pointprod&op=pointorder_list','error');
        }
        if (chksubmit()){
            $obj_validate = new Validate();
            $validate_arr[] = array("input"=>$_POST["shippingcode"],"require"=>"true","message"=>L('admin_pointorder_ship_code_nullerror'));
            $obj_validate->validateparam = $validate_arr;
            $error = $obj_validate->validate();
            if ($error != ''){
                showDialog(L('error').$error,'index.php?act=pointprod&op=pointorder_list','error');
            }
            //发货
            $data = $model_pointorder->shippingPointOrder($order_id, $_POST, $order_info);
            if ($data['state']){
                //更新ERP 物流状态
                $result = Logic('erp_points_order')->updateExpressState($order_id);
                if ($result) {
                    showDialog('发货修改成功','index.php?act=pointprod&op=pointorder_list','succ');
                } else {
                    $secResult = Logic('erp_points_order')->updateExpressState($order_id);
                    if($secResult) {
                        showDialog('发货修改成功','index.php?act=pointprod&op=pointorder_list','succ');
                    } else {// todo

                    }
                }
            }else {
                showDialog($data['msg'],'index.php?act=pointprod&op=pointorder_list','error');
            }
        } else {
            $express_list = Model('express')->getExpressList();
            Tpl::output('express_list',$express_list);
            Tpl::output('order_info',$order_info);
            Tpl::showpage('pointorder.ship');
        }
    }
    /**
     * 兑换信息详细
     */
    public function order_infoOp(){
        $order_id = intval($_GET['order_id']);
        if ($order_id <= 0){
            showDialog(L('admin_pointorder_parameter_error'),'index.php?act=pointprod&op=pointorder_list','error');
        }
        //查询订单信息
        $model_pointorder = Model('pointorder');
        $order_info = $model_pointorder->getPointOrderInfo(array('point_orderid'=>$order_id));
        if (!$order_info){
            showDialog(L('admin_pointorderd_record_error'),'index.php?act=pointprod&op=pointorder_list','error');
        }
        $orderstate_arr = $model_pointorder->getPointOrderState($order_info['point_orderstate']);
        $order_info['point_orderstatetext'] = $orderstate_arr[1];

        //查询兑换订单收货人地址
        $orderaddress_info = $model_pointorder->getPointOrderAddressInfo(array('point_orderid'=>$order_id));
        Tpl::output('orderaddress_info',$orderaddress_info);

        //兑换商品信息
        $prod_list = $model_pointorder->getPointOrderGoodsList(array('point_orderid'=>$order_id));
        Tpl::output('prod_list',$prod_list);

        //物流公司信息
        if ($order_info['point_shipping_ecode'] != ''){
            $data = Model('express')->getExpressInfoByECode($order_info['point_shipping_ecode']);
            if ($data['state']){
                $express_info = $data['data']['express_info'];
            }
            Tpl::output('express_info',$express_info);
        }
        if ($order_info['point_goodstype'] == 3) {
            $order_info['point_orderstatetext'] = $this->getPointStatus($order_info['point_status']);
        }

        Tpl::output('order_info',$order_info);
        Tpl::showpage('pointorder.info');
    }
    /**
     * 兑换信息详细
     */
    public function vrorder_infoOp(){
        $order_id = intval($_GET['order_id']);
        if ($order_id <= 0){
            showDialog(L('admin_pointorder_parameter_error'),'index.php?act=pointprod&op=pointorder_list','error');
        }
        //查询订单信息
        $model_pointorder = Model('pointorder');
        $order_info = $model_pointorder->getPointOrderInfo(array('point_orderid'=>$order_id));
        if (!$order_info){
            showDialog(L('admin_pointorderd_record_error'),'index.php?act=pointprod&op=pointorder_list','error');
        }
        //兑换商品信息
        $prod_list = $model_pointorder->getPointOrderGoodsList(array('point_orderid'=>$order_id));
        Tpl::output('prod_list',$prod_list);
        if (intval($order_info['point_vrorder_id']) > 0) {
            Tpl::output('order_info',$order_info);
            $this->show_vrorder($order_info['point_vrorder_id']);
        }else {
            $orderstate_arr = $model_pointorder->getPointOrderState($order_info['point_orderstate']);
            $order_info['point_orderstatetext'] = $orderstate_arr[1];

            //查询兑换订单收货人地址
            $orderaddress_info = $model_pointorder->getPointOrderAddressInfo(array('point_orderid'=>$order_id));
            Tpl::output('orderaddress_info',$orderaddress_info);

            //物流公司信息
            if ($order_info['point_shipping_ecode'] != ''){
                $data = Model('express')->getExpressInfoByECode($order_info['point_shipping_ecode']);
                if ($data['state']){
                    $express_info = $data['data']['express_info'];
                }
                Tpl::output('express_info',$express_info);
            }

            Tpl::output('order_info',$order_info);
            Tpl::showpage('pointorder.info');
        }
    }
    /**
     * 查看虚拟商品订单详情
     *
     */
    public function show_vrorder($order_id){
        $model_vr_order = Model('vr_order');
        $order_info = $model_vr_order->getOrderInfo(array('order_id'=>$order_id));
        if (empty($order_info)) {
            showMessage('订单不存在','','html','error');
        }

        //取兑换码列表
        $vr_code_list = $model_vr_order->getOrderCodeList(array('order_id' => $order_info['order_id']));
        $order_info['extend_vr_order_code'] = $vr_code_list;

        //显示取消订单
        $order_info['if_cancel'] = $model_vr_order->getOrderOperateState('buyer_cancel',$order_info);

        //显示订单进行步骤
        $order_info['step_list'] = $model_vr_order->getOrderStep($order_info);

        //显示系统自动取消订单日期
        if ($order_info['order_state'] == ORDER_STATE_NEW) {
            $order_info['order_cancel_day'] = $order_info['add_time'] + ORDER_AUTO_CANCEL_TIME * 60;
        }
        //显示门店信息
        if ($order_info['chain_id']) {
            $chain_model = Model('chain');
            $chain_info = $chain_model->getChainInfo(array('chain_id' => $order_info['chain_id']),"chain_name");
            $order_info['chain_name'] = $chain_info['chain_name'];
        }

        //退款信息
        $refund_list = Model('vr_refund')->getRefundList(array('order_id'=>$order_info['order_id']));
        Tpl::output('refund_list',$refund_list);

        //商家信息
        $store_info = Model('store')->getStoreInfo(array('store_id'=>$order_info['store_id']));
        Tpl::output('store_info',$store_info);

        Tpl::output('vrorder_info',$order_info);
        Tpl::showpage('pointvrorder.info');
    }
    /**
     * ERP订单信息
     */
    public function erp_orderOp() {
        $order_id = intval($_GET['order_id']);
        if ($order_id <= 0){
            showDialog(L('admin_pointorder_parameter_error'),'index.php?act=pointprod&op=pointorder_list','error');
        }

        //查询订单信息
        $model_pointorder = Model('pointorder');
        $erp_info = $model_pointorder->getPointOrderInfo(array('point_orderid'=>$order_id),'erp_order_id');
        if (!$erp_info){
            showDialog(L('admin_pointorderd_record_error'),'index.php?act=pointprod&op=pointorder_list','error');
        }
        /*$orderstate_arr = $model_pointorder->getPointOrderState($order_info['point_orderstate']);
        $order_info['point_orderstatetext'] = $orderstate_arr[1]; */

        $order_info = Logic('erp_points_order')->orderget($erp_info['erp_order_id']);
        Tpl::output('erp_order', $order_info);
        Tpl::showpage('erp_order.info', 'null_layout');
    }
    /**
     * 查看ERP积分
     */
    public function get_erp_pointsOp() {
        $order_id = intval($_GET['order_id']);
        if ($order_id <= 0){
            showDialog(L('admin_pointorder_parameter_error'),'index.php?act=pointprod&op=pointorder_list','error');
        }
        //查询订单信息
        $model_pointorder = Model('pointorder');
        $pointorder_info = $model_pointorder->getPointOrderInfo(array('point_orderid'=>$order_id),'point_buyerid');
        if (!$pointorder_info){
            showDialog(L('admin_pointorderd_record_error'),'index.php?act=pointprod&op=pointorder_list','error');
        }
        $memberInfo = Model('member')->getMemberInfo(array('member_id' => $pointorder_info['point_buyerid']),'member_mobile');
        $member_mobile = $memberInfo['member_mobile'];
        $erpoints = Logic('erp_points')->getUserPoints($member_mobile);
        Tpl::output('erp_point', $erpoints);
        Tpl::showpage('erp_point.info', 'null_layout');
    }
    /**
     * 导出 积分订单
     */
    public function export_pointorderOp() {
        $order_id = intval($_GET['order_id']);
        /*if ($order_id <= 0) {
            exit();
        }*/
        $model_order = Model('pointorder');
        $condition = array();
        $if_start_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_start_date']);
        $if_end_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_end_date']);
        $start_unixtime = $if_start_date ? strtotime($_GET['query_start_date']) : null;
        $end_unixtime = $if_end_date ? strtotime($_GET['query_end_date']) : null;
        $end_unixtime = $if_end_date ? $end_unixtime+86400-1 : null;
        if ($if_start_date || $if_end_date) {
            $condition['point_addtime'] = array('between',"{$start_unixtime},{$end_unixtime}");
        } else {
            //$condition['finnshed_time'] = array('between',"{$bill_info['ob_start_date']},{$bill_info['ob_end_date']}");
        }
        if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
            $_GET['order_id'] = explode(',',trim($_GET['order_id'],','));
            $condition['point_orderid'] = array('in',$_GET['order_id']);
        }
        if ($_REQUEST['query'] != '' && in_array($_REQUEST['qtype'],array('point_ordersn','point_buyername'))) {
            $condition[$_REQUEST['qtype']] = array('like',"%{$_REQUEST['query']}%");
        }
        if ($_GET['point_ordersn'] != ''){
            $condition['point_ordersn'] = array('like',"%{$_GET['point_ordersn']}%");
        }
        if ($_GET['point_orderstate'] != ''){
            $condition['point_orderstate'] = array('eq',intval($_GET['point_orderstate']));
        }
        if ($_GET['point_buyername'] != '') {
            if ($_GET['jq_query']) {
                $condition['point_buyername'] = $_GET['point_buyername'];
            } else {
                $condition['point_buyername'] = array('like', "%{$_GET['point_buyername']}%");
            }
        }
        if ($_GET['point_type'] != '') {
            $condition['point_type'] = array('eq', intval($_GET['point_type']));
        }
        if ($_GET['point_gtype'] != '') {
            $condition['point_gtype'] = array('eq', intval($_GET['point_gtype']));
        }
        $sort_fields = array('point_ordersn', 'point_allpoint', 'point_buyername', 'point_addtime', 'point_orderstate', 'point_shippingcode', 'point_finnshedtime');
        if (in_array($_GET['sortorder'], array('asc', 'desc')) && in_array($_GET['sortname'], $sort_fields)) {
            $order = $_GET['sortname'] . ' ' . $_GET['sortorder'];
        }
        $condition['point_goodstype'] = array('eq', 1);
        if (!is_numeric($_GET['curpage'])) {
            $count = $model_order->getPointOrderCount($condition);
            $array = array();
            if ($count > self::EXPORT_SIZE) {
                //显示下载链接
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=pointprod&op=pointorder_list&order_id='.$order_id);
                Tpl::showpage('export.excel');
                exit();
            }
            $limit = false;
        }else{
            //下载
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $limit = "{$limit1},{$limit2}";
        }
        $field = "points_order.*,points_orderaddress.point_truename,points_orderaddress.point_areainfo,points_orderaddress.point_address,points_orderaddress.point_mobphone";
        //$data = $model_order->getPointOrderList($condition,$field,'',$limit,$order);
        $data = $model_order->getExportPointOrderList($condition,$field,'',$limit,$order);

        /*//订单商品表查询条件
        $order_id_array = array();
        if (is_array($data)) {
            foreach ($data as $order_info) {
                $order_id_array[] = $order_info['order_id'];
            }
        }
        $order_goods_condition = array();
        $order_goods_condition['order_id'] = array('in',$order_id_array);*/
        Language::read('export');
        import('libraries.excel');
        $excel_obj = new Excel();
        $excel_data = array();
        //$model_goods = Model('goods');
        //设置样式
        $excel_obj->setStyle(array('id'=>'s_title','Font'=>array('FontName'=>'宋体','Size'=>'12','Bold'=>'1')));
        //header
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单编号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'会员名称');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'兑换积分');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'兑换时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'状态');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'物流单号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'物流编码');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'发货时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'完成时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'来源');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单留言');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'收货人');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'联系电话');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'会员手机');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'收货地址');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'商品名称');
        //data
        $point_totals = 0;
        foreach ((array)$data as $k=>$order_info){
            $goodsList = $model_order->getPointOrderGoodsList(array('point_orderid'=>$order_info['point_orderid']),"point_goodsname,point_goodsimage,point_goodspoints,point_goodsnum");
            $list = array();
            $list['point_ordersn'] = $order_info['point_ordersn'];
            $list['point_buyername'] = $order_info['point_buyername'];
            $list['point_allpoint'] = $order_info['point_allpoint'];
            $point_totals += $order_info['point_allpoint'];
            $list['point_addtime'] = date('Y-m-d H:i:s',$order_info['point_addtime']);
            $list['point_orderstatetext'] = $order_info['point_orderstatetext'];
            $list['point_shippingcode'] = $order_info['point_shippingcode'];
            $list['point_shipping_ecode'] = $order_info['point_shipping_ecode'];
            $list['point_shippingtime'] = $order_info['point_shippingtime'] > 0 ? date('Y-m-d H:i:s',$order_info['point_shippingtime']):'';
            $list['point_finnshedtime'] = $order_info['point_finnshedtime'] > 0 ? date('Y-m-d H:i:s',$order_info['point_finnshedtime']):'';
            $list['point_type'] = str_replace(array(0,1), array('电商','宠医云'),$order_info['point_type']);
            $list['point_ordermessage'] = $order_info['point_ordermessage'];
            $goods_string = '';
            if (is_array($goodsList)) {
                foreach ($goodsList as $vv) {
                    $goods_string .= $vv['point_goodsname'].'|积分:'.$vv['point_goodspoints']."|数量：".$vv['point_goodsnum']." ";
                }
            }
            $list['point_truename'] = $order_info['point_truename'];
            $list['point_mobphone'] = $order_info['point_mobphone'];
            $list['erp_mobile'] = $order_info['erp_mobile'];
            $list['address'] = str_replace(array("\r\n", "\r", "\n"), "", $order_info['point_areainfo']).str_replace(array("\r\n", "\r", "\n"), "", $order_info['point_address']);;
            $list['goods_info'] = $goods_string;;
            $tmp = array();
            $tmp[] = array('data'=>$list['point_ordersn']);
            $tmp[] = array('data'=>$list['point_buyername']);
            $tmp[] = array('data'=>$list['point_allpoint']);
            $tmp[] = array('data'=>$list['point_addtime']);
            $tmp[] = array('data'=>$list['point_orderstatetext']);
            $tmp[] = array('data'=>$list['point_shippingcode']);
            $tmp[] = array('data'=>$list['point_shipping_ecode']);
            $tmp[] = array('data'=>$list['point_shippingtime']);
            $tmp[] = array('data'=>$list['point_finnshedtime']);
            $tmp[] = array('data'=>$list['point_type']);
            $tmp[] = array('data'=>$list['point_ordermessage']);
            $tmp[] = array('data'=>$list['point_truename']);
            $tmp[] = array('data'=>$list['point_mobphone']);
            $tmp[] = array('data'=>$list['erp_mobile']);
            $tmp[] = array('data'=>$list['address']);
            $tmp[] = array('data'=>$list['goods_info']);
            $excel_data[] = $tmp;
        }
        $count = count($excel_data);
        $excel_data[$count][] = array('data'=>'总共积分');
        $excel_data[$count][] = array('data'=>$point_totals);
        $excel_data[$count][] = array('data'=>'订单数量');
        $excel_data[$count][] = array('data'=>$count-1);
        $excel_data = $excel_obj->charset($excel_data,CHARSET);
        $excel_obj->addArray($excel_data);
        $excel_obj->addWorksheet($excel_obj->charset(L('exp_od_order'),CHARSET));
        $excel_obj->generateXML('points_order-'.$_GET['curpage'].'-'.date('Y-m-d-H',time()));
    }

    public function export_point_vrorderOp() {
        $order_id = intval($_GET['order_id']);
        /*if ($order_id <= 0) {
            exit();
        }*/
        $model_order = Model('pointorder');
        $condition = array();
        $if_start_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_start_date']);
        $if_end_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_end_date']);
        $start_unixtime = $if_start_date ? strtotime($_GET['query_start_date']) : null;
        $end_unixtime = $if_end_date ? strtotime($_GET['query_end_date']) : null;
        $end_unixtime = $if_end_date ? $end_unixtime+86400-1 : null;
        if ($if_start_date || $if_end_date) {
            $condition['point_addtime'] = array('between',"{$start_unixtime},{$end_unixtime}");
        } else {
            //$condition['finnshed_time'] = array('between',"{$bill_info['ob_start_date']},{$bill_info['ob_end_date']}");
        }
        if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
            $_GET['order_id'] = explode(',',trim($_GET['order_id'],','));
            $condition['point_orderid'] = array('in',$_GET['order_id']);
        }
        if ($_REQUEST['query'] != '' && in_array($_REQUEST['qtype'],array('point_ordersn','point_buyername'))) {
            $condition[$_REQUEST['qtype']] = array('like',"%{$_REQUEST['query']}%");
        }
        if ($_GET['point_ordersn'] != ''){
            $condition['point_ordersn'] = array('like',"%{$_GET['point_ordersn']}%");
        }
        if ($_GET['point_orderstate'] != ''){
            $condition['point_orderstate'] = array('eq',intval($_GET['point_orderstate']));
        }
        if ($_GET['point_buyername'] != ''){
            if ($_GET['jq_query']) {
                $condition['point_buyername'] = $_GET['point_buyername'];
            } else {
                $condition['point_buyername'] = array('like',"%{$_GET['point_buyername']}%");
            }
        }
        if ($_GET['point_type'] != '') {
            $condition['point_type'] = array('eq',intval($_GET['point_type']));
        }

        $sort_fields = array('point_ordersn','point_allpoint','point_buyername','point_addtime','point_orderstate','point_shippingcode','point_finnshedtime');
        if (in_array($_GET['sortorder'],array('asc','desc')) && in_array($_GET['sortname'],$sort_fields)) {
            $order = $_GET['sortname'].' '.$_GET['sortorder'];
        }
        $condition['point_goodstype'] = array('gt',1);
        if (!is_numeric($_GET['curpage'])){
            $count = $model_order->getPointOrderCount($condition);
            $array = array();
            if ($count > self::EXPORT_SIZE ){
                //显示下载链接
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=pointprod&op=pointorder_list&order_id='.$order_id);
                Tpl::showpage('export.excel');
                exit();
            }
            $limit = false;
        }else{
            //下载
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $limit = "{$limit1},{$limit2}";
        }
        $field = "points_order.*,points_orderaddress.point_truename,points_orderaddress.point_areainfo,points_orderaddress.point_address,points_orderaddress.point_mobphone";
        //$data = $model_order->getPointOrderList($condition,$field,'',$limit,$order);
        $data = $model_order->getExportPointOrderList($condition,$field,'',$limit,$order);

        /*//订单商品表查询条件
        $order_id_array = array();
        if (is_array($data)) {
            foreach ($data as $order_info) {
                $order_id_array[] = $order_info['order_id'];
            }
        }
        $order_goods_condition = array();
        $order_goods_condition['order_id'] = array('in',$order_id_array);*/
        Language::read('export');
        import('libraries.excel');
        $excel_obj = new Excel();
        $excel_data = array();
        //$model_goods = Model('goods');
        //设置样式
        $excel_obj->setStyle(array('id'=>'s_title','Font'=>array('FontName'=>'宋体','Size'=>'12','Bold'=>'1')));
        //header
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单编号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'会员名称');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'兑换积分');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'兑换时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'状态');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'类型');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'优惠券码');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'过期时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'来源');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单留言');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'收货人');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'联系电话');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'会员手机');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'收货地址');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'商品名称');
        //data
        $point_totals = 0;
        foreach ((array)$data as $k=>$order_info){
            $goodsList = $model_order->getPointOrderGoodsList(array('point_orderid'=>$order_info['point_orderid']),"point_goodsname,point_goodsimage,point_goodspoints,point_goodsnum");
            $list = array();
            $list['point_ordersn'] = $order_info['point_ordersn'];
            $list['point_buyername'] = $order_info['point_buyername'];
            $list['point_allpoint'] = $order_info['point_allpoint'];
            $point_totals += $order_info['point_allpoint'];
            $list['point_addtime'] = date('Y-m-d H:i:s',$order_info['point_addtime']);
            if ($order_info['point_couponendtime'] < time() && $order_info['point_couponendtime'] > 0) {
                $list['point_orderstatetext'] = "已过期";
            }else{
                $list['point_orderstatetext'] = $this->getPointStatus($order_info['point_status']);
            }
            $list['point_goodstype'] = str_replace(array(1,2,3), array('实物订单','虚拟套餐','优惠券'),$order_info['point_goodstype']);
            if ($order_info['point_vrorder_id'] > 0) {
                $vrcode_orderinfo = Model('vr_order')->getOrderCodeInfo(array('order_id' => $order_info['point_vrorder_id']),"vr_code");
                $list['point_couponcode'] = $vrcode_orderinfo['vr_code'];
            }else{
                $list['point_couponcode'] = $order_info['point_couponcode'] > 0 ? $order_info['point_couponcode']:'';
            }
            $list['point_couponendtime'] = $order_info['point_couponendtime'] > 0 ? date('Y-m-d H:i:s',$order_info['point_couponendtime']):'';
            $list['point_type'] = str_replace(array(0,1,2), array('电商','宠医云','阿闻'),$order_info['point_type']);
            $list['point_ordermessage'] = $order_info['point_ordermessage'];
            $goods_string = '';
            if (is_array($goodsList)) {
                foreach ($goodsList as $vv) {
                    $goods_string .= $vv['point_goodsname'].' | 积分:'.$vv['point_goodspoints']." | 数量：".$vv['point_goodsnum']." ";
                }
            }
            $list['point_truename'] = $order_info['point_truename'];
            $list['point_mobphone'] = $order_info['point_mobphone'];
            $list['erp_mobile'] = $order_info['erp_mobile'];
            $list['address'] = str_replace(array("\r\n", "\r", "\n"), "", $order_info['point_areainfo']).str_replace(array("\r\n", "\r", "\n"), "", $order_info['point_address']);;
            $list['goods_info'] = $goods_string;;
            $tmp = array();
            $tmp[] = array('data'=>$list['point_ordersn']);
            $tmp[] = array('data'=>$list['point_buyername']);
            $tmp[] = array('data'=>$list['point_allpoint']);
            $tmp[] = array('data'=>$list['point_addtime']);
            $tmp[] = array('data'=>$list['point_orderstatetext']);
            $tmp[] = array('data'=>$list['point_goodstype']);
            $tmp[] = array('data'=>$list['point_couponcode']);
            $tmp[] = array('data'=>$list['point_couponendtime']);
            $tmp[] = array('data'=>$list['point_type']);
            $tmp[] = array('data'=>$list['point_ordermessage']);
            $tmp[] = array('data'=>$list['point_truename']);
            $tmp[] = array('data'=>$list['point_mobphone']);
            $tmp[] = array('data'=>$list['erp_mobile']);
            $tmp[] = array('data'=>$list['address']);
            $tmp[] = array('data'=>$list['goods_info']);
            $excel_data[] = $tmp;
        }
        $count = count($excel_data);
        $excel_data[$count][] = array('data' => '总共积分');
        $excel_data[$count][] = array('data' => $point_totals);
        $excel_data[$count][] = array('data' => '订单数量');
        $excel_data[$count][] = array('data' => $count - 1);
        $excel_data = $excel_obj->charset($excel_data, CHARSET);
        $excel_obj->addArray($excel_data);
        $excel_obj->addWorksheet($excel_obj->charset(L('exp_od_order'), CHARSET));
        $excel_obj->generateXML('points_order-' . $_GET['curpage'] . '-' . date('Y-m-d-H', time()));
    }

    public function order_importOp()
    {
        $newdata = [];
        if (chksubmit()) {
            require_once BASE_ROOT_PATH . '/vendor/phpoffice/phpexcel/Classes/PHPExcel/IOFactory.php';
            if (!empty($_FILES['files']['name'])) {
                $tmp = $_FILES['files']['tmp_name'];
                $imageName = "25220_" . date("His", time()) . "_" . rand(1111, 9999) . '.xlsx';
                $path = BASE_UPLOAD_PATH . DS . ATTACH_POINTPROD;
                if (move_uploaded_file($tmp, $path . DS . $imageName)) {
                    $fileSrc = $path . "/" . $imageName;
                } else {
                    echo 'error';
                    exit;
                }
            } else {
                showDialog('请上传导入文件', 'reload');
            }
            $extension = strtolower(pathinfo($_FILES['files']['name'], PATHINFO_EXTENSION));
            if ($extension == 'xlsx') {
                //$objReader = new PHPExcel_Reader_Excel2007();
                $objPHPExcel = PHPExcel_IOFactory::load($fileSrc);
            } else if ($extension == 'xls') {
                /* $objReader = PHPExcel_IOFactory::createReader('Excel5');
                 $objPHPExcel = $objReader->load($fileSrc);*/
                $objPHPExcel = PHPExcel_IOFactory::load($fileSrc);
            } else if ($extension == 'csv') {//没测
                $PHPReader = new PHPExcel_Reader_CSV();
                //默认输入字符集
                $PHPReader->setInputEncoding('GBK');
                //默认的分隔符
                $PHPReader->setDelimiter(',');
                //载入文件
                $objExcel = $PHPReader->load($fileSrc);
            }
            $sheet = $objPHPExcel->getSheet(0);
            $highestRow = $sheet->getHighestRow(); // 取得总行数
            //$highestColumn = $sheet->getHighestColumn(); // 取得总列数
            for ($i = 2; $i <= $highestRow; $i++) {
                $data = array();
                $data['point_ordersn'] = $objPHPExcel->getActiveSheet()->getCell("A" . $i)->getValue();//订单编号
                $data['point_orderstate'] = $objPHPExcel->getActiveSheet()->getCell("E" . $i)->getValue();//订单状态
                $data['point_shippingcode'] = $objPHPExcel->getActiveSheet()->getCell("F" . $i)->getValue();//物流单号
                $data['point_shipping_ecode'] = $objPHPExcel->getActiveSheet()->getCell("G" . $i)->getValue();//物流编码
                $data['point_shippingtime'] = $objPHPExcel->getActiveSheet()->getCell("H" . $i)->getValue();//发货时间
                if ($data['point_orderstate'] == "待发货" && $data['point_shippingcode']) {
                    array_push($newdata, $data);
                }
                /*$result= $this->send($data['order_sn'],$data['shipping_code'],$data['shipping_name']);
                if (!$result && $data['shipping_code']&& $data['order_sn']) {
                    array_push($newdata,$data);
                }*/
            }
            if (!empty($newdata) && is_array($newdata)) {
                $result = Model('pointorder')->batchShippingPointOrder($newdata);
                if ($result['state']) {
                    if (!empty($fileSrc)) {
                        @unlink($fileSrc);
                    }
                    showDialog($result['msg'], 'index.php?act=pointprod&op=pointorder_list', 'error');
                } else {
                    showDialog($result['msg'], 'reload');
                }
            } else {
                showDialog('导入订单数据没有合适数据', 'reload');
            }
        } else {
            Tpl::output('order_list', $newdata);
            Tpl::showpage('pointorder.import');
        }

    }
}
