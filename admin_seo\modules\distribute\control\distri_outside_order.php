<?php
/**
 * 分销-业绩管理
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');

class distri_outside_orderControl extends SystemControl{
    private $_links = array(
        array('url' => 'act=distri_outside_order&op=cash_list', 'text' => '业绩管理'),
        array('url' => 'act=distri_outside_order&op=cash_export', 'text' => '导入已结算业绩记录'),

    );
    const EXPORT_SIZE = 2000;
    public function indexOp(){
        $this->cash_listOp();
    }

    /**
     * 业绩列表
     */
    public function cash_listOp(){
        //输出子菜单
        Tpl::output('top_link', $this->sublink($this->_links, 'cash_list'));
        Tpl::showpage('distri_outside_order.list');
    }

    /**
     * 删除业绩记录
     */
    public function cash_delOp(){
        $id = intval($_GET['id']);
        if ($id > 0) {
            $model_trad = Model('dis_trad');
            $condition = array();
            $condition['tradc_id'] = $id;
            $condition['tradc_payment_state'] = 0;
            $info = $model_trad->getDistriTradCashInfo($condition);
            if (!$info) {
                exit(json_encode(array('state'=>false,'msg'=>'删除失败')));
            }
            try {
                $model_trad->beginTransaction();
                $result = $model_trad->delDistriTradCash($condition);
                if (!$result) {
                    throw new Exception(Language::get('admin_predeposit_cash_del_fail'));
                }
                //退还冻结的预存款
                $model_member = Model('member');
                $member_info = $model_member->getMemberInfo(array('member_id'=>$info['tradc_member_id']));
                //扣除冻结的预存款
                $admininfo = $this->getAdminInfo();
                $data = array();
                $data['member_id'] = $member_info['member_id'];
                $data['member_name'] = $member_info['member_name'];
                $data['amount'] = $info['tradc_amount'];
                $data['order_sn'] = $info['tradc_sn'];
                $data['admin_name'] = $admininfo['name'];
                $model_trad->changeDirtriTrad('cash_del',$data);
                $model_trad->commit();

                $this->log('佣金业绩申请删除[ID:'.$id.']',null);
                exit(json_encode(array('state'=>true,'msg'=>'删除成功')));
            } catch (Exception $e) {
                $model_trad->rollback();
                exit(json_encode(array('state'=>false,'msg'=>'删除失败')));
            }
        } else {
            exit(json_encode(array('state'=>false,'msg'=>'删除失败')));
        }
    }

    /**
     * 更改业绩为支付状态
     */
    public function cash_payOp(){
        $id = intval($_GET['id']);
        if ($id <= 0){
            showMessage('参数错误','index.php?act=distri_outside_order&op=cash_list','','error');
        }
        $model_trad = Model('dis_trad');
        $condition = array();
        $condition['tradc_id'] = $id;
        $condition['tradc_payment_state'] = 0;
        $info = $model_trad->getDistriTradCashInfo($condition);
        if (!is_array($info) || count($info)<0){
            showMessage('记录不存在或已付款','index.php?act=distri_outside_order&op=cash_list','','error');
        }

        //查询用户信息
        $model_member = Model('member');
        $member_info = $model_member->getMemberInfo(array('member_id'=>$info['tradc_member_id']));

        $update = array();
        $admininfo = $this->getAdminInfo();
        $update['tradc_payment_state'] = 1;
        $update['tradc_payment_admin'] = $admininfo['name'];
        $update['tradc_payment_time'] = TIMESTAMP;
        $log_msg = '佣金业绩付款完成，业绩单号：'.$info['tradc_sn'];

        try {
            $model_trad->beginTransaction();
            $result = $model_trad->updateDistriTradCash($update,$condition);
            if (!$result) {
                throw new Exception('付款失败');
            }
            //扣除冻结的预存款
            $data = array();
            $data['member_id'] = $member_info['member_id'];
            $data['member_name'] = $member_info['member_name'];
            $data['amount'] = $info['tradc_amount'];
            $data['order_sn'] = $info['tradc_sn'];
            $data['admin_name'] = $admininfo['name'];
            $model_trad->changeDirtriTrad('cash_pay',$data);
            $model_trad->commit();
            $this->log($log_msg,1);
            showMessage('付款成功','index.php?act=distri_outside_order&op=cash_list');
        } catch (Exception $e) {
            $model_trad->rollback();
            $this->log($log_msg,0);
            showMessage($e->getMessage(),'index.php?act=distri_outside_order&op=cash_list','html','error');
        }
    }
    /**
     * 更改业绩为拒绝状态
     */
    public function cash_noOp(){
        $id = intval($_POST['id']);

        if ($id <= 0){
            showMessage('参数错误','index.php?act=distri_outside_order&op=cash_list','','error');
        }
        $model_trad = Model('dis_trad');
        $condition = array();
        $condition['tradc_id'] = $id;
        $condition['tradc_payment_state'] = 0;
        $info = $model_trad->getDistriTradCashInfo($condition);
        if (!is_array($info) || count($info) < 0){
            showMessage('记录不存在或已付款','index.php?act=distri_outside_order&op=cash_list','','error');
        }

        //查询用户信息
        $model_member = Model('member');
        $member_info = $model_member->getMemberInfo(array('member_id'=>$info['tradc_member_id']));
        $update = array();
        $admininfo = $this->getAdminInfo();
        $update['tradc_payment_state'] = 2;
        $update['tradc_payment_admin'] = $admininfo['name'];
        $update['tradc_payment_time'] = TIMESTAMP;
        $update['tradc_content'] = $_POST['tradc_content'];
        $log_msg = '佣金业绩付款拒绝，业绩单号：'.$info['tradc_sn'];

        try {
            $model_trad->beginTransaction();
            $result = $model_trad->updateDistriTradCash($update,$condition);
            if (!$result) {
                echo json_encode(['code'=>400,'msg'=>'拒绝失败']);exit;
            }
            //扣除冻结的预存款
            $data = array();
            $data['member_id'] = $member_info['member_id'];
            $data['member_name'] = $member_info['member_name'];
            $data['amount'] = $info['tradc_amount'];
            $data['order_sn'] = $info['tradc_sn'];
            $data['admin_name'] = $admininfo['name'];
            $model_trad->changeDirtriTrad('cash_no',$data);
            $model_trad->commit();
            $this->log($log_msg,1);
            echo json_encode(['code'=>200,'msg'=>'拒绝成功']);exit;
        } catch (Exception $e) {
            $model_trad->rollback();
            $this->log($log_msg,0);
            echo json_encode(['code'=>400,'msg'=>$e->getMessage()]);exit;

        }
    }
    /**
     * 查看业绩信息
     */
    public function cash_viewOp(){

        $dis_order_id = intval($_GET['dis_order_id']);
        $order_id = intval($_GET['order_id']);
        $is_vir = intval($_GET['is_vir']);
        $model_order = Model('dis_outside_order');
        $model_member = Model('member');

        $dis_order_info = $model_order->getOutsideOrderInfo(['dis_order_id'=>$dis_order_id]);

        $member_info = $model_member->getMemberInfo(['member_id'=>$dis_order_info['dis_member_id']],'bill_user_name');
        $info = $model_order->getOutsideOrderView($order_id, $is_vir);
        $info['dis_member_name'] = $member_info['bill_user_name'];
        Tpl::output('info',$info);
        Tpl::showpage('distri_outside_order.view', 'null_layout');
    }

    /**
     * 导出预存款业绩记录
     *
     */
    public function export_cash_step1Op(){
        $condition = array();
        $if_start_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['stime']);
        $if_end_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['etime']);
        $start_unixtime = $if_start_date ? strtotime($_GET['stime']) : null;
        $end_unixtime = $if_end_date ? strtotime($_GET['etime']): null;
        if ($start_unixtime || $end_unixtime) {
            $condition['create_time'] = array('time',array($start_unixtime,$end_unixtime));
        }
        if (!empty($_GET['member_name'])){
            $condition['leader_member_name'] = array('like', '%' . $_GET['member_name'] . '%');
        }
        if (!empty($_GET['member_id'])){
            $condition['leader_member_id'] = $_GET['member_id'];
        }
        if (!empty($_GET['payment_admin'])){
            $condition['admin_name'] = array('like', '%' . $_GET['payment_admin'] . '%');
        }
        if ($_GET['payment_state'] != ''){
            $condition['state'] = $_GET['payment_state'];
        }
        $model_distri_outside = Model('dis_outside_order');
        $chain_model = Model('chain');
        $chain_list =$chain_model->getChainList(array('store_id'=>$_SESSION['store_id']), 'chain_id', null, '',10000);
        $chainIdsArr = array_column($chain_list,'chain_id');
        $condition['chain_id'] = array('in',$chainIdsArr);
        if (!is_numeric($_GET['curpage'])){
            $count = $model_distri_outside->getDisOutsideOrderCount($condition);
            $array = array();
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=distri_outside_order&op=cash_list');
                Tpl::showpage('export.excel');
            }else{  //如果数量小，直接下载
                $data = $model_distri_outside->getOutsideOrderlist($condition,'*','',self::EXPORT_SIZE);

                $cashpaystate = array(0=>'未打款',1=>'已打款',2=>'已拒绝');
                $member_id_arr = [];
                foreach ($data as $k=>$v) {
                    $member_id_arr[] = $v['leader_member_id'];
                    $data[$k]['state'] = $cashpaystate[$v['state']];
                }
                /*$on = 'member.distri_chainid = chain.chain_id';
                $where = [
                    'member.member_id'=>['in',$member_id_arr],
                ];
                $member_chain_list = Model()->table('member,chain')->field('member.member_id,chain.account_id,chain.chain_name')->join('left')->on($on)->where($where)->key('member_id')->limit(false)->select();*/

                $this->createCashExcel($data);
            }
        }else{  //下载
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $data = $model_distri_outside->getOutsideOrderlist($condition,'*','',"{$limit1},{$limit2}");
            $cashpaystate = array(0=>'未打款',1=>'已打款',2=>'已拒绝');
            foreach ($data as $k=>$v) {
                $member_id_arr[] = $v['leader_member_id'];
                $data[$k]['state'] = $cashpaystate[$v['state']];
            }
            /*$on = 'member.distri_chainid = chain.chain_id';
            $where = [
                'member.member_id'=>['in',$member_id_arr],
            ];
            $member_chain_list = Model()->table('member,chain')->field('member.member_id,chain.account_id,chain.chain_name')->join('left')->on($on)->where($where)->key('member_id')->limit(false)->select();*/

            $this->createCashExcel($data);
        }
    }

    /**
     * 生成导出预存款业绩excel
     *
     * @param array $data
     */
    private function createCashExcel($data = array()){
        Language::read('export');
        import('libraries.excel');
        $excel_obj = new Excel();
        $excel_data = array();
        //设置样式
        $excel_obj->setStyle(array('id'=>'s_title','Font'=>array('FontName'=>'宋体','Size'=>'12','Bold'=>'1')));
        //header
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'业绩ID');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'会员ID');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'姓名');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'业绩金额（元）');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'归属门店');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'结算状态');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'付款状态');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'支付时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'管理员');
        foreach ((array)$data as $k=>$v){
            $tmp = array();
            $tmp[] = array('data'=>$v['dis_order_id']);
            $tmp[] = array('data'=>$v['order_sn']);
            $tmp[] = array('data'=>$v['leader_member_id']);
            $tmp[] = array('data'=>$v['leader_member_name']);
            $tmp[] = array('format'=>'Number','data'=>ncPriceFormat($v['goods_amount']));
            $tmp[] = array('data'=>$v['chain_name']);
            if($v['state']==0){
                $state_text = '未结算';
            }else{
                $state_text = '已结算';
            }
            if($v['pay_state']==0){
                $payment_state = '未支付';
            }elseif($v['pay_state']==1){
                $payment_state = '已支付';
            }else{
                $payment_state= '已失效';
            }
            $tmp[] = array('data'=>$state_text);
            $tmp[] = array('data'=>$payment_state);
            $tmp[] = array('data'=>date('Y-m-d H:i:s',$v['pay_time']));
            $tmp[] = array('data'=>$v['admin_name']);
            $excel_data[] = $tmp;
        }
        $excel_data = $excel_obj->charset($excel_data,CHARSET);
        $excel_obj->addArray($excel_data);
        $excel_obj->addWorksheet($excel_obj->charset('分销佣金业绩',CHARSET));
        $excel_obj->generateXML($excel_obj->charset('分销佣金业绩',CHARSET).$_GET['curpage'].'-'.date('Y-m-d-H',time()));
    }


    /**
     * 获取业绩列表xml
     */
    public function get_xmlOp(){
        $model_trad = Model('dis_outside_order');
        $condition = array();
        $if_start_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['stime']);
        $if_end_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['etime']);
        $start_unixtime = $if_start_date ? strtotime($_GET['stime']) : null;
        $end_unixtime = $if_end_date ? strtotime($_GET['etime']): null;
        if ($start_unixtime || $end_unixtime) {
            $condition['create_time'] = array('time',array($start_unixtime,$end_unixtime));
        }
        if (!empty($_GET['member_name'])){
            $condition['leader_member_name'] = array('like', '%' . $_GET['member_name'] . '%');
        }
        if (!empty($_GET['member_id'])){
            $condition['leader_member_id'] = $_GET['member_id'];
        }
        if (!empty($_GET['payment_admin'])){
            $condition['admin_name'] = array('like', '%' . $_GET['payment_admin'] . '%');
        }
        if ($_GET['state'] != ''){
            $condition['state'] = $_GET['state'];
        }
        if ($_GET['payment_state'] != ''){
            $condition['pay_state'] = $_GET['payment_state'];
        }
        $page = $_POST['rp'];

        $chain_model = Model('chain');
        $chain_list =$chain_model->getChainList(array('store_id'=>$_SESSION['store_id']), 'chain_id', null, '',10000);
        $chainIdsArr = array_column($chain_list,'chain_id');
        $condition['chain_id'] = array('in',$chainIdsArr);
        $cash_list = $model_trad->getOutsideOrderlist($condition,'*',$page);

        $data = array();
        $data['now_page'] = $model_trad->shownowpage();
        $data['total_num'] = $model_trad->gettotalnum();
        foreach ($cash_list as $value) {
            $param = array();
            $param['operation'] = "";
            if ($value['state'] == 0) {
                $param['operation'] .= "<a class='btn red' href=\"javascript:void(0)\" onclick=\"fg_delete('" . $value['dis_order_id'] . "')\"><i class='fa fa-trash-o'></i>删除</a>";
            }
            $param['operation'] .= "<a class='btn green' href='javascript:void(0)' onclick=\"ajax_form('cash_info','查看业绩编号“". $value['dis_order_id'] ."”的明细', 'index.php?act=distri_outside_order&op=cash_view&dis_order_id=".$value['dis_order_id']."&order_id=". $value['order_id'] ."&is_vir=". $value['is_virtual'] ."', 840)\" ><i class='fa fa-list-alt'></i>查看</a>";
            $param['dis_order_id'] = $value['dis_order_id'];
            $param['order_sn'] = $value['order_sn'];
            $param['leader_member_id'] = $value['leader_member_id'];
            $param['leader_member_name'] = "<img src=".getMemberAvatarForID($value['leader_member_id'])." class='user-avatar' onMouseOut='toolTip()' onMouseOver='toolTip(\"<img src=".getMemberAvatarForID($value['leader_member_id']).">\")'>" .$value['leader_member_name'];
            $param['goods_amount'] = ncPriceFormat($value['goods_amount']);
            $param['chain_name'] = $value['chain_name'];
            $param['create_time'] = date('Y-m-d', $value['create_time']);

            if($value['state']==0){
                $param['state_text'] = '未结算';
            }else{
                $param['state_text'] = '已结算';
            }
            if($value['pay_state']==0){
                $param['payment_state'] = '未支付';
            }elseif($value['pay_state']==1){
                $param['payment_state'] = '已支付';
            }else{
                $param['payment_state'] = '已失效';
            }
            $param['pay_time'] = $value['pay_time'] > 0 ? date('Y-m-d', $value['pay_time']) : '';
            $param['payment_admin'] = $value['admin_name'];
            $data['list'][$value['dis_order_id']] = $param;
        }
        echo Tpl::flexigridXML($data);exit();

    }
    public function cash_exportOp(){

        Tpl::output('top_link', $this->sublink($this->_links, 'cash_export'));
        Tpl::showpage('distri_outside_order.export');
    }
    /**
     * 分类导出
     */
    public function cash_tem_exportOp($excel2007=true){
        require_once  BASE_ROOT_PATH.'/vendor/phpoffice/phpexcel/Classes/PHPExcel.php';
        require_once  BASE_ROOT_PATH.'/vendor/phpoffice/phpexcel/Classes/PHPExcel/Writer/Excel2007.php';


        $indexKey=['业绩编号','订单单号'];
        if(empty($filename)) $filename = '已打款业绩模版'.time();
        if( !is_array($indexKey)) return false;

        $header_arr = array('A','B','C','D','E','F','G','H','I','J','K','L','M', 'N','O','P','Q','R','S','T','U','V','W','X','Y','Z');

        //初始化PHPExcel()
        $objPHPExcel = new PHPExcel();

        //设置保存版本格式
        if($excel2007){
            $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
            $filename = $filename.'.xlsx';
        }else{
            $objWriter = new PHPExcel_Writer_Excel5($objPHPExcel);
            $filename = $filename.'.xls';
        }

        //接下来就是写数据到表格里面去
        $objActSheet = $objPHPExcel->getActiveSheet();
        $startRow = 1;

        for ($i=0; $i < count($indexKey); $i++) {

            $objActSheet->setCellValue($header_arr[$i].$startRow,$indexKey[$i]);
        }


        // 下载这个表格，在浏览器输出
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");;
        header('Content-Disposition:attachment;filename='.$filename.'');
        header("Content-Transfer-Encoding:binary");
        $objWriter->save('php://output');

    }

    /**
     * 批量导入业绩成功记录
     */
    public function cash_importOp(){
        require_once  BASE_ROOT_PATH.'/vendor/phpoffice/phpexcel/Classes/PHPExcel/IOFactory.php';
        //得到导入文件后缀名
        if (!empty($_FILES['dis_outside_export']['name'])){
            $tmp = $_FILES['dis_outside_export']['tmp_name'];
            $imageName = "25220_".date("His",time())."_".rand(1111,9999).'.xlsx';
            $path= BASE_UPLOAD_PATH.DS.ATTACH_DISTRI.DS;
            if(move_uploaded_file($tmp,$path.DS.$imageName)){
                $imageSrc= $path."/". $imageName;
            }else{
                echo 'error';exit;
            }
        }else{
            showDialog('文件错误', 'reload');
        }
        $objPHPExcelReader = PHPExcel_IOFactory::load($imageSrc);

        $reader = $objPHPExcelReader->getWorksheetIterator();

        //循环读取sheet
        foreach($reader as $sheet) {
            //读取表内容
            $content = $sheet->getRowIterator();
            //逐行处理
            $res_arr = array();
            foreach($content as $key => $items) {
                $rows = $items->getRowIndex();
                //行
                $columns = $items->getCellIterator();

                //列
                $row_arr = array();
                //确定从哪一行开始读取
                if($rows < 2){
                    continue;
                }
                //逐列读取
                foreach($columns as $head => $cell) {
                    //获取cell中数据
                    $data = $cell->getValue();
                    $row_arr[] = $data;
                }
                $res_arr[] = $row_arr;
            }
        }

        if(!empty($res_arr)){
            $out_order_id_arr=[];
            foreach ($res_arr as $value){
                $out_order_id_arr[] = $value[0];
            }
        }
        $this->edit_outside($out_order_id_arr);
    }


    public function edit_outside($out_order_id_arr){
        $model_dis_outside_order = Model('dis_outside_order');
        $condition = [
            'dis_order_id'=>['in',$out_order_id_arr],
            'state'=>1,
            'pay_state'=>0
        ];

        //查询用户信息
        $order_list = $model_dis_outside_order->getOutsideOrderlist($condition,$fields = '*', $page = '', $limit = false);
        if(empty($order_list)){
            showMessage('业绩记录无效','index.php?act=distri_outside_order&op=cash_export','html','error');
        }
        $update = array();
        $admininfo = $this->getAdminInfo();
        $update['pay_state'] = 1;
        $update['admin_name'] = $admininfo['name'];
        $update['pay_time'] = TIMESTAMP;

        try {
            $model_dis_outside_order->beginTransaction();
            foreach ($order_list as $value){
                $condition = array();
                $condition['dis_order_id'] = $value['dis_order_id'];
                $log_msg = '佣金业绩付款完成，业绩单号：'.$value['dis_order_id'];
                $result = $model_dis_outside_order->editOutsideOrder($condition, $update);
                if (!$result) {
                    throw new Exception('付款失败');
                }
                $model_dis_outside_order->commit();
                $this->log($log_msg,1);
            }

            showDialog('批量打款成功','index.php?act=distri_outside_order&op=cash_list');
        } catch (Exception $e) {
            $model_dis_outside_order->rollback();
            $this->log($log_msg,0);
            showMessage($e->getMessage(),'index.php?act=distri_outside_order&op=cash_export','html','error');
        }
    }

}