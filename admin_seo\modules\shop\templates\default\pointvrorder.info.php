<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title"> <a class="back" href="<?php echo urlAdminShop('pointprod', 'pointvrorder_list'); ?>" title="返回列表"> <i class="fa fa-arrow-circle-o-left"></i> </a>
      <div class="subject">
        <h3>积分兑换 - 兑换详情</h3>
        <h5>查看商城会员使用积分兑换礼品的详情</h5>
      </div>
    </div>
  </div>
  <div class="ncap-order-style">
    <div class="ncap-order-flow">
        <ol class="num5">
            <li class="current">
                <h5>生成订单</h5>
                <i class="fa fa-arrow-circle-right"></i>
                <time><?php echo date("Y-m-d H:i:s",$output['vrorder_info']['add_time']); ?></time>
            </li>
            <?php if ($output['vrorder_info']['order_state'] == ORDER_STATE_CANCEL) { ?>
                <li class="current">
                    <h5>取消订单</h5>
                    <time><?php echo date('Y-m-d H:i:s',$output['vrorder_info']['close_time']); ?></time>
                </li>
            <?php } else { ?>
                <li class="<?php echo $output['vrorder_info']['step_list']['step3'] ? 'current' : null ; ?>" style=" width: 40%;">
                    <h5>发放兑换码</h5>
                    <i class="fa fa-arrow-circle-right"></i>
                    <?php if (!empty($output['vrorder_info']['extend_vr_order_code'])){ ?>
                        <div class="code-list tip" title="如列表过长超出显示区域时可滚动鼠标进行查看">
                            <div id="codeList">
                                <ul>
                                    <?php foreach($output['vrorder_info']['extend_vr_order_code'] as $code_info){ ?>
                                        <li class="<?php echo $code_info['vr_state'] == 1 ? 'used' : null;?>"><strong><?php echo $code_info['vr_code'];?></strong> <?php echo $code_info['vr_code_desc'];?> </li>
                                    <?php } ?>
                                </ul>
                            </div>
                        </div>
                    <?php } ?>
                </li>
                <li class="long <?php echo $output['vrorder_info']['step_list']['step4'] ? 'current' : null ; ?>">
                    <h5>订单完成</h5>
                    <time><?php echo date("Y-m-d H:i:s",$output['vrorder_info']['finnshed_time']); ?></time>
                </li>
            <?php } ?>
        </ol>
    </div>
      <div class="ncap-order-details">
          <ul class="tabs-nav">
              <li class="current"><a href="javascript:void(0);">订单详情</a></li>
              <?php if(is_array($output['refund_list']) and !empty($output['refund_list'])) { ?>
                  <li><a href="javascript:void(0);">退款记录</a></li>
              <?php } ?>
          </ul>
          <div class="tabs-panels">
              <div class="misc-info">
                  <h4>下单/支付</h4>
                  <dl>
                      <dt>订单号<?php echo $lang['nc_colon'];?></dt>
                      <dd><?php echo $output['vrorder_info']['order_sn'];?></dd>
                      <dt>订单来源<?php echo $lang['nc_colon'];?></dt>
                      <dd><?php echo orderFromName($output['vrorder_info']['order_from'], 1);?></dd>
                      <dt>下单时间<?php echo $lang['nc_colon'];?></dt>
                      <dd><?php echo date('Y-m-d H:i:s',$output['vrorder_info']['add_time']);?></dd>
                  </dl>
                  <?php if(intval($output['vrorder_info']['payment_time'])){?>
                      <dl>
                          <dt>支付单号<?php echo $lang['nc_colon'];?></dt>
                          <dd><?php echo $output['vrorder_info']['order_sn'];?></dd>
                          <dt>支付方式<?php echo $lang['nc_colon'];?></dt>
                          <dd><?php echo orderPaymentName($output['vrorder_info']['payment_code']);?></dd>
                          <dt>支付时间<?php echo $lang['nc_colon'];?></dt>
                          <dd><?php echo intval(date('H:i:s',$output['vrorder_info']['payment_time'])) ? date('Y-m-d H:i:s',$output['vrorder_info']['payment_time']) : date('Y-m-d',$output['vrorder_info']['payment_time']);?></dd>
                      </dl>
                  <?php }?>
                  <?php if ($output['vrorder_info']['order_state'] == ORDER_STATE_CANCEL) { ?>
                      <dl>
                          <dt>订单取消日志<?php echo $lang['nc_colon'];?></dt>
                          <dd><?php echo $output['vrorder_info']['close_reason'];?></dd>
                      </dl>
                  <?php } ?>
                  <?php if ($output['vrorder_info']['chain_name']) { ?>
                      <dl>
                          <dt>所属门店<?php echo $lang['nc_colon'];?></dt>
                          <dd><?php echo $output['vrorder_info']['chain_name'];?></dd>
                      </dl>
                  <?php } ?>
              </div>
              <div class="addr-note">
                  <h4>购买/收货方信息</h4>
                  <dl>
                      <dt>买家<?php echo $lang['nc_colon'];?></dt>
                      <dd><?php echo $output['vrorder_info']['buyer_name'];?></dd>
                      <dt>接收手机<?php echo $lang['nc_colon'];?></dt>
                      <dd><?php echo $output['vrorder_info']['buyer_phone'];?></dd>
                  </dl>
                  <dl>
                      <dt>买家留言<?php echo $lang['nc_colon'];?></dt>
                      <dd><?php echo $output['vrorder_info']['buyer_msg'];?></dd>
                  </dl>
              </div>
              <div class="contact-info">
                  <h4>销售/发货方信息</h4>
                  <dl>
                      <dt>店铺<?php echo $lang['nc_colon'];?></dt>
                      <dd><?php echo $output['vrorder_info']['store_name'];?></dd>
                      <dt>店主名称<?php echo $lang['nc_colon'];?></dt>
                      <dd><?php echo $output['store_info']['seller_name'];?></dd>
                      <dt>联系电话<?php echo $lang['nc_colon'];?></dt>
                      <dd><?php echo !empty($output['store_info']['live_store_tel']) ? $output['store_info']['live_store_tel'] : $output['store_info']['store_phone']; ?></dd>
                  </dl>
                  <dl>
                      <dt>地&#12288;&#12288;址<?php echo $lang['nc_colon'];?></dt>
                      <dd><?php echo !empty($output['store_info']['live_store_address']) ? $output['store_info']['live_store_address'] : $output['store_info']['store_address']; ?></dd>
                  </dl>
                  <dl>
                      <dt>交通信息<?php echo $lang['nc_colon'];?></dt>
                      <dd><?php echo $output['store_info']['live_store_bus'];?></dd>
                  </dl>
              </div>
              <div class="goods-info">
                  <h4><?php echo $lang['admin_pointorder_info_prodinfo'];?></h4>
                  <table>
                      <thead>
                      <tr>
                          <th colspan="2">兑换礼品</th>
                          <th><?php echo $lang['admin_pointorder_exchangepoints'];?></th>
                          <th><?php echo $lang['admin_pointorder_info_prodinfo_exnum'];?></th>
                      </tr>
                      </thead>
                      <tbody>
                      <?php foreach($output['prod_list'] as $v){?>
                          <tr>
                              <td class="w30"><div class="goods-thumb"><a href="<?php echo urlShop('pointprod', 'pinfo', array('id' => $v['point_goodsid']));?>" target="_blank" class="order_info_pic"> <img src="<?php echo $v['point_goodsimage_small'];?>"/></a></div></td>
                              <td style="text-align: left;"><a href="<?php echo urlShop('pointprod', 'pinfo', array('id' => $v['point_goodsid']));?>" target="_blank"><?php echo $v['point_goodsname'];?></a></td>
                              <td class="w150"><?php echo $v['point_goodspoints'];?></td>
                              <td class="w150"><?php echo $v['point_goodsnum'];?></td>
                          </tr>
                      <?php }?>
                      </tbody>
                  </table>
                  <div class="total-amount">
                      <h3><?php echo $lang['admin_pointorder_exchangepoints'];?><?php echo $lang['nc_colon'];?><strong class="red_common"><?php echo $output['order_info']['point_allpoint'];?></strong></h3>
                      <?php if ($output['order_info']['point_shippingcharge'] == 1){ ?>
                          <h4>(<?php echo $lang['admin_pointorder_shippingfee'];?><?php echo $lang['nc_colon'];?><?php echo $output['order_info']['point_shippingfee'];?>)</h4>
                      <?php } ?>
                  </div>
          </div>
          <?php if(is_array($output['refund_list']) and !empty($output['refund_list'])) { ?>
              <div class="tabs-panels tabs-hide">
                  <div>
                      <h4>退款信息</h4>
                      <?php foreach($output['refund_list'] as $val) { ?>
                          <dl>
                              <dt>退款单号<?php echo $lang['nc_colon'];?></dt>
                              <dd><?php echo $val['refund_sn'];?></dd>
                              <dt>退款金额<?php echo $lang['nc_colon'];?></dt>
                              <dd><?php echo $lang['currency'];?><?php echo ncPriceFormat($val['refund_amount']); ?></dd>
                              <dt>管理员操作时间<?php echo $lang['nc_colon'];?></dt>
                              <dd><?php echo intval($val['admin_time']) ? date("Y-m-d H:i:s",$val['admin_time']) : null; ?></dd>
                              <dt>备注<?php echo $lang['nc_colon'];?></dt>
                              <dd><?php echo $val['goods_name'];?></dd>
                              <dt>状态：</dt>
                              <dd><?php echo str_replace(array(1,2,3), array('待审核','成功退款','管理员拒绝退款'), $val['admin_state'])?></dd>
                          </dl>
                      <?php } ?>
                  </div>
              </div>
          <?php } ?>
      </div>
  </div>
</div>
<script type="text/javascript">
    //兑换码列表过多时出现滚条
    $(function(){
        $('#codeList').perfectScrollbar();
        $(".tabs-nav > li > a").mousemove(function(e) {
            if (e.target == this) {
                var tabs = $(this).parent().parent().children("li");
                var panels = $(this).parents('.ncap-order-details:first').children(".tabs-panels");
                var index = $.inArray(this, $(this).parents('ul').find("a"));
                if (panels.eq(index)[0]) {
                    tabs.removeClass("current").eq(index).addClass("current");
                    panels.addClass("tabs-hide").eq(index).removeClass("tabs-hide");
                }
            }
        });
    });
</script>
