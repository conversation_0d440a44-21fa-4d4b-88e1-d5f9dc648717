<?php
/**
 * 分销-结算管理
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;
use Upet\Models\Order as OrderAlias;
use Upet\Models\VrOrder;
use Upet\Integrates\Redis\RedisManager as Redis;

defined('InShopNC') or exit('Access Invalid!');

class distri_billControl extends SystemControl
{
    const EXPORT_SIZE = 5000;

    function __construct()
    {
        parent::__construct();
    }

    /**
     * 分销佣金结算列表
     */
    public function indexOp()
    {
        Tpl::showpage('distri_bill.index');
    }

    /**
     * 获取分销佣金结算xml数据
     */
    public function get_bill_xmlOp(){
        $model_bill = Model('dis_bill');
        $condition = array();
        $condition['dis_pay.store_id'] = $_SESSION['store_id'];
        list($condition, $order) = $this->_get_bill_condition($condition);
        $flag =false;
        if (isset($condition['member.distri_chainid']) || $condition['dis_member_id']){
            $flag = true;
        }
        $bill_list = $model_bill->getDistriBillList($condition, '*', $_POST['rp'], $order,0,'', $flag);
        $data = array();
        $data['now_page'] = $model_bill->shownowpage();
        $data['total_num'] = $model_bill->gettotalnum();

        $orderIds = $vrOrderIds = $disTypes = $vrDisTypes= [];
        foreach ($bill_list as $bill){
            if($bill['is_virtual']) {
                $vrOrderIds[] = $bill['order_id'];
                continue;
            }
            $orderIds[] = $bill['order_id'];
        }
        if($orderIds) {
            $disTypes = OrderAlias::whereIn('order_id',$orderIds)->column('dis_type','order_id');
        }
        if($vrOrderIds) {
            $vrDisTypes = VrOrder::whereIn('order_id',$vrOrderIds)->column('dis_type','order_id');
        }

        foreach ($bill_list as $bill_info) {
            $list = array();
            $list['operation'] = "--";
            $list['log_id'] = $bill_info['log_id'];
            $list['order_sn'] = $bill_info['order_sn'];
            $list['goods_name'] = $bill_info['goods_name'];
            $list['add_time'] = date('Y-m-d', $bill_info['add_time']);
            $list['pay_goods_amount'] = ncPriceFormat($bill_info['pay_goods_amount']);
            $list['refund_amount'] = ncPriceFormat($bill_info['refund_amount']);
            $list['dis_commis_rate'] = floatval($bill_info['dis_commis_rate']).'%';
            $list['dis_pay_amount'] = ncPriceFormat($bill_info['dis_pay_amount']);
            $list['dis_order_amount'] = ncPriceFormat($bill_info['dis_order_amount']);
            if($bill_info['dis_pay_time']){
                $list['dis_pay_time'] = date('Y-m-d', $bill_info['dis_pay_time']);
            }else{
                $list['dis_pay_time'] ='';
            }
            $list['log_state'] = $bill_info['log_state']?'已结':'未结';
            $list['store_id'] = $bill_info['store_id'];
            $list['dis_member_id'] = $bill_info['dis_member_id'];

            $disType = $bill_info['is_virtual'] ? $vrDisTypes[$bill_info['order_id']] : $disTypes[$bill_info['order_id']];
            $list['remark'] = Logic("distribute")->getDisTypeText($disType);

            $data['list'][$bill_info['log_id']] = $list;
        }

        list($where,$arr) = $this->_get_bill_export_condition([]);
        $searchKey = md5(json_encode($where));
        if ($cache = Redis::get($searchKey)) {
            $sellnum = $cache;
        }else {
            $where1 = $where;
            $where1['vr_order_code.vr_state'] = 1;
            $where1['dis_pay.is_virtual'] = 1;
            $data1 = Model()->table('dis_pay,member,vr_order_code,chain,vr_order')->join('left,left,left,left,left')
                ->on('dis_pay.dis_member_id = member.member_id,dis_pay.order_goods_id = vr_order_code.rec_id,vr_order_code.chain_id = chain.chain_id,dis_pay.order_id = vr_order.order_id')
                ->where($where1)->sum('dis_pay.dis_pay_amount');

            $where2 = $where;
            $where2['dis_pay.is_virtual'] = 0;
            $data2 = Model()->table('dis_pay,member,order_goods,chain,orders')->join('left,left,left,left,left')
                ->on('dis_pay.dis_member_id = member.member_id,dis_pay.order_goods_id = order_goods.rec_id,order_goods.chain_id = chain.chain_id,dis_pay.order_id = orders.order_id')
                ->where($where2)->sum('dis_pay.dis_pay_amount');
            $sellnum = bcadd($data1, $data2, 2) ?: 0;
            Redis::set($searchKey, $sellnum, 5*60);
        }
        $key='dis_pay_sellnum_'.$_SESSION['store_id'];
        $_SESSION[$key] = $sellnum;
        exit(Tpl::flexigridXML($data));
    }
    public function get_sellnumOp(){
        $key='dis_pay_sellnum_'.$_SESSION['store_id'];
        echo json_encode(array($key=>$_SESSION['dis_pay_sellnum']));
    }
    /**
     * 导出账单表
     *
     */
    public function export_billOp(){
        $model_bill = Model('dis_bill');
        $condition = array();
        if (preg_match('/^[\d,]+$/', $_GET['ob_id'])) {
            $_GET['ob_id'] = explode(',',trim($_GET['ob_id'],','));
            $condition['log_id'] = array('in',$_GET['ob_id']);
        }
        list($condition,$order) = $this->_get_bill_export_condition($condition);
        $condition['vr_order_code.vr_state'] = 1;
        $condition['dis_pay.is_virtual'] = 1;
        if (!is_numeric($_GET['curpage'])){
            $count = $model_bill->getDistriBillNewCount($condition,'v');
            $array = array();
            if ($count > self::EXPORT_SIZE){
                //显示下载链接
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','javascript:history.back(-1)');
                Tpl::showpage('export.excel');
                exit();
            }
            $limit = false;
        }else{
            //下载
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $limit = "{$limit1},{$limit2}";
        }

        $data = $model_bill->getDistriBillListNew($condition,'*','','log_id desc',$limit,'v');
        $this->billExcel($data);
    }

    /**
     * 导出实物账单表
     *
     */
    public function export_bill_rOp(){
        $model_bill = Model('dis_bill');
        $condition = array();
        if (preg_match('/^[\d,]+$/', $_GET['ob_id'])) {
            $_GET['ob_id'] = explode(',',trim($_GET['ob_id'],','));
            $condition['log_id'] = array('in',$_GET['ob_id']);
        }
        list($condition,$order) = $this->_get_bill_export_condition($condition);
        $condition['dis_pay.is_virtual'] = 0;
        if (!is_numeric($_GET['curpage'])){
            $count = $model_bill->getDistriBillNewCount($condition,'r');

            $array = array();
            if ($count > self::EXPORT_SIZE){
                //显示下载链接
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','javascript:history.back(-1)');
                Tpl::showpage('export.excel');
                exit();
            }
            $limit = false;
        }else{
            //下载
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $limit = "{$limit1},{$limit2}";
        }
        $data = $model_bill->getDistriBillListNew($condition,'*','','log_id desc',$limit,'r');
        $this->billExcelR($data);
    }
    /**
     * 导出虚拟结算
     */
    private function billExcel($data_order)
    {
        require_once BASE_ROOT_PATH . '/vendor/phpoffice/phpexcel/Classes/PHPExcel.php';
        require_once BASE_ROOT_PATH . '/vendor/phpoffice/phpexcel/Classes/PHPExcel/Writer/Excel2007.php';

        $indexKey = array(
            'log_id'=>'结算编号',
            'order_sn'=>'订单编号',
            'goods_name'=>'商品名称',
            'add_time'=>'添加时间',
            'pay_goods_amount'=>'支付金额',
            'refund_amount'=>'退款金额',
            'dis_commis_rate'=>'分销佣金比例',
            'dis_pay_amount'=>'分销佣金',
            'dis_pay_time'=>'结算时间',
            'log_state'=>'结算状态',
            'store_id'=>'商家ID',
            'dis_member_id'=>'分销员ID',
            'bill_user_name'=>'分销员名称',
            'member_mobile'=>'分销员电话',
            'chain_name'=>'归属门店',
            'account_id'=>'门店财务编号',
            'is_virtual'=>'订单类型',
            'code_chain_name'=>'虚拟订单核销门店',
            'code_account_id'=>'核销门店财务编码',
            'remark'=>'备注'
        );

        if (empty($filename)) $filename = 'dis_bill-' . date('Y-m-d');
        if (!is_array($indexKey)) return false;
        $excel2007 = true;
        $header_arr = [
            'log_id' => 'A',
            'order_sn' => 'B',
            'goods_name' => 'C',
            'add_time' => 'D',
            'pay_goods_amount' => 'E',
            'refund_amount' => 'F',
            'dis_commis_rate' => 'G',
            'dis_pay_amount' => 'H',
            'dis_pay_time' => 'I',
            'log_state' => 'J',
            'store_id' => 'K',
            'dis_member_id' => 'L',
            'bill_user_name' => 'M',
            'member_mobile' => 'N',
            'chain_name' => 'O',
            'account_id' => 'P',
            'is_virtual' =>'Q',
            'code_chain_name' =>'R',
            'code_account_id' =>'S',
            'remark'=>'T'
        ];
        //初始化PHPExcel()
        $objPHPExcel = new PHPExcel();
        //设置保存版本格式
        if ($excel2007) {
            $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
            $filename = $filename . '.xlsx';
        } else {
            $objWriter = new PHPExcel_Writer_Excel5($objPHPExcel);
            $filename = $filename . '.xls';
        }

        //接下来就是写数据到表格里面去
        $objActSheet = $objPHPExcel->getActiveSheet();
        $startRow = 1;
        foreach ($indexKey as $key => $val) {
            $objActSheet->setCellValue($header_arr[$key] . $startRow, $val);
        }

        $data = array();
        foreach ($data_order as $k => $order_info) {
            $list = array();
            $list['log_id'] = $order_info['log_id'];
            $list['order_sn'] = $order_info['order_sn'];
            $list['goods_name'] = $order_info['goods_name'];
            $list['add_time'] = date('Y-m-d H:i:s', $order_info['add_time']);
            $list['pay_goods_amount'] = ncPriceFormat($order_info['pay_goods_amount']);
            $list['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
            $list['dis_commis_rate'] =floatval($order_info['dis_commis_rate']);
            $list['dis_pay_amount'] = ncPriceFormat($order_info['dis_pay_amount']);
            $list['dis_pay_time'] = $order_info['dis_pay_time']?date('Y-m-d H:i:s', $order_info['dis_pay_time']):'';
            $list['log_state'] = $order_info['log_state']?'已结':'未结';
            $list['store_id'] = $order_info['store_id'];
            $list['dis_member_id'] = $order_info['dis_member_id'];
            $list['bill_user_name'] = $order_info['bill_user_name'];
            $list['member_mobile'] = $order_info['member_mobile'];
            $list['chain_name'] = $order_info['chain_name'];
            $list['account_id'] = $order_info['account_id'];
            $list['is_virtual'] = $order_info['is_virtual']?'虚拟':'实物';
            $list['code_chain_name'] = $order_info['code_chain_name'];
            $list['code_account_id'] = $order_info['code_account_id'];
            $list['remark'] = Logic("distribute")->getDisTypeText($order_info['dis_type']);
            $data[] = $list;
        }
        foreach ($data as $k => $row) {
            foreach ($indexKey as $key => $value) {
                //这里是设置单元格的内容
                if (in_array($header_arr[$key], ['B','N'])) {
                    $objPHPExcel->setActiveSheetIndex(0);
                    $objActSheet->setTitle('Simple');

                    $objActSheet->setCellValueExplicit($header_arr[$key] . ($k + 2), $row[$key], PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $objActSheet->setCellValue($header_arr[$key] . ($k + 2), $row[$key]);
                }
            }
            $startRow++;
        }


        //ob_end_clean();//清除缓冲区,避免乱码
        // 下载这个表格，在浏览器输出
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");;
        header('Content-Disposition:attachment;filename='.$filename.'');
        header("Content-Transfer-Encoding:binary");
        $filename = iconv("utf-8", "gb2312", $filename);
        $objWriter->save('php://output');

    }
    /**
     * 导出实物结算
     */
    private function billExcelR($data_order)
    {

        require_once BASE_ROOT_PATH . '/vendor/phpoffice/phpexcel/Classes/PHPExcel.php';
        require_once BASE_ROOT_PATH . '/vendor/phpoffice/phpexcel/Classes/PHPExcel/Writer/Excel2007.php';

        $indexKey = array(
            'log_id'=>'结算编号',
            'order_sn'=>'订单编号',
            'goods_name'=>'商品名称',
            'add_time'=>'添加时间',
            'pay_goods_amount'=>'支付金额',
            'refund_amount'=>'退款金额',
            'dis_commis_rate'=>'分销佣金比例',
            'dis_pay_amount'=>'分销佣金',
            'dis_pay_time'=>'结算时间',
            'log_state'=>'结算状态',
            'store_id'=>'商家ID',
            'dis_member_id'=>'分销员ID',
            'bill_user_name'=>'分销员名称',
            'member_mobile'=>'分销员电话',
            'chain_name'=>'归属门店',
            'account_id'=>'门店财务编号',
            'is_virtual'=>'订单类型',
            'remark'=>'备注',

        );

        if (empty($filename)) $filename = 'dis_bill_r-' . date('Y-m-d');
        if (!is_array($indexKey)) return false;
        $excel2007 = true;
        $header_arr = [
            'log_id' => 'A',
            'order_sn' => 'B',
            'goods_name' => 'C',
            'add_time' => 'D',
            'pay_goods_amount' => 'E',
            'refund_amount' => 'F',
            'dis_commis_rate' => 'G',
            'dis_pay_amount' => 'H',
            'dis_pay_time' => 'I',
            'log_state' => 'J',
            'store_id' => 'K',
            'dis_member_id' => 'L',
            'bill_user_name' => 'M',
            'member_mobile' => 'N',
            'chain_name' => 'O',
            'account_id' => 'P',
            'is_virtual' =>'Q',
            'remark' =>'R',
        ];
        //初始化PHPExcel()
        $objPHPExcel = new PHPExcel();
        //设置保存版本格式
        if ($excel2007) {
            $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
            $filename = $filename . '.xlsx';
        } else {
            $objWriter = new PHPExcel_Writer_Excel5($objPHPExcel);
            $filename = $filename . '.xls';
        }

        //接下来就是写数据到表格里面去
        $objActSheet = $objPHPExcel->getActiveSheet();
        $startRow = 1;
        foreach ($indexKey as $key => $val) {

            $objActSheet->setCellValue($header_arr[$key] . $startRow, $val);
        }

        $data = array();


        foreach ($data_order as $k => $order_info) {


            $list = array();
            $list['log_id'] = $order_info['log_id'];
            $list['order_sn'] = $order_info['order_sn'];
            $list['goods_name'] = $order_info['goods_name'];
            $list['add_time'] = date('Y-m-d H:i:s', $order_info['add_time']);
            $list['pay_goods_amount'] = ncPriceFormat($order_info['pay_goods_amount']);
            $list['refund_amount'] = ncPriceFormat($order_info['refund_amount']);

            $list['dis_commis_rate'] =$order_info['dis_commis_rate'];
            $list['dis_pay_amount'] = ncPriceFormat($order_info['dis_pay_amount']);
            $list['dis_pay_time'] = $order_info['dis_pay_time']?date('Y-m-d H:i:s', $order_info['dis_pay_time']):'';
            $list['log_state'] = $order_info['log_state']?'已结':'未结';
            $list['store_id'] = $order_info['store_id'];
            $list['dis_member_id'] = $order_info['dis_member_id'];

            $list['bill_user_name'] = $order_info['bill_user_name'];
            $list['member_mobile'] = $order_info['member_mobile'];
            $list['chain_name'] = $order_info['chain_name'];
            $list['account_id'] = $order_info['account_id'];
            $list['is_virtual'] = $order_info['is_virtual']?'虚拟':'实物';
            $list['remark'] = Logic("distribute")->getDisTypeText($order_info['dis_type']);
            $data[] = $list;
        }
        foreach ($data as $k => $row) {

            foreach ($indexKey as $key => $value) {

                //这里是设置单元格的内容

                if (in_array($header_arr[$key], ['B','N','S'])) {
                    $objPHPExcel->setActiveSheetIndex(0);
                    $objActSheet->setTitle('Simple');

                    $objActSheet->setCellValueExplicit($header_arr[$key] . ($k + 2), $row[$key], PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $objActSheet->setCellValue($header_arr[$key] . ($k + 2), $row[$key]);
                }
            }
            $startRow++;
        }


        //ob_end_clean();//清除缓冲区,避免乱码
        // 下载这个表格，在浏览器输出
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");;
        header('Content-Disposition:attachment;filename='.$filename.'');
        header("Content-Transfer-Encoding:binary");
        $filename = iconv("utf-8", "gb2312", $filename);
        $objWriter->save('php://output');

    }
    /**
     * 合并相同代码
     */
    private function _get_bill_condition($condition) {

        if (!empty($_REQUEST['query']) ) {
            $condition[$_REQUEST['qtype']] = $_REQUEST['query'];
        }

        if (is_numeric($_GET["log_state"])) {
            $condition['log_state'] = intval($_GET["log_state"]);
        }
        if (is_numeric($_GET["order_sn"])) {
            $condition['order_sn'] = intval($_GET["order_sn"]);
        }
        if ($_GET['goods_name'] != ''){
            if ($_GET['jq_query']) {
                $condition['goods_name'] = $_GET['goods_name'];
            } else {
                $condition['goods_name'] = array('like',"%{$_GET['goods_name']}%");
            }
        }
        if (is_numeric($_GET["dis_tri_state"]) && $_GET['dis_tri_state'] == 1) {
            $condition['member.distri_chainid'] =['gt',0];
        }elseif(is_numeric($_GET["dis_tri_state"]) && $_GET['dis_tri_state'] == 0){
            $condition['member.distri_chainid'] = 0;
        }
        if (!empty($_GET['dis_member_id'])){
            $condition['dis_member_id'] = array('like', '%' . $_GET['dis_member_id'] . '%');
        }

        $if_start_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['stime']);
        $if_end_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['etime']);
        $start_unixtime = $if_start_date ? strtotime($_GET['stime']) : null;
        $end_unixtime = $if_end_date ? strtotime($_GET['etime']): null;
        if ($start_unixtime || $end_unixtime) {
            $condition['dis_pay_time'] = array('time',array($start_unixtime,$end_unixtime));
        }
        $sort_fields = array('log_id','order_sn','store_id','dis_member_id','goods_commonid','goods_name','add_time','pay_goods_amount','refund_amount','dis_commis_rate','dis_commis_rate','dis_pay_amount','dis_pay_time','log_state');
        if (in_array($_REQUEST['sortorder'],array('asc','desc')) && in_array($_REQUEST['sortname'],$sort_fields)) {
            $order = $_REQUEST['sortname'].' '.$_REQUEST['sortorder'];
        } else {
            $order = 'log_id desc';
        }
        return array($condition,$order);
    }

    /**
     * 导出条件
     */
    private function _get_bill_export_condition($condition) {
        if (!empty($_REQUEST['query']) ) {
            $condition['dis_pay.'.$_REQUEST['qtype']] = $_REQUEST['query'];
        }

        if (is_numeric($_GET["log_state"])) {
            $condition['dis_pay.log_state'] = intval($_GET["log_state"]);
        }
        if (is_numeric($_GET["order_sn"])) {
            $condition['dis_pay.order_sn'] = intval($_GET["order_sn"]);
        }
        if ($_GET['goods_name'] != ''){
            if ($_GET['jq_query']) {
                $condition['dis_pay.goods_name'] = $_GET['goods_name'];
            } else {
                $condition['dis_pay.goods_name'] = array('like',"%{$_GET['goods_name']}%");
            }
        }
        if (is_numeric($_GET["dis_tri_state"]) && $_GET['dis_tri_state'] == 1) {
            $condition['member.distri_chainid'] =['gt',0];
        }elseif(is_numeric($_GET["dis_tri_state"]) && $_GET['dis_tri_state'] == 0){
            $condition['member.distri_chainid'] = 0;
        }
        if (!empty($_GET['dis_member_id'])){
            $condition['dis_pay.dis_member_id'] = array('like', '%' . $_GET['dis_member_id'] . '%');
        }
        $if_start_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['stime']);
        $if_end_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['etime']);
        $start_unixtime = $if_start_date ? strtotime($_GET['stime']) : null;
        $end_unixtime = $if_end_date ? strtotime($_GET['etime']): null;
        if ($start_unixtime || $end_unixtime) {
            $condition['dis_pay.dis_pay_time'] = array('time',array($start_unixtime,$end_unixtime));
        }
        $sort_fields = array('log_id','order_sn','store_id','dis_member_id','goods_commonid','goods_name','add_time','pay_goods_amount','refund_amount','dis_commis_rate','dis_commis_rate','dis_pay_amount','dis_pay_time','log_state');
        if (in_array($_REQUEST['sortorder'],array('asc','desc')) && in_array($_REQUEST['sortname'],$sort_fields)) {
            $order = $_REQUEST['sortname'].' '.$_REQUEST['sortorder'];
        } else {
            $order = 'log_id desc';
        }
        $condition['dis_pay.store_id'] = $_SESSION['store_id'];
        return array($condition,$order);
    }

    /**
     * 查询指定分销员信息
     */
    public function ajax_dis_nameOp(){
        $dis_name_type = intval($_GET['dis_name_type']);
        $dis_name = $_GET['dis_name'];
        $model_member = Model('member');
        $condition = array();
        $field = ($dis_name_type == 1 ? 'member_mobile' : 'member_id');
        $condition[$field] = $dis_name;
        $condition['distri_state'] = 2;
        $data = $model_member->getDistriMemberInfo($condition, 'member_id,member_name,member_mobile,chain_name');
        if ($data){
            $data = $data[0];
            $data['code'] = 200;
        }else{
            $data['code'] = 400;
            $data['msg']  = "暂无结果！";
        }

        exit(json_encode($data));
    }
}