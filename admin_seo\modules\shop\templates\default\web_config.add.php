<?php defined('InShopNC') or exit('Access Invalid!');?>
<div class="page">
  <div class="fixed-bar">
    <div class="item-title">
        <a class="back" href="index.php?act=web_channel" title="返回商城首页板块列表"><i class="fa fa-arrow-circle-o-left"></i></a>
        <div class="subject">
            <h3><a>首页板块 - 新增商城首页</a></h3>
            <h5>商城首页板块内容管理</h5>
        </div>
    </div>
  </div>
  <form id="web_form" method="post" name="form1">
    <input type="hidden" name="form_submit" value="ok" />
    <div class="ncap-form-default">
      <dl class="row">
        <dt class="tit">
            <label for="channel_name"><em>*</em>模块名称</label>
        </dt>
        <dd class="opt">
          <input id="channel_name" name="channel_name" value="" class="input-txt" type="text" maxlength="20">
          <span class="err"></span>
        </dd>
      </dl>
      <dl class="row">
        <dt class="tit">
          <label><em>*</em>颜色风格</label>
        </dt>
        <dd class="opt">
          <input type="hidden" value="default" name="channel_style" id="channel_style">
          <ul class="home-templates-board-style">
            <li class="red"><em></em><i class="fa fa-check-circle"></i><?php echo $lang['web_config_style_red'];?></li>
            <li class="pink"><em></em><i class="fa fa-check-circle"></i><?php echo $lang['web_config_style_pink'];?></li>
            <li class="orange"><em></em><i class="fa fa-check-circle"></i><?php echo $lang['web_config_style_orange'];?></li>
            <li class="green"><em></em><i class="fa fa-check-circle"></i><?php echo $lang['web_config_style_green'];?></li>
            <li class="blue"><em></em><i class="fa fa-check-circle"></i><?php echo $lang['web_config_style_blue'];?></li>
            <li class="purple"><em></em><i class="fa fa-check-circle"></i><?php echo $lang['web_config_style_purple'];?></li>
            <li class="brown"><em></em><i class="fa fa-check-circle"></i><?php echo $lang['web_config_style_brown'];?></li>
            <li class="default selected"><em></em><i class="fa fa-check-circle"></i><?php echo $lang['web_config_style_default'];?></li>
          </ul>
          <span class="err"></span>
          <p class="notic">选择该频道的整体风格，与页面中的CSS配合使用。</p>
        </dd>
      </dl>
      <dl class="row">
          <dt class="tit">所在页面</dt>
          <dd class="opt">
              <select id="inpage" name="page">
                  <option value="index">默认1（index）</option>
                  <option value="index_fl">默认2（index_fl）</option>
                  <option value="index_fl3">风格3（index_fl3）</option>
                  <option value="index_fl4">风格4（index_fl4）</option>
                  <option value="index_fl5">风格5（index_fl5）</option>
                  <option value="index_fl6">风格6（index_fl6）</option>
              </select>
          </dd>
      </dl>
       <dl class="row">
          <dt class="tit">预览效果</dt>
          <dd class="opt _opt">
             <a href="<?php echo C("admin_site_url");?>/templates/default/images/style/f1.png" target="_blank"><img src="<?php echo C("admin_site_url");?>/templates/default/images/style/f1.png" width=500 alt=""></a>
          </dd>
      </dl>
      
      <dl class="row">
        <dt class="tit">启用状态</dt>
        <dd class="opt">
          <div class="onoff">
            <label for="show1" class="cb-enable selected" title="<?php echo $lang['nc_yes'];?>"><?php echo $lang['nc_yes'];?></label>
            <label for="show0" class="cb-disable" title="<?php echo $lang['nc_no'];?>"><?php echo $lang['nc_no'];?></label>
            <input id="show1" name="channel_show" checked="checked" value="1" type="radio">
            <input id="show0" name="channel_show" value="0" type="radio">
          </div>
          <p class="notic"></p>
        </dd>
      </dl>
      <div class="bot">
          <a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="submitBtn"><?php echo $lang['nc_submit'];?></a>
      </div>
    </div>
  </form>
</div>
<script>
//按钮先执行验证再提交表单
$(function(){
	$("#submitBtn").click(function(){
        if($("#web_form").valid()){
            $("#web_form").submit();
		}
	});
	$(".home-templates-board-style li").click(function(){
        $(".home-templates-board-style li").removeClass("selected");
        $("#channel_style").val($(this).attr("class"));
        $(this).addClass("selected");
	});
	$("#web_form").validate({
		errorPlacement: function(error, element){
			var error_td = element.parent('dd').children('span.err');
            error_td.append(error);
        },
        rules : {
            channel_name : {
                required : true
            }
        },
        messages : {
            channel_name : {
                required : "<i class='fa fa-exclamation-circle'></i>频道名称不能为空"
            }
        }
	});

	//下拉框触发事件
  	  $('#inpage').change(function(){
      	  var val = $(this).val();         	     	 
     	  var imgurl="<?php echo C("admin_site_url");?>/templates/default/images/style/";
     	        	 
      	  if(val=="index"){
      		  imgurl+="f1.png";
          	  }	
      	  if(val=="index_fl"){
      		  imgurl+="f2.png";
          	  }
      	  if(val=="index_fl3"){
      		  imgurl+="f3.png";
          	  }
      	  if(val=="index_fl4"){
      		  imgurl+="f4.png";
          	  }
      	  if(val=="index_fl5"){
      		  imgurl+="f5.png";
          	  }
      	  if(val=='index_fl6'){
			  imgurl+="f6.png";
              }      	 
       	  $("._opt").find("a").attr("href",imgurl);
      	  $("._opt").find("img").attr("src",imgurl);
       });

});
</script>
