<?php
/**
 * 分销-提现管理
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');

class distri_cashControl extends SystemControl{
    private $_links = array(
        array('url' => 'act=distri_cash&op=cash_list', 'text' => '提现管理'),
        array('url' => 'act=distri_cash&op=cash_export', 'text' => '导入已打款记录'),

    );
    const EXPORT_SIZE = 2000;
    public function indexOp(){
        $this->cash_listOp();
    }

    /**
     * 提现列表
     */
    public function cash_listOp(){
        //输出子菜单
        Tpl::output('top_link', $this->sublink($this->_links, 'cash_list'));
        Tpl::showpage('distri_cash.list');
    }

    /**
     * 删除提现记录
     */
    public function cash_delOp(){
        $id = intval($_GET['id']);
        if ($id > 0) {
            $model_trad = Model('dis_trad');
            $condition = array();
            $condition['tradc_id'] = $id;
            $condition['tradc_payment_state'] = 0;
            $info = $model_trad->getDistriTradCashInfo($condition);
            if (!$info) {
                exit(json_encode(array('state'=>false,'msg'=>'删除失败')));
            }
            try {
                $model_trad->beginTransaction();
                $result = $model_trad->delDistriTradCash($condition);
                if (!$result) {
                    throw new Exception(Language::get('admin_predeposit_cash_del_fail'));
                }
                //退还冻结的预存款
                $model_member = Model('member');
                $member_info = $model_member->getMemberInfo(array('member_id'=>$info['tradc_member_id']));
                //扣除冻结的预存款
                $admininfo = $this->getAdminInfo();
                $data = array();
                $data['member_id'] = $member_info['member_id'];
                $data['member_name'] = $member_info['member_name'];
                $data['amount'] = $info['tradc_amount'];
                $data['order_sn'] = $info['tradc_sn'];
                $data['admin_name'] = $admininfo['name'];
                $model_trad->changeDirtriTrad('cash_del',$data);
                $model_trad->commit();

                $this->log('佣金提现申请删除[ID:'.$id.']',null);
                exit(json_encode(array('state'=>true,'msg'=>'删除成功')));
            } catch (Exception $e) {
                $model_trad->rollback();
                exit(json_encode(array('state'=>false,'msg'=>'删除失败')));
            }
        } else {
            exit(json_encode(array('state'=>false,'msg'=>'删除失败')));
        }
    }

    /**
     * 更改提现为支付状态
     */
    public function cash_payOp(){
        $id = intval($_GET['id']);
        if ($id <= 0){
            showMessage('参数错误','index.php?act=distri_cash&op=cash_list','','error');
        }
        $model_trad = Model('dis_trad');
        $condition = array();
        $condition['tradc_id'] = $id;
        $condition['tradc_payment_state'] = 0;
        $info = $model_trad->getDistriTradCashInfo($condition);
        if (!is_array($info) || count($info)<0){
            showMessage('记录不存在或已付款','index.php?act=distri_cash&op=cash_list','','error');
        }

        //查询用户信息
        $model_member = Model('member');
        $member_info = $model_member->getMemberInfo(array('member_id'=>$info['tradc_member_id']));

        $update = array();
        $admininfo = $this->getAdminInfo();
        $update['tradc_payment_state'] = 1;
        $update['tradc_payment_admin'] = $admininfo['name'];
        $update['tradc_payment_time'] = TIMESTAMP;
        $log_msg = '佣金提现付款完成，提现单号：'.$info['tradc_sn'];

        try {
            $model_trad->beginTransaction();
            $result = $model_trad->updateDistriTradCash($update,$condition);
            if (!$result) {
                throw new Exception('付款失败');
            }
            //扣除冻结的预存款
            $data = array();
            $data['member_id'] = $member_info['member_id'];
            $data['member_name'] = $member_info['member_name'];
            $data['amount'] = $info['tradc_amount'];
            $data['order_sn'] = $info['tradc_sn'];
            $data['admin_name'] = $admininfo['name'];
            $model_trad->changeDirtriTrad('cash_pay',$data);
            $model_trad->commit();
            $this->log($log_msg,1);
            showMessage('付款成功','index.php?act=distri_cash&op=cash_list');
        } catch (Exception $e) {
            $model_trad->rollback();
            $this->log($log_msg,0);
            showMessage($e->getMessage(),'index.php?act=distri_cash&op=cash_list','html','error');
        }
    }
    /**
     * 更改提现为拒绝状态
     */
    public function cash_noOp(){
        $id = intval($_POST['id']);

        if ($id <= 0){
            showMessage('参数错误','index.php?act=distri_cash&op=cash_list','','error');
        }
        $model_trad = Model('dis_trad');
        $condition = array();
        $condition['tradc_id'] = $id;
        $condition['tradc_payment_state'] = 0;
        $info = $model_trad->getDistriTradCashInfo($condition);
        if (!is_array($info) || count($info) < 0){
            showMessage('记录不存在或已付款','index.php?act=distri_cash&op=cash_list','','error');
        }

        //查询用户信息
        $model_member = Model('member');
        $member_info = $model_member->getMemberInfo(array('member_id'=>$info['tradc_member_id']));
        $update = array();
        $admininfo = $this->getAdminInfo();
        $update['tradc_payment_state'] = 2;
        $update['tradc_payment_admin'] = $admininfo['name'];
        $update['tradc_payment_time'] = TIMESTAMP;
        $update['tradc_content'] = $_POST['tradc_content'];
        $log_msg = '佣金提现付款拒绝，提现单号：'.$info['tradc_sn'];

        try {
            $model_trad->beginTransaction();
            $result = $model_trad->updateDistriTradCash($update,$condition);
            if (!$result) {
                echo json_encode(['code'=>400,'msg'=>'拒绝失败']);exit;
            }
            //扣除冻结的预存款
            $data = array();
            $data['member_id'] = $member_info['member_id']?:0;
            $data['member_name'] = $member_info['member_name']?:'';
            $data['amount'] = $info['tradc_amount'];
            $data['order_sn'] = $info['tradc_sn'];
            $data['admin_name'] = $admininfo['name'];
            $model_trad->changeDirtriTrad('cash_no',$data);
            $model_trad->commit();
            $this->log($log_msg,1);
            echo json_encode(['code'=>200,'msg'=>'拒绝成功']);exit;
        } catch (Exception $e) {
            $model_trad->rollback();
            $this->log($log_msg,0);
            echo json_encode(['code'=>400,'msg'=>$e->getMessage()]);exit;
        }
    }
    /**
     * 查看提现信息
     */
    public function cash_viewOp(){
        $id = intval($_GET['id']);
        $model_trad = Model('dis_trad');
        $condition = array();
        $condition['tradc_id'] = $id;
        $info = $model_trad->getDistriTradCashInfo($condition);
        $info['after_tradc_amount'] = $info['type'] == 1?bcmul($info['tradc_amount'],'0.92',2):$info['tradc_amount'];//税后提现金额
        Tpl::output('info',$info);
        Tpl::showpage('distri_cash.view', 'null_layout');
    }

    /**
     * 导出预存款提现记录
     *
     */
    public function export_cash_step1Op(){
        $condition = array();
        $if_start_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['stime']);
        $if_end_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['etime']);
        $start_unixtime = $if_start_date ? strtotime($_GET['stime']) : null;
        $end_unixtime = $if_end_date ? strtotime($_GET['etime']): null;
        if ($start_unixtime || $end_unixtime) {
            $condition['tradc_add_time'] = array('time',array($start_unixtime,$end_unixtime));
        }
        if (!empty($_GET['member_name'])){
            $condition['tradc_member_name'] = array('like', '%' . $_GET['member_name'] . '%');
        }
        if (!empty($_GET['member_id'])){
            $condition['tradc_member_id'] = array('like', '%' . $_GET['member_id'] . '%');
        }
        if (!empty($_GET['user_name'])){
            $condition['tradc_bank_user'] = array('like', '%' . $_GET['user_name'] . '%');
        }
        if ($_GET['payment_state'] != ''){
            $condition['tradc_payment_state'] = $_GET['payment_state'];
        }
        if ($_GET['id'] != '') {
            $id_array = explode(',', $_GET['id']);
            $condition['tradc_id'] = array('in', $id_array);
        }

        if ($_GET['query'] != '') {
            $condition[$_GET['qtype']] = array('like', '%' . $_GET['query'] . '%');
        }

        if (isset($_GET['dis_tri_state']) &&$_GET['dis_tri_state'] ==1) {
            $condition['dis_trad_cash.chain_id'] =['gt',0];
        }elseif(isset($_GET['dis_tri_state']) &&$_GET['dis_tri_state'] == 2){
            $condition['dis_trad_cash.chain_id'] = 0;
        }
        $order = '';
        $param = array('tradc_id', 'tradc_sn', 'tradc_member_id', 'tradc_member_name', 'tradc_amount', 'tradc_add_time',
            'tradc_bank_name', 'tradc_bank_no','tradc_bank_user','tradc_payment_state','tradc_payment_time','tradc_payment_admin'
        );
        if (in_array($_GET['sortname'], $param) && in_array($_GET['sortorder'], array('asc', 'desc'))) {
            $order = $_GET['sortname'] . ' ' . $_GET['sortorder'];
        }
        $model_trad = Model('dis_trad');
        $on = 'dis_trad_cash.tradc_member_id=member.member_id,dis_trad_cash.chain_id = chain.chain_id';
        $field = 'dis_trad_cash.*,member.member_id,member.distri_chainid,chain.account_id,chain.chain_name,member.bill_bank_branch';

        if (!is_numeric($_GET['curpage'])){
            $count = $model_trad->getMemberDistriCashCount($condition, $field,$on);
            $array = array();
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=distri_cash&op=cash_list');
                Tpl::showpage('export.excel');
            }else{  //如果数量小，直接下载

                $data = $model_trad->getDistriTradCashNewList($condition,$field,$on,'',$order,self::EXPORT_SIZE);

                $cashpaystate = array(0=>'未打款',1=>'已打款',2=>'已拒绝');
                $member_id_arr = [];
                foreach ($data as $k=>$v) {
                    $member_id_arr[] = $v['tradc_member_id'];
                    $data[$k]['tradc_payment_state'] = $cashpaystate[$v['tradc_payment_state']];
                }
                $on = 'member.distri_chainid = chain.chain_id,chain.region_id = chain_region.region_id,chain.area_id_2=area.area_id,chain.chain_brand_id = chain_brand.chain_brand_id';
                $where = [
                    'member.member_id'=>['in',array_unique($member_id_arr)],
                ];
                $member_chain_list = Model()->table('member,chain,chain_region,area,chain_brand')->field('member.member_id,member.member_mobile,chain.account_id,chain.chain_name,chain_region.region_name,area.area_name,chain_brand.brand_name,member.member_identity')->join('left')->on($on)->where($where)->key('member_id')->limit(false)->select();

                $this->createCashExcel($data, $member_chain_list);
            }
        }else{  //下载
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $data = $model_trad->getDistriTradCashNewList($condition,$field,$on,'',$order,"{$limit1},{$limit2}");
            $cashpaystate = array(0=>'未打款',1=>'已打款',2=>'已拒绝');
            foreach ($data as $k=>$v) {
                $member_id_arr[] = $v['tradc_member_id'];
                $data[$k]['tradc_payment_state'] = $cashpaystate[$v['tradc_payment_state']];
            }
            $on = 'member.distri_chainid = chain.chain_id,chain.region_id = chain_region.region_id,chain.area_id_2=area.area_id,chain.chain_brand_id = chain_brand.chain_brand_id';
            $where = [
                'member.member_id'=>['in',array_unique($member_id_arr)],
            ];

            $member_chain_list = Model()->table('member,chain,chain_region,area,chain_brand')->field('member.member_id,member.member_mobile,chain.account_id,chain.chain_name,chain_region.region_name,area.area_name,chain_brand.brand_name,member.member_identity')->join('left')->on($on)->where($where)->key('member_id')->limit(false)->select();

            $this->createCashExcel($data, $member_chain_list);
        }
    }

    /**
     * 生成导出预存款提现excel
     *
     * @param array $data
     */
    private function createCashExcel($data = array(), $member_chain_list){
        Language::read('export');
        import('libraries.excel');
        $excel_obj = new Excel();
        $excel_data = array();
        //设置样式
        $excel_obj->setStyle(array('id'=>'s_title','Font'=>array('FontName'=>'宋体','Size'=>'12','Bold'=>'1')));
        //header
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'提现ID');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'提现编号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'提现用户编号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'提现用户名');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'税前提现金额（元）');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'税后提现金（元）');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'代缴个税金额（元）');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'申请时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'提现用户手机号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'收款银行');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'收款账号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'开户姓名');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'收款银行支行');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'分销员标识');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'用户身份证');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'门店财务编码');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'归属门店');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'门店品牌');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'门店城市');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'门店大区');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'付款状态');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'支付时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'管理员');
        foreach ((array)$data as $k=>$v){
            $tmp = array();
            $tmp[] = array('data'=>$v['tradc_id']);
            $tmp[] = array('data'=>$v['tradc_sn']);
            $tmp[] = array('data'=>$v['tradc_member_id']);
            $tmp[] = array('data'=>$v['tradc_member_name']);
            $tmp[] = array('format'=>'Number','data'=>ncPriceFormat($v['tradc_amount']));
            $tmp[] = array('format'=>'Number','data'=>$v['type'] == 1 ? bcmul(ncPriceFormat($v['tradc_amount']),'0.92',2) : $v['tradc_amount']);
            //代缴个税金额
            $tmp[] = array('format'=>'Number','data'=>$v['type'] == 1 ? bcsub(ncPriceFormat($v['tradc_amount']),bcmul(ncPriceFormat($v['tradc_amount']),'0.92',2),2) : 0);
            $tmp[] = array('data'=>date('Y-m-d H:i:s',$v['tradc_add_time']));
            $tmp[] = array('data'=>$member_chain_list[$v['tradc_member_id']]['member_mobile']);
            $tmp[] = array('data'=>$v['tradc_bank_name']);
            $tmp[] = array('data'=>$v['tradc_bank_no']);
            $tmp[] = array('data'=>$v['tradc_bank_user']);
            $tmp[] = array('data'=>$v['bill_bank_branch']);
            $tmp[] = array('data'=>$v['chain_name']?'内部员工':'外部代理人');
            $tmp[] = array('data'=>$member_chain_list[$v['tradc_member_id']]['member_identity']);
            $tmp[] = array('data'=>$v['account_id']);
            $tmp[] = array('data'=>$v['chain_name']);
            $tmp[] = array('data'=>$member_chain_list[$v['tradc_member_id']]['brand_name']);
            $tmp[] = array('data'=>$member_chain_list[$v['tradc_member_id']]['area_name']);
            $tmp[] = array('data'=>$member_chain_list[$v['tradc_member_id']]['region_name']);
            $tmp[] = array('data'=>$v['tradc_payment_state']);
            $tmp[] = array('data'=>date('Y-m-d H:i:s',$v['tradc_payment_time']));
            $tmp[] = array('data'=>$v['tradc_payment_admin']);
            $excel_data[] = $tmp;
        }
        $excel_data = $excel_obj->charset($excel_data,CHARSET);
        $excel_obj->addArray($excel_data);
        $excel_obj->addWorksheet($excel_obj->charset('分销佣金提现',CHARSET));
        $excel_obj->generateXML($excel_obj->charset('分销佣金提现',CHARSET).$_GET['curpage'].'-'.date('Y-m-d-H',time()));
    }


    /**
     * 获取提现列表xml
     */
    public function get_xmlOp(){
        $model_trad = Model('dis_trad');
        $condition = array();
        $if_start_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['stime']);
        $if_end_date = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['etime']);
        $start_unixtime = $if_start_date ? strtotime($_GET['stime']) : null;
        $end_unixtime = $if_end_date ? strtotime($_GET['etime']): null;
        if ($start_unixtime || $end_unixtime) {
            $condition['tradc_add_time'] = array('time',array($start_unixtime,$end_unixtime));
        }
        if (!empty($_GET['member_name'])){
            $condition['tradc_member_name'] = array('like', '%' . $_GET['member_name'] . '%');
        }
        if (!empty($_GET['member_id'])){
            $condition['tradc_member_id'] = array('like', '%' . $_GET['member_id'] . '%');
        }
        if (!empty($_GET['user_name'])){
            $condition['tradc_bank_user'] = array('like', '%' . $_GET['user_name'] . '%');
        }
        if ($_GET['payment_state'] != ''){
            $condition['tradc_payment_state'] = $_GET['payment_state'];
        }
        if ($_POST['query'] != '') {
            $condition[$_POST['qtype']] = array('like', '%' . $_POST['query'] . '%');
        }
        if (!empty($_GET['dis_tri_state']) && $_GET['dis_tri_state'] == 1) {
            $condition['dis_trad_cash.chain_id'] =['gt',0];
        }elseif(!empty($_GET['dis_tri_state']) && $_GET['dis_tri_state'] == 2){
            $condition['dis_trad_cash.chain_id'] = 0;
        }

        $order = '';
        $param = array('tradc_id', 'tradc_sn', 'tradc_member_id', 'tradc_member_name', 'tradc_amount', 'tradc_add_time',
            'tradc_bank_name', 'tradc_bank_no','tradc_bank_user','tradc_payment_state','tradc_payment_time','tradc_payment_admin'
        );
        if (in_array($_POST['sortname'], $param) && in_array($_POST['sortorder'], array('asc', 'desc'))) {
            $order = $_POST['sortname'] . ' ' . $_POST['sortorder'];
        }
        $on = 'dis_trad_cash.tradc_member_id=member.member_id,dis_trad_cash.chain_id = chain.chain_id';
        $field = 'dis_trad_cash.*,member.member_id,member.distri_chainid,chain.chain_name,member.bill_bank_branch';
        $page = $_POST['rp'];

        $cash_list = $model_trad->getDistriTradCashNewList($condition,$field,$on,$page,$order);

        $data = array();
        $data['now_page'] = $model_trad->shownowpage();
        $data['total_num'] = $model_trad->gettotalnum();
        foreach ($cash_list as $value) {
            $param = array();
            $param['operation'] = "";
            if ($value['tradc_payment_state'] == 0) {
                $param['operation'] .= "<a class='btn red' href=\"javascript:void(0)\" onclick=\"fg_delete('" . $value['tradc_id'] . "')\"><i class='fa fa-trash-o'></i>删除</a>";
            }
            $param['operation'] .= "<a class='btn green' href='javascript:void(0)' onclick=\"ajax_form('cash_info','查看提现编号“". $value['tradc_sn'] ."”的明细', 'index.php?act=distri_cash&op=cash_view&id=". $value['tradc_id'] ."', 840)\" ><i class='fa fa-list-alt'></i>查看</a>";
            $param['tradc_id'] = $value['tradc_id'];
            $param['tradc_sn'] = $value['tradc_sn'];
            $param['tradc_member_id'] = $value['tradc_member_id'];
            $param['tradc_member_name'] = "<img src=".getMemberAvatarForID($value['tradc_member_id'])." class='user-avatar' onMouseOut='toolTip()' onMouseOver='toolTip(\"<img src=".getMemberAvatarForID($value['tradc_member_id']).">\")'>" .$value['tradc_member_name'];
            $param['tradc_amount'] = ncPriceFormat($value['tradc_amount']);//税前提现金额
            $param['after_tradc_amount'] = $value['type'] == 1 ? bcmul(ncPriceFormat($value['tradc_amount']),'0.92',2) : $param['tradc_amount'];//税后提现金额
            $param['payroll_tax'] = $value['type'] == 1 ? bcsub(ncPriceFormat($value['tradc_amount']),$param['after_tradc_amount'],2) : 0;//代缴个税金额
            $param['tradc_add_time'] = date('Y-m-d', $value['tradc_add_time']);
            $param['tradc_bank_name'] = $value['tradc_bank_name'];
            $param['tradc_bank_no'] = $value['tradc_bank_no'];
            $param['tradc_bank_user'] = $value['tradc_bank_user'];
            $param['dis_name'] = $value['chain_id']>0?'内部员工':'外部代理人';
            $param['chain_name'] = $value['chain_name'];
            $param['bill_bank_branch'] = $value['bill_bank_branch'];

            if($value['tradc_payment_state']==0){
                $param['tradc_payment_state'] = '未打款';
            }elseif($value['tradc_payment_state']==1){
                $param['tradc_payment_state'] = '已打款';
            }else{
                $param['tradc_payment_state'] = '已拒绝';
            }
            $param['tradc_payment_time'] = $value['tradc_payment_time'] > 0 ? date('Y-m-d', $value['tradc_payment_time']) : '';
            $param['tradc_payment_admin'] = $value['tradc_payment_admin'];
            $data['list'][$value['tradc_id']] = $param;
        }
        echo Tpl::flexigridXML($data);exit();

    }
    public function cash_exportOp(){

        Tpl::output('top_link', $this->sublink($this->_links, 'cash_export'));
        Tpl::showpage('distri_cash.export');
    }
    /**
     * 分类导出
     */
    public function cash_tem_exportOp($excel2007=true){
        require_once  BASE_ROOT_PATH.'/vendor/phpoffice/phpexcel/Classes/PHPExcel.php';
        require_once  BASE_ROOT_PATH.'/vendor/phpoffice/phpexcel/Classes/PHPExcel/Writer/Excel2007.php';


        $indexKey=['提现ID','提现编号'];
        if(empty($filename)) $filename = '已打款提现模版'.time();
        if( !is_array($indexKey)) return false;

        $header_arr = array('A','B','C','D','E','F','G','H','I','J','K','L','M', 'N','O','P','Q','R','S','T','U','V','W','X','Y','Z');

        //初始化PHPExcel()
        $objPHPExcel = new PHPExcel();

        //设置保存版本格式
        if($excel2007){
            $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
            $filename = $filename.'.xlsx';
        }else{
            $objWriter = new PHPExcel_Writer_Excel5($objPHPExcel);
            $filename = $filename.'.xls';
        }

        //接下来就是写数据到表格里面去
        $objActSheet = $objPHPExcel->getActiveSheet();
        $startRow = 1;

        for ($i=0; $i < count($indexKey); $i++) {

            $objActSheet->setCellValue($header_arr[$i].$startRow,$indexKey[$i]);
        }


        // 下载这个表格，在浏览器输出
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");;
        header('Content-Disposition:attachment;filename='.$filename.'');
        header("Content-Transfer-Encoding:binary");
        $objWriter->save('php://output');

    }

    /**
     * 批量导入提现成功记录
     */
    public function cash_importOp(){
        require_once  BASE_ROOT_PATH.'/vendor/phpoffice/phpexcel/Classes/PHPExcel/IOFactory.php';
        //得到导入文件后缀名
        if (!empty($_FILES['cash_export']['name'])){
            $tmp = $_FILES['cash_export']['tmp_name'];
            $imageName = "25220_".date("His",time())."_".rand(1111,9999).'.xlsx';
            $path= BASE_UPLOAD_PATH.DS.ATTACH_DISTRI.DS;
            if(move_uploaded_file($tmp,$path.DS.$imageName)){
                $imageSrc= $path."/". $imageName;
            }else{
                echo 'error';exit;
            }
        }else{
            showDialog('文件错误', 'reload');
        }
        $objPHPExcelReader = PHPExcel_IOFactory::load($imageSrc);

        $reader = $objPHPExcelReader->getWorksheetIterator();

        //循环读取sheet
        foreach($reader as $sheet) {
            //读取表内容
            $content = $sheet->getRowIterator();
            //逐行处理
            $res_arr = array();
            foreach($content as $key => $items) {
                $rows = $items->getRowIndex();
                //行
                $columns = $items->getCellIterator();
                //列
                $row_arr = array();
                //确定从哪一行开始读取
                if($rows < 2){
                    continue;
                }
                //逐列读取
                foreach($columns as $head => $cell) {
                    //获取cell中数据
                    $data = $cell->getValue();
                    $row_arr[] = $data;
                }
                $res_arr[] = $row_arr;
            }
        }
        if(!empty($res_arr)){
            $cash_id_arr=[];
            foreach ($res_arr as $value){
                $cash_id_arr[] = $value[0];
            }
        }
        $this->edit_trad($cash_id_arr);
    }

    public function edit_trad($cash_id_arr){
        $model_trad = Model('dis_trad');
        $condition = [
            'tradc_id'=>['in',$cash_id_arr],
            'tradc_payment_state'=>0
        ];

        //查询用户信息
        $trad_list = $model_trad->getDistriTradCashList($condition,'*',$page = 0, $order = 'tradc_id desc', $limit = false);

        if(empty($trad_list)){
            showMessage('提现记录无效','index.php?act=distri_cash&op=cash_export','html','error');
        }
        $update = array();
        $admininfo = $this->getAdminInfo();
        $update['tradc_payment_state'] = 1;
        $update['tradc_payment_admin'] = $admininfo['name'];
        $update['tradc_payment_time'] = TIMESTAMP;

        try {
            $model_trad->beginTransaction();
            foreach ($trad_list as $value){
                $condition = array();
                $condition['tradc_id'] = $value['tradc_id'];

                $log_msg = '佣金提现付款完成，提现单号：'.$value['tradc_sn'];
                $result = $model_trad->updateDistriTradCash($update,$condition);
                if (!$result) {
                    throw new Exception('付款失败');
                }
                //扣除冻结的预存款
                $data = array();
                $data['member_id'] = $value['tradc_member_id'];
                $data['member_name'] = $value['tradc_member_name'];
                $data['amount'] = $value['tradc_amount'];
                $data['order_sn'] = $value['tradc_sn'];
                $data['admin_name'] = $admininfo['name'];

                $model_trad->changeDirtriTrad('cash_pay',$data);

                $this->log($log_msg,1);
            }
            $model_trad->commit();
            showDialog('批量打款成功','index.php?act=distri_cash&op=cash_list');
        } catch (Exception $e) {
            $model_trad->rollback();
            $this->log($log_msg,0);
            showMessage($e->getMessage(),'index.php?act=distri_cash&op=cash_export','html','error');
        }
    }

}