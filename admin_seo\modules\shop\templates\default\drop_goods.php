<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3><?php echo $output['top_title'];?></h3>
                <h5><?php echo $output['top_desc'];?></h5>
            </div>
            <?php echo $output['top_link'];?>
        </div>
    </div>
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
            <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
            <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span>
        </div>
        <ul>
            <li>针对分类同步无法正常显示的，可以删除商品后，重新认领，切记不要随便删除商品！！！</li>
        </ul>
    </div>
    <div id="flexigrid"></div>
    <form id="add_form" method="post" enctype="multipart/form-data" action="index.php?act=goods_manage_handel&op=drop_goods">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="sku_no">商品货号：</label>
                </dt>
                <dd class="opt">
                    <input name="sku_no" id="sku_no" value="" type="text" class="input-txt">
                    <span class="err"></span>
                    <p class="error">请确认右上角的主体后，再操作删除，否则会删除其它主体下的商品！！！</p>
                </dd>
            </dl>
            <div class="bot"><a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="btn_add"><?php echo $lang['nc_submit'];?></a></div>
        </div>
    </form>
</div>
<script type="text/javascript">
    //
    $(document).ready(function(){
        //添加按钮的单击事件
        $("#btn_add").click(function(){
            $("#add_form").submit();
        });
        //页面输入内容验证
        $("#add_form").validate({
            errorPlacement: function(error, element){
                var error_td = element.parent('dd').children('span.err');
                error_td.append(error);
            },
            rules : {
                sku_no: {
                    required : true,
                }
            },
            messages : {
                sku_no: {
                    required : '<i class="fa fa-exclamation-circle"></i>货号必填',
                }
            }
        });
    });
</script>
