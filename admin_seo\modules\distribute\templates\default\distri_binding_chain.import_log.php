<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title"> <a class="back" href="index.php?act=distri_binding_chain&op=batch_import" title="返回列表"> <i class="fa fa-arrow-circle-o-left"></i> </a>
            <div class="subject">
                <h3>操作 - 批量导入“<?php echo $output['pintuan_info']['pintuan_name'];?>”</h3>
                <h5>查看导入数据</h5>
            </div>
        </div>
    </div>
    <!-- 操作说明 -->
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
            <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
            <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
        <ul>
            <li>绑定客服和门店的关系，是为了把所绑定门店的爱省钱账号里的佣金，默认分1/10给客服。不绑定或解绑，佣金都不会分1/10给客服。</li>
            <li>新增的分销员为内部分销员，且已经在阿闻爱省钱认证内部分销员并审核通过。此处只显示生效中的绑定关系。历史绑定过，当前已不生效的关系，请查看历史记录。</li>
        </ul>
    </div>
    <div id="flexigrid"></div>
    <div class="ncap-search-ban-s" id="searchBarOpen"><i class="fa fa-search-plus"></i>高级搜索</div>
    <div class="ncap-search-bar">
        <div class="handle-btn" id="searchBarClose"><i class="fa fa-search-minus"></i>收起边栏</div>
        <div class="title">
            <h3>高级搜索</h3>
        </div>
        <form method="get" name="formSearch" id="formSearch">
            <div id="searchCon" class="content">
                <div class="layout-box">

                    <dl>
                        <dt>分销认证来源</dt>
                        <dd>
                            <label>
                                <select name="distribute_state" class="s-select">
                                    <option value="">请选择</option>
                                    <option value="1">内部分销员</option>
                                    <option value="2">外部代理人</option>

                                </select>
                            </label>
                        </dd>
                    </dl>

                </div>
            </div>
            <div class="bottom"> <a href="javascript:void(0);" id="ncsubmit" class="ncap-btn ncap-btn-green mr5">提交查询</a><a href="javascript:void(0);" id="ncreset" class="ncap-btn ncap-btn-orange" title="撤销查询结果，还原列表项所有内容"><i class="fa fa-retweet"></i><?php echo $lang['nc_cancel_search'];?></a></div>
        </form>
    </div>
</div>
<script type="text/javascript">
    $(function(){

        // 高级搜索提交
        $('#ncsubmit').click(function(){
            $("#flexigrid").flexOptions({url: 'index.php?act=distri_binding_chain&op=import_log&'+$("#formSearch").serialize(),query:'',qtype:''}).flexReload();
        });
        // 高级搜索重置
        $('#ncreset').click(function(){
            $("#flexigrid").flexOptions({url: 'index.php?act=distri_binding_chain&op=import_log'}).flexReload();
            $("#formSearch")[0].reset();
        });
        $("#flexigrid").flexigrid({
            url: 'index.php?act=distri_binding_chain&op=import_log',
            colModel : [
                {display: '导入时间', name : 'create_time', width : 200, sortable : true, align: 'center'},
                {display: '导出结果', name : 'content', width : 200, sortable : true, align: 'center'},
                {display: '导出记录', name : 'result', width : 200, sortable : true, align: 'center'},
            ],
        });
    });

    function fg_csv(id) {
        window.location.href = $("#flexigrid").flexSimpleSearchQueryString()+'&op=import_log_xls&id='+id;
    }
</script>

