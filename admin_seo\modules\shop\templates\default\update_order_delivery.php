<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3><?php echo $output['top_title'];?></h3>
                <h5><?php echo $output['top_desc'];?></h5>
            </div>
            <ul class="tab-base nc-row">
                <?php echo $output['top_tab'];?>
            </ul>
        </div>
    </div>
    <form id="add_form" method="post" enctype="multipart/form-data" action="index.php?act=order_delay_delivery&op=updateOrderDelivery">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="order_sn">订单号：</label>
                </dt>
                <dd class="opt">
                    <input name="order_sn" id="order_sn" value="" type="text" class="input-txt">
                    <span class="err"></span>
                </dd>
            </dl>
            <div class="bot"><a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="btn_add"><?php echo $lang['nc_submit'];?></a></div>
        </div>
    </form>
</div>
<script type="text/javascript">
    $(document).ready(function(){
        //添加按钮的单击事件
        $("#btn_add").click(function(){
            $("#add_form").submit();
        });
        //页面输入内容验证
        $("#add_form").validate({
            errorPlacement: function(error, element){
                var error_td = element.parent('dd').children('span.err');
                error_td.append(error);
            },
            rules : {
                order_sn: {
                    required : true,
                    digits : true
                }
            },
            messages : {
                order_sn: {
                    required : '<i class="fa fa-exclamation-circle"></i>订单号必填',
                    digits : '<i class="fa fa-exclamation-circle"></i>订单号只能为数字'
                }
            }
        });
    });
</script>
