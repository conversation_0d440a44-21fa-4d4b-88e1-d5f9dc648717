<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>客服绑定门店</h3>
                <h5>总部客服绑定门店管理</h5>
            </div>
            <?php echo $output['top_link'];?>
        </div>
    </div>
    <!-- 操作说明 -->
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
            <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
            <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
        <ul>
            <li>绑定客服和门店的关系，是为了把所绑定门店的爱省钱账号里的佣金，默认分1/10给客服。不绑定或解绑，佣金都不会分1/10给客服。</li>
            <li>新增的分销员为内部分销员，且已经在阿闻爱省钱认证内部分销员并审核通过。此处只显示生效中的绑定关系。历史绑定过，当前已不生效的关系，请查看历史记录。</li>
            <li>客服绑定的门店爱省钱账号解绑或清退，则客服和门店的绑定关系也自动解绑。同理，客服的爱省钱账号解绑或清退，则客服和门店的绑定关系也自动解绑。</li>
        </ul>
    </div>
    <div id="flexigrid"></div>
    <div class="ncap-search-ban-s" id="searchBarOpen"><i class="fa fa-search-plus"></i>高级搜索</div>
    <div class="ncap-search-bar">
        <div class="handle-btn" id="searchBarClose"><i class="fa fa-search-minus"></i>收起边栏</div>
        <div class="title">
            <h3>高级搜索</h3>
        </div>
        <form method="get" name="formSearch" id="formSearch">
            <div id="searchCon" class="content">
                <div class="layout-box">

                    <dl>
                        <dt>分销认证来源</dt>
                        <dd>
                            <label>
                                <select name="distribute_state" class="s-select">
                                    <option value="">请选择</option>
                                    <option value="1">内部分销员</option>
                                    <option value="2">外部代理人</option>

                                </select>
                            </label>
                        </dd>
                    </dl>

                </div>
            </div>
            <div class="bottom"> <a href="javascript:void(0);" id="ncsubmit" class="ncap-btn ncap-btn-green mr5">提交查询</a><a href="javascript:void(0);" id="ncreset" class="ncap-btn ncap-btn-orange" title="撤销查询结果，还原列表项所有内容"><i class="fa fa-retweet"></i><?php echo $lang['nc_cancel_search'];?></a></div>
        </form>
    </div>
</div>
<script type="text/javascript">
    $(function(){

        // 高级搜索提交
        $('#ncsubmit').click(function(){
            $("#flexigrid").flexOptions({url: 'index.php?act=distri_binding_chain&op=get_xml&'+$("#formSearch").serialize(),query:'',qtype:''}).flexReload();
        });
        // 高级搜索重置
        $('#ncreset').click(function(){
            $("#flexigrid").flexOptions({url: 'index.php?act=distri_binding_chain&op=get_xml'}).flexReload();
            $("#formSearch")[0].reset();
        });
        $("#flexigrid").flexigrid({
            url: 'index.php?act=distri_binding_chain&op=get_xml',
            colModel : [
                {display: '操作', name : 'operation', width : 200, sortable : false, align: 'center'},
                {display: '分销员ID', name : 'member_id', width : 80, sortable : true, align: 'center'},
                {display: '姓名', name : 'member_name', width : 150, sortable : true, align: 'center'},
                {display: '分销员手机', name : 'member_mobile', width : 100, sortable : true, align: 'center'},
                {display: '客服提现比例', name : 'cash_ratio', width : 100, sortable : true, align: 'center'},
                {display: '门店名称', name : 'chain_name', width : 200, sortable : true, align: 'center'},
                {display: '财务编码', name : 'account_id', width : 80, sortable : true, align: 'center'},
                {display: '门店手机号', name : 'chain_member_mobile', width : 200, sortable : true, align: 'center'},
                {display: '绑定时间', name : 'binding_time', width : 120, sortable : true, align: 'center'}
            ],
            buttons : [
                {display: '<i class="fa fa-file-excel-o"></i>导出数据', name : 'csv', bclass : 'csv', title : '将选定行数据导出CSV文件', onpress : fg_operation},
                {display: '<i class="fa fa-check-circle-o"></i>新增', name : 'add', bclass : 'add', title : '新增', onpress : fg_operation},
                {display: '<i class="fa fa-check-circle-o"></i>批量导入', name : 'import', bclass : 'import', title : '批量导入', onpress : fg_operation}
            ],
            searchitems : [
                {display: '分销员ID', name : 'member_id'},
                {display: '分销员名称', name : 'bill_user_name'},
                {display: '分销员手机号', name : 'member_mobile'},
                {display: '门店编码', name : 'account_id'},
                {display: '门店名称', name : 'chain_name'}
            ],

            sortname: "binding_time",
            sortorder: "desc",
            title: '客服绑定门店列表'
        });

    });

    function fg_operation(name, bDiv) {
        if (name == 'csv') {
            if ($('.trSelected', bDiv).length == 0) {
                if (!confirm('您确定要下载全部数据吗？')) {
                    return false;
                }
            }
            var itemids = new Array();
            $('.trSelected', bDiv).each(function(i){
                itemids[i] = $(this).attr('data-id');
            });
            fg_csv(itemids);
        }else if(name == 'add'){
            location.href = 'index.php?act=distri_binding_chain&op=bind_add';
        }else if(name == 'import'){
            location.href = 'index.php?act=distri_binding_chain&op=batch_import';
        }
    }

    function fg_csv(ids) {
        id = ids.join(',');
        window.location.href = $("#flexigrid").flexSimpleSearchQueryString()+'&op=get_xml&export=1&id=' + id;
    }

    //解绑
    function fg_unbind(id) {
        if(confirm('确认解绑吗？')){
            $.getJSON('index.php?act=distri_binding_chain&op=chain_unbind', {id:id}, function(data){
                if (data.state) {
                    $("#flexigrid").flexReload();
                } else {
                    showError(data.msg);
                }
            });
        }
    }
</script>

