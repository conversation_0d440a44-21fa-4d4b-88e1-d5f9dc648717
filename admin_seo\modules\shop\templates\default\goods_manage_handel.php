<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3><?php echo $output['top_title'];?></h3>
                <h5><?php echo $output['top_desc'];?></h5>
            </div>
            <?php echo $output['top_link'];?>
        </div>
    </div>
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
            <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
            <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span>
        </div>
        <ul>
            <li>分类需以这种格式输入：猫咪专区>猫咪零食>罐头湿粮</li>
        </ul>
    </div>
    <div id="flexigrid"></div>
    <form id="add_form" method="post" enctype="multipart/form-data" action="index.php?act=goods_manage_handel&op=index">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="cate_name">新分类名称：</label>
                </dt>
                <dd class="opt">
                    <input name="cate_name" id="cate_name" value="" type="text" class="input-txt">
                    <span class="err"></span>
                </dd>
                <ul>
                    <li>依据服务器环境支持最大上传组件大小设置选项，如需要上传超大附件需调整服务器环境配置。</li>
                </ul>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="sku_no">商品货号：</label>
                </dt>
                <dd class="opt">
                    <input name="sku_no" id="sku_no" value="" type="text" class="input-txt">
                    <span class="err"></span>
                </dd>
            </dl>
            <div class="bot"><a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="btn_add"><?php echo $lang['nc_submit'];?></a></div>
        </div>
    </form>
</div>
<script type="text/javascript">
    //
    $(document).ready(function(){
        //添加按钮的单击事件
        $("#btn_add").click(function(){
            $("#add_form").submit();
        });
        //页面输入内容验证
        $("#add_form").validate({
            errorPlacement: function(error, element){
                var error_td = element.parent('dd').children('span.err');
                error_td.append(error);
            },
            rules : {
                cate_name: {
                    required : true,
                },
                sku_no: {
                    required : true,
                }
            },
            messages : {
                cate_name: {
                    required : '<i class="fa fa-exclamation-circle"></i>分类名称必填',
                },
                sku_no: {
                    required : '<i class="fa fa-exclamation-circle"></i>货号必填',
                }
            }
        });
    });
</script>
