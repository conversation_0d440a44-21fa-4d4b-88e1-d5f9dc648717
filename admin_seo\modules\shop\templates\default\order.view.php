<?php defined('InShopNC') or exit('Access Invalid!');?>
<style>
.ncm-goods-gift {
	text-align: left;
}
.ncm-goods-gift ul {
    display: inline-block;
    font-size: 0;
    vertical-align: middle;
}
.ncm-goods-gift li {
    display: inline-block;
    letter-spacing: normal;
    margin-right: 4px;
    vertical-align: top;
    word-spacing: normal;
}
.ncm-goods-gift li a {
    background-color: #fff;
    display: table-cell;
    height: 30px;
    line-height: 0;
    overflow: hidden;
    text-align: center;
    vertical-align: middle;
    width: 30px;
}
.ncm-goods-gift li a img {
    max-height: 30px;
    max-width: 30px;
}

.ncsc-order-info {
     font-size: 0;
     *word-spacing: -1px;
     border: solid 1px #DDD;
     position: relative;
     z-index: 2;
 }
.ncsc-order-details {
    background-color: #FBFBFB;
    vertical-align: top;
    letter-spacing: normal;
    word-spacing: normal;
    display: inline-block;
    *display: inline;
    *zoom: 1;
    width: 359px;
    border-right: solid 1px #DDD;
}
.ncsc-order-details .title {
    font-size: 12px;
    font-weight: 600;
    line-height: 20px;
    background-color: #F3F3F3;
    height: 20px;
    padding: 9px;
    border-bottom: solid 1px #DDD;
}
.ncsc-order-details .content {
    display: block;
    width: auto;
    padding: 17px 17px 7px 17px;
}
.ncsc-order-details .content dl, .ncsc-order-contnet .daddress-info {
    font-size: 0;
    *word-spacing: -1px;
    margin-bottom: 10px;
}
.ncap-order-details .tabs-panels dt {
    color: #999;
    width: 100px;
    text-align: right;
}
.ncap-order-details .tabs-panels dl {
    font-size: 0;
    padding-bottom: 5px;
}
.ncsc-order-details .content dl dt {
    color: #888;
    width: 20%;
}
.ncsc-order-details .content dl dd {
    color: #666;
    width: 80%;
}
</style>
<div class="page">
  <div class="fixed-bar">
    <div class="item-title"><a class="back" href="javascript:history.back(-1)" title="返回列表"><i class="fa fa-arrow-circle-o-left"></i></a>
      <div class="subject">
        <h3><?php echo $lang['order_manage'];?></h3>
        <h5><?php echo $lang['order_manage_subhead'];?></h5>
      </div>
    </div>
  </div>
  <div class="ncap-order-style">
    <div class="titile">
      <h3></h3>
    </div>
<div class="ncap-order-flow">

    <?php if ($output['order_info']['order_type'] != 3) { ?>
      <ol class="num5">
        <li class="current">
          <h5>生成订单</h5>
          <i class="fa fa-arrow-circle-right"></i>
          <time><?php echo date('Y-m-d H:i:s',$output['order_info']['add_time']);?></time>
        </li>
        <?php if ($output['order_info']['order_state'] == ORDER_STATE_CANCEL) { ?>
        <li class="current">
          <h5>取消订单</h5>
          <time><?php echo date('Y-m-d H:i:s',$output['order_info']['close_info']['log_time']);?></time>
        </li>
        <?php } else { ?>
        <li class="<?php if(intval($output['order_info']['payment_time']) && $output['order_info']['order_pay_state'] !== false) echo 'current'; ?>">
          <h5>完成付款</h5>
          <i class="fa fa-arrow-circle-right"></i>
          <time><?php echo intval(date('His',$output['order_info']['payment_time'])) ? date('Y-m-d H:i:s',$output['order_info']['payment_time']) : date('Y-m-d',$output['order_info']['payment_time']);?></time>
        </li>
        <li class="<?php if($output['order_info']['extend_order_common']['shipping_time']) echo 'current'; ?>">
          <h5>商家发货</h5>
          <i class="fa fa-arrow-circle-right"></i>
          <time><?php echo $output['order_info']['extend_order_common']['shipping_time'] ? date('Y-m-d H:i:s',$output['order_info']['extend_order_common']['shipping_time']) : null; ?></time>
        </li>
        <li class="<?php if(intval($output['order_info']['finnshed_time'])) { ?>current<?php } ?>">
          <h5>收货确认</h5>
          <i class="fa fa-arrow-circle-right"></i>
          <time><?php echo $output['order_info']['finnshed_time'] ? date('Y-m-d H:i:s',$output['order_info']['finnshed_time']) : null;?></time>
        </li>
        <li class="<?php if($output['order_info']['evaluation_state'] == 1) { ?>current<?php } ?>">
          <h5>完成评价</h5>
          <time><?php echo $output['order_info']['extend_order_common']['evaluation_time'] ? date("Y-m-d H:i:s",$output['order_info']['extend_order_common']['evaluation_time']) : null; ?></time>
        </li>
        <?php } ?>
    </ol>
    <?php } else { ?>
      <ol class="num5">
        <li class="current">
          <h5>生成订单</h5>
          <i class="fa fa-arrow-circle-right"></i>
          <time><?php echo date('Y-m-d H:i:s',$output['order_info']['add_time']);?></time>
        </li>
        <?php if ($output['order_info']['order_state'] == ORDER_STATE_CANCEL) { ?>
        <li class="current">
          <h5>取消订单</h5>
          <time><?php echo date('Y-m-d H:i:s',$output['order_info']['close_info']['log_time']);?></time>
        </li>
        <?php } ?>
        <?php if($output['order_info']['payment_code'] != 'chain') { ?>
        <li class="<?php if(intval($output['order_info']['payment_time']) && $output['order_info']['order_pay_state'] !== false) echo 'current';?>">
          <h5>完成付款</h5>
          <i class="fa fa-arrow-circle-right"></i>
          <time>
          <?php if ($output['order_info']['payment_time']) { ?>
          <?php echo intval(date('His',$output['order_info']['payment_time'])) ? date('Y-m-d H:i:s',$output['order_info']['payment_time']) : date('Y-m-d',$output['order_info']['payment_time']);?>
          <?php } ?>
          </time>
        </li>
        <li class="<?php if(intval($output['order_info']['finnshed_time'])) { ?>current<?php } ?>">
          <h5>买家取货</h5>
          <i class="fa fa-arrow-circle-right"></i>
          <time><?php echo $output['order_info']['finnshed_time'] ? date('Y-m-d H:i:s',$output['order_info']['finnshed_time']) : null;?></time>
        </li>
        <?php } else { ?>
        <li class="<?php if(intval($output['order_info']['finnshed_time'])) { ?>current<?php } ?>">
          <h5>买家到门店付款取货</h5>
          <i class="fa fa-arrow-circle-right"></i>
          <time><?php echo $output['order_info']['finnshed_time'] ? date('Y-m-d H:i:s',$output['order_info']['finnshed_time']) : null;?></time>
        </li>
        <?php } ?>
        <li class="<?php if($output['order_info']['evaluation_state'] == 1) { ?>current<?php } ?>">
          <h5>完成评价</h5>
          <time><?php echo $output['order_info']['extend_order_common']['evaluation_time'] ? date("Y-m-d H:i:s",$output['order_info']['extend_order_common']['evaluation_time']) : null; ?></time>
        </li>
        
    </ol>
    <?php }?>
    </div>

    <div class="ncap-order-details">
      <ul class="tabs-nav">
        <li class="current"><a href="javascript:void(0);"><?php echo $lang['order_detail'];?></a></li>
        <?php if(is_array($output['refund_list']) && !empty($output['refund_list'])) { ?>
        <li><a href="javascript:void(0);">退款记录</a></li>
        <?php } ?>
        <?php if(is_array($output['return_list']) && !empty($output['return_list'])) { ?>
        <li><a href="javascript:void(0);">退货记录</a></li>
        <?php } ?>
      </ul>
      <div class="tabs-panels">
        <div class="misc-info">
          <h4>下单/支付</h4>
          <dl>
              <?php if($output['order_info']['order_type'] == 9 && $output['order_info']['is_head'] == 0) {
                ?>
                  <dt>子订单号<?php echo $lang['nc_colon'];?></dt>
                  <dd><?php echo $output['order_info']['order_sn'];?><?php if ($output['order_info']['order_type'] == 2) echo '[预定]';?><?php if ($output['order_info']['order_type'] == 3) echo '[门店自提]';?></dd>
                  <dt>主订单号<?php echo $lang['nc_colon'];?></dt>
                  <dd><?php echo $output['parent_order_info']['parent_order_sn'];?></dd>

                  <?php
              } else {
                ?>
                  <dt><?php echo $lang['order_number'];?><?php echo $lang['nc_colon'];?></dt>
                  <dd><?php echo $output['order_info']['order_sn'];?><?php if ($output['order_info']['order_type'] == 2) echo '[预定]';?><?php if ($output['order_info']['order_type'] == 3) echo '[门店自提]';?></dd>
              <?php
              } ?>

            <dt>订单来源<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo orderFromName($output['order_info']['order_from']);?></dd>
            <dt><?php echo $lang['order_time'];?><?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo date('Y-m-d H:i:s',$output['order_info']['add_time']);?></dd>
          </dl>
          <?php if(intval($output['order_info']['payment_time'])){?>
          <dl>
            <dt>支付单号<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $output['order_info']['pay_sn'];?></dd>

              <?php if($output['order_info']['order_type'] == 11) {?>
                  <dt>定金支付方式<?php echo $lang['nc_colon'];?></dt>
                  <dd><?php echo orderPaymentName($output['pre_info']['pre_payment_code']);?></dd>
                  <dt>尾款支付方式<?php echo $lang['nc_colon'];?></dt>
                  <dd><?php echo orderPaymentName($output['pre_info']['last_payment_code']);?></dd>
                  <dt></dt>
                  <dd></dd>

                  <dt>定金支付时间<?php echo $lang['nc_colon'];?></dt>
                  <dd><?php echo intval(date('His',$output['order_info']['payment_time'])) ? date('Y-m-d H:i:s',$output['order_info']['payment_time']) : date('Y-m-d',$output['order_info']['payment_time']);?></dd>
                  <dt>尾款支付时间<?php echo $lang['nc_colon'];?></dt>
                  <dd><?php echo $output['pre_info']['last_pay_time'];?></dd>

              <?php } else { ?>
                  <dt><?php echo $lang['payment'];?><?php echo $lang['nc_colon'];?></dt>
                  <dd><?php echo orderPaymentName($output['order_info']['payment_code']);?></dd>
                  <dt><?php echo $lang['payment_time'];?><?php echo $lang['nc_colon'];?></dt>
                  <dd><?php echo intval(date('His',$output['order_info']['payment_time'])) ? date('Y-m-d H:i:s',$output['order_info']['payment_time']) : date('Y-m-d',$output['order_info']['payment_time']);?></dd>

              <?php }?>

         </dl>
          <?php } else if ($output['order_info']['payment_code'] == 'offline') { ?>
          <dl>
            <dt><?php echo $lang['payment'];?><?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo orderPaymentName($output['order_info']['payment_code']);?></dd>
          </dl>          
          <?php } ?>
          <?php if ($output['order_info']['order_state'] == ORDER_STATE_CANCEL) { ?>
          <dl>
            <dt>订单取消原因：</dt>
            <dd><?php echo $output['order_info']['close_info']['log_role'];?>(<?php echo $output['order_info']['close_info']['log_user'];?>) <?php echo $output['order_info']['close_info']['log_msg'];?></dd>
          </dl>
          <?php }?>
          <?php if ($output['order_info']['order_state'] == ORDER_STATE_PAY) { ?>
          <dl>
            <dt>支付日志：</dt>
            <dd><?php echo $output['order_info']['pay_info']['log_role'];?> <?php echo $output['order_info']['pay_info']['log_msg'];?></dd>
          </dl>
          <?php }?>
          <?php if ($output['order_info']['chain_name']) { ?>
          <dl>
              <dt>所属门店：</dt><dd><?php echo $output['order_info']['chain_name'];?></dd>
              <dt style="width: 120px;">业绩归属-财务编码：</dt><dd><?php echo $output['order_info']['account_id'];?></dd>
          </dl>
          <?php }?>
        </div>
        <div class="addr-note">
          <h4>购买/收货方信息</h4>
          <dl>
            <dt><?php echo $lang['buyer_name'];?><?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $output['order_info']['buyer_name'];?></dd>
            <dt>联系方式<?php echo $lang['nc_colon'];?></dt>
              <dd>
                  <span id="show_phone_<?php echo $output['order_info']['order_id'];?>"><?php echo hideStr(@$output['order_info']['extend_order_common']['reciver_info']['phone']);?></span>
                  <span  class=""><a class="ncap-btn-mini ncap-btn-green" id="showmobile_<?php echo $output['order_info']['order_id'];?>" onclick="hideshow(<?php echo $output['order_info']['order_id'];?>)">显示</a></span>
              </dd>
          </dl>
          <dl>
            <dt>收货地址<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $output['order_info']['extend_order_common']['reciver_name'];?>&nbsp;&nbsp;
                ,&nbsp;<?php echo @$output['order_info']['extend_order_common']['reciver_info']['address'];?>
                <?php if($output['order_info']['extend_order_common']['reciver_info']['dlyp']) { ?>(门店代收费用：<?php echo $output['order_info']['extend_order_common']['reciver_info']['chain_price'];?>)<?php } ?></dd>
          </dl>
          <dl>
            <dt>发票信息<?php echo $lang['nc_colon'];?></dt>
            <dd>
              <?php if (!empty($output['order_info']['extend_order_common']['invoice_info'])) {?>
              <ul>
                <?php foreach ((array)$output['order_info']['extend_order_common']['invoice_info'] as $key => $value){?>
                <li><strong><?php echo $key.$lang['nc_colon'];?></strong><?php echo $value;?></li>
                <?php } ?>
              </ul>
              <?php } ?>
            </dd>
          </dl>
          <dl>
            <dt>买家留言<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $output['order_info']['extend_order_common']['order_message']; ?></dd>
          </dl>
        </div>

        <div class="contact-info">
          <h4>销售/发货方信息</h4>
          <dl>
            <dt><?php echo $lang['store_name'];?><?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $output['order_info']['store_name'];?></dd><dt>店主名称<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $output['store_info']['seller_name'];?></dd>
            <dt>联系电话<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $output['store_info']['store_phone'];?></dd>
          </dl>
          <dl>
            <dt>发货地址<?php echo $lang['nc_colon'];?></dt>
            <?php if (!empty($output['daddress_info'])) {?>
            <dd><?php echo $output['daddress_info']['seller_name']; ?>&nbsp;,&nbsp;<?php echo $output['daddress_info']['telphone'];?>&nbsp;,&nbsp;<?php echo $output['daddress_info']['area_info'];?>&nbsp;<?php echo $output['daddress_info']['address'];?>&nbsp;,&nbsp;<?php echo $output['daddress_info']['company'];?></dd>
            <?php } ?>
          </dl>
          <dl>
            <dt>发货时间<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $output['order_info']['extend_order_common']['shipping_time'] ? date('Y-m-d H:i:s',$output['order_info']['extend_order_common']['shipping_time']) : null; ?></dd>
            <dt>快递公司<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $output['order_info']['express_info']['e_name'];?></dd>
            <dt>物流单号<?php echo $lang['nc_colon'];?></dt>
            <dd>
              <?php if($output['order_info']['shipping_code'] != ''){?>
              <?php echo $output['order_info']['shipping_code'];?>
              <?php }?>
            </dd>
          </dl>
        </div>

        <?php if ($output['order_info']['order_type'] == 2) { ?>
        <div>
        <h4>预定信息</h4>
          <table>
            <tbody>
              <tr>
                <td>阶段</td>
                <td>应付金额</td>
                <td>支付方式</td>
                <td>支付交易号</td>
                <td>支付时间</td>
                <td>备注</td>
              </tr>
              <?php foreach ($output['order_info']['book_list'] as $k => $book_info) { ?>
              <tr>
                <td><?php echo $book_info['book_step'];?></td>
                <td><?php echo $book_info['book_amount'].$book_info['book_amount_ext'];?></td>
                <td><?php echo $book_info['book_pay_name'];?></td>
                <td><?php echo $book_info['book_trade_no'];?></td>
                <td>
                <?php if (!empty($book_info['book_pay_time'])) { ?>
                <?php echo !date('His',$book_info['book_pay_time']) ? date('Y-m-d',$book_info['book_pay_time']) : date('Y-m-d H:i:s',$book_info['book_pay_time']);?>
                <?php } ?>
                </td>
                <td><?php echo $book_info['book_state'];?><?php echo $k == 1 ? '（通知手机号'.hideStr($book_info['book_buyer_phone']).'）' : null;?></td>
                </dd>
              </tr>
              <?php } ?>
          </tbody>
          </table>
        </div>
        <?php } ?>

          <?php if($output['order_info']['order_type'] == 9 && $output['order_info']['is_head'] == 1) {?>
              <h4>周期购信息</h4>
              <dl>
                  <dt>发货间隔：</dt>
                  <dd>每<?php echo $output['order_info']['delivery_info']['delivery_interval']?>天发货一次</dd>
              </dl>
              <dl>
                  <dt>每期发货时间：</dt>
                  <dd>
                      <?php foreach($output['order_info']['delivery_info']['delivery_times'] as $delivery_value) {
                          $style = '';
                          if($delivery_value['state'] == 1) {
                              $style = 'style="color:red;text-align:right"';
                          } else {
                              $style = 'style="text-align:right"';
                          }
                          ?>
                          <dl>
                              <dt <?php echo $style?>><?php echo $delivery_value['date_desc']?></dt>
                              <dd <?php echo $style?>><?php echo $delivery_value['delivery_date']?><?php echo$delivery_value['delivery_info']?></dd>
                          </dl>
                      <?php }?>
                      <dl>
                          <dt></dt>
                          <dd></dd>
                      </dl>
                  </dd>
              </dl>
          <?php } ?>
        <div class="goods-info">
            <?php
                if($output['order_info']['order_type'] == 9 && $output['order_info']['is_head'] == 1) {
                ?>
                    <h4>主订单商品信息<span style="margin-left: 50px;">主订单号：<?php echo $output['order_info']['order_sn']?></span><span style="margin-left: 50px;">交易单号：<?php echo $output['order_info']['trade_no']?></span></h4>
                        <?php
                } else {
                    ?>
                    <h4><?php echo $lang['product_info'];?></h4>
            <?php
                }
            ?>

          <table>
            <thead>
              <tr>
                <th colspan="2">商品</th>
                <th>单价</th>
                <th><?php echo $lang['product_num'];?></th>
                <th>优惠活动</th>
                <th>佣金比例</th>
                <th>收取佣金</th>
              </tr>
            </thead>
            <tbody>
              <?php $i = 0;?>
              <?php if($output['order_info']['goods_list']){ foreach($output['order_info']['goods_list'] as $goods){ ?>
              <?php $i++;?>
              <tr>
                <td class="w30"><div class="goods-thumb"><a href="<?php echo SHOP_SITE_URL;?>/index.php?act=goods&goods_id=<?php echo $goods['goods_id'];?>" target="_blank"><img alt="<?php echo $lang['product_pic'];?>" src="<?php echo thumb($goods, 60);?>" /> </a></div></td>
                <td style="text-align: left;"><a href="<?php echo SHOP_SITE_URL;?>/index.php?act=goods&goods_id=<?php echo $goods['goods_id'];?>" target="_blank"><?php echo $goods['goods_name'];?></a>
                    <span class="rec"><a target="_blank" href="<?php echo urlShop('snapshot', 'index', array('rec_id' => $goods['rec_id']));?>">[交易快照]</a></span>
                    <?php if ($goods['refund'] == 1){?>
                    <a onclick="CUR_DIALOG = ajax_form('add_refund', '<?php echo addslashes($goods['goods_name']);?> 退款', 'index.php?act=order&op=add_refund&order_id=<?php echo $output['order_info']['order_id']; ?>&goods_id=<?php echo $goods['rec_id']; ?>');" href="javascript:void(0);">[退款/退货]</a>
                    <?php } ?>
                    <br/><?php echo $goods['goods_spec'];?></td>
                <td class="w80"><?php echo $lang['currency'].ncPriceFormat($goods['goods_price']);?></td>
                <td class="w60"><?php echo $goods['goods_num'];?></td>
                <td class="w100"><?php echo orderGoodsType($goods['goods_type']); ?></td>
                <td class="w60"><?php echo $goods['commis_rate'] == 200 ? '' : $goods['commis_rate'].'%';?></td>
                <td class="w80"><?php echo $goods['commis_rate'] == 200 ? '' : ncPriceFormat($goods['goods_pay_price']*$goods['commis_rate']/100);?></td>
              </tr>
                <!-- S 赠品列表 -->
                <?php if (!empty($output['order_info']['zengpin_list']) && $i == count($output['order_info']['goods_list'])) { ?>
                <tr>
                  <td>&nbsp;</td>
                  <td colspan="6"><div class="ncm-goods-gift">赠品：
                  <ul><?php foreach($output['order_info']['zengpin_list'] as $zengpin_info) {?>
                  <li><a title="赠品：<?php echo $zengpin_info['goods_name'];?> * <?php echo $zengpin_info['goods_num'];?>" target="_blank" href="<?php echo $zengpin_info['goods_url'];?>"><img src="<?php echo $zengpin_info['image_60_url']; ?>" /></a></li>
                  <?php } ?></ul></div>
                  </td>
                </tr>
                <?php } ?>
                <!-- E 赠品列表 -->
              <?php } }?>
            </tbody>
            <!-- S 促销信息 -->
            <?php $pinfo = $output['order_info']['extend_order_common']['promotion_info'];?>
            <?php if(!empty($pinfo)){ ?>
            <?php $pinfo = unserialize($pinfo);?>
            <tfoot>
              <tr>
                <th colspan="10">其它信息</th>
              </tr>
              <tr>
                <td colspan="10">
              <?php if($pinfo == false){ ?>
              <?php echo $output['order_info']['extend_order_common']['promotion_info'];?>
              <?php }elseif (is_array($pinfo)){ ?>
                  <?php foreach ($pinfo as $v) { ?>
                      <?php if (stristr($v[0], '开卡礼包') !== false) { ?>
                          <dl class="nc-store-sales" style="width: 400px;">
                              <dt style="width: 150px !important;"><?php echo $v[0]; ?></dt>
                              <dd style="width: 100px;">-￥<?php echo $v[1]; ?></dd>
                          </dl>
                      <?php } else { ?>
                          <dl class="nc-store-sales">
                              <dt><?php echo $v[0]; ?></dt>
                              <dd><?php echo $v[1]; ?></dd>
                          </dl>
                      <?php } ?>
                  <?php } ?>
              <?php }?>
                </td>
              </tr>
            </tfoot>
            <?php } ?>
            <!-- E 促销信息 -->
          </table>
        </div>

          <div style="display: inline-block;width: 560px;float: left;height: 0px;padding-top: 5px;">
              <?php if(($sdp = $output['order_info']['extend_order_common']['seller_discount_price']) != 0){ ?>
                  <div style="padding: 5px 0;font-weight: bold;">设置优惠金额：
                      <?php echo $sdp > 0 ? ("-￥$sdp") : ("+￥".abs($sdp)); ?>
                  </div>
              <?php } ?>
              <p style="color: #999;">说明：未付款订单，如出现运费错误、优惠金额错误等原因时，可和用户协商后，客服在后台设置优惠，增减订单应付金额后，再让用户付款，达到补差价的目的。</p>
              <p style="padding-top: 3px;color: #999;">订单总额=商品总价+运费-优惠券-设置优惠金额</p>
          </div>

        <div class="total-amount">
          <h3><?php echo $lang['order_total_price'];?><?php echo $lang['nc_colon'];?><strong class="red_common"><?php echo $lang['currency'].ncPriceFormat($output['order_info']['order_amount']);?></strong>
            <?php if ($output['order_info']['if_refund_cancel']) { ?>
            <a onclick="CUR_DIALOG = ajax_form('add_refund', '订单退款', 'index.php?act=order&op=add_refund_all&order_id=<?php echo $output['order_info']['order_id']; ?>');" href="javascript:void(0);">[订单退款]</a>
            <?php } ?>
            </h3>
            <?php if($output['order_info']['show_refund_shipping_fee']) { ?>
          <h4>(<?php echo $lang['order_total_transport'];?><?php echo $lang['nc_colon'];?><?php echo $lang['currency'].ncPriceFormat($output['order_info']['shipping_fee']);?>)
            <a onclick="CUR_DIALOG = ajax_form('refund_freight','运费退款', 'index.php?act=order&op=refund_freight&order_id=<?php echo $output['order_info']['order_id']; ?>');" href="javascript:void(0);">[运费退款]</a>
          </h4>
            <?php } ?>

            <?php if($output['order_info']['if_part_refund']) { ?>
                <h4>
                    <a onclick="CUR_DIALOG = ajax_form('refund_freight','订单异常退款', 'index.php?act=order&op=refund_freight&type=2&order_id=<?php echo $output['order_info']['order_id']; ?>');" href="javascript:void(0);">[订单异常退款]</a>
                </h4>
            <?php } ?>
            <?php if($output['order_info']['refund_amount'] > 0) { ?>
          (<?php echo $lang['order_refund'];?><?php echo $lang['nc_colon'];?><?php echo $lang['currency'].ncPriceFormat($output['order_info']['refund_amount']);?>)
          <?php } ?>
        </div>


          <!--周期购子订单开始-->
          <?php if($output['order_info']['order_type'] == 9) {?>
              <?php
              if($output['sub_order_info']) {
              foreach($output['sub_order_info'] as $order_id => $sub_order) {
                  ?>
                  <div class="goods-info">
                      <h4>子订单商品信息（第<?php echo $sub_order['push_type']?>期）<span style="margin-left: 50px;">子订单号：<?php echo $sub_order['erp_order_sn']?></span></h4>
                      <table>
                          <thead>
                          <tr>
                              <th colspan="2">商品</th>
                              <th>单价</th>
                              <th><?php echo $lang['product_num'];?></th>
                              <th>优惠活动</th>
                              <th>佣金比例</th>
                              <th>收取佣金</th>
                          </tr>
                          </thead>
                          <tbody>
                          <?php $i = 0;?>
                          <?php if($sub_order['goods_list']){ foreach($sub_order['goods_list'] as $goods){ ?>
                              <?php $i++;?>
                              <tr>
                                  <td class="w30"><div class="goods-thumb"><a href="<?php echo SHOP_SITE_URL;?>/index.php?act=goods&goods_id=<?php echo $goods['goods_id'];?>" target="_blank"><img alt="<?php echo $lang['product_pic'];?>" src="<?php echo thumb($goods, 60);?>" /> </a></div></td>
                                  <td style="text-align: left;"><a href="<?php echo SHOP_SITE_URL;?>/index.php?act=goods&goods_id=<?php echo $goods['goods_id'];?>" target="_blank"><?php echo $goods['goods_name'];?></a>
                                      <span class="rec"><a target="_blank" href="<?php echo urlShop('snapshot', 'index', array('rec_id' => $goods['rec_id']));?>">[交易快照]</a></span>
                                      <?php if ($goods['refund'] == 1){?>
                                          <a onclick="CUR_DIALOG = ajax_form('add_refund', '<?php echo addslashes($goods['goods_name']);?> 退款', 'index.php?act=order&op=add_refund&order_id=<?php echo $output['order_info']['order_id']; ?>&goods_id=<?php echo $goods['rec_id']; ?>');" href="javascript:void(0);">[退款/退货]</a>
                                      <?php } ?>
                                      <br/><?php echo $goods['goods_spec'];?></td>
                                  <td class="w80"><?php echo $lang['currency'].ncPriceFormat($goods['goods_price']);?></td>
                                  <td class="w60"><?php echo $goods['goods_num'];?></td>
                                  <td class="w100"><?php echo orderGoodsType($goods['goods_type']); ?></td>
                                  <td class="w60"><?php echo $goods['commis_rate'] == 200 ? '' : $goods['commis_rate'].'%';?></td>
                                  <td class="w80"><?php echo $goods['commis_rate'] == 200 ? '' : ncPriceFormat($goods['goods_pay_price']*$goods['commis_rate']/100);?></td>
                              </tr>
                              <!-- S 赠品列表 -->
                              <?php if (!empty($sub_order['zengpin_list']) && $i == count($sub_order['goods_list'])) { ?>
                                  <tr>
                                      <td>&nbsp;</td>
                                      <td colspan="6"><div class="ncm-goods-gift">赠品：
                                              <ul><?php foreach($sub_order['zengpin_list'] as $zengpin_info) {?>
                                                      <li><a title="赠品：<?php echo $zengpin_info['goods_name'];?> * <?php echo $zengpin_info['goods_num'];?>" target="_blank" href="<?php echo $zengpin_info['goods_url'];?>"><img src="<?php echo $zengpin_info['image_60_url']; ?>" /></a></li>
                                                  <?php } ?></ul></div>
                                      </td>
                                  </tr>
                              <?php } ?>
                              <!-- E 赠品列表 -->
                          <?php } }?>
                          </tbody>
                          <!-- S 促销信息 -->
                          <?php $pinfo = $sub_order['extend_order_common']['promotion_info'];?>
                          <?php if(!empty($pinfo)){ ?>
                              <?php $pinfo = unserialize($pinfo);?>
                              <tfoot>
                              <tr>
                                  <th colspan="10">其它信息</th>
                              </tr>
                              <tr>
                                  <td colspan="10">
                                      <?php if($pinfo == false){ ?>
                                          <?php echo $output['order_info']['extend_order_common']['promotion_info'];?>
                                      <?php }elseif (is_array($pinfo)){ ?>
                                          <?php foreach ($pinfo as $v) {?>
                                              <dl class="nc-store-sales"><dt><?php echo $v[0];?></dt><dd><?php echo $v[1];?></dd></dl>
                                          <?php }?>
                                      <?php }?>
                                  </td>
                              </tr>
                              </tfoot>
                          <?php } ?>
                          <!-- E 促销信息 -->
                      </table>
                  </div>
                  <div class="total-amount">
                      <h3><?php echo $lang['order_total_price'];?><?php echo $lang['nc_colon'];?><strong class="red_common"><?php echo $lang['currency'].ncPriceFormat($sub_order['order_amount']);?></strong>
                          <?php if ($sub_order['if_refund_cancel']) { ?>
                              <a onclick="CUR_DIALOG = ajax_form('add_refund', '订单退款', 'index.php?act=order&op=add_refund_all&order_id=<?php echo $sub_order['order_id']; ?>');" href="javascript:void(0);">[订单退款]</a>
                          <?php } ?>
                      </h3>
                      <h4>(<?php echo $lang['order_total_transport'];?><?php echo $lang['nc_colon'];?><?php echo $lang['currency'].ncPriceFormat($sub_order['shipping_fee']);?>)</h4>
                      <?php if($sub_order['refund_amount'] > 0) { ?>
                          (<?php echo $lang['order_refund'];?><?php echo $lang['nc_colon'];?><?php echo $lang['currency'].ncPriceFormat($sub_order['refund_amount']);?>)
                      <?php } ?>
                  </div>
              <?php }
              }?>
          <?php }?>
          <!--周期购子订单结束-->

          <!--预付定金信息开始-->
          <?php if ($output['order_info']['order_type'] == 11) {  ?>
              <?php
              $pre_data = $output['order_info']['pre_data'];
              //  优惠券
              $promotion_total = isset($output['order_info']['extend_order_common']['promotion_total']) ? $output['order_info']['extend_order_common']['promotion_total'] : '0.00';
              ?>
              <h4>两阶段：</h4>
                  <table>
              <tfoot>
              <tr>
                  <td colspan="20" style="padding: 0px 20px">
                      <dl style="line-height: 10px;">
                          <dt style="margin-right: 50px;text-align: left;width: 140px;">
                              <b style="color:#FF875A;font-weight: 400;">阶段一: <?php echo ($pre_data['is_pay_dj'] ? "已完成" : "未完成").$pre_data['refund_status_dj']; ?></b><br/>
                              商品定金
                          </dt>
                          <dd style="text-align: right">￥<?php echo $pre_data['pre_price'] ?: '0.00'; ?></dd>
                      </dl>
                  </td>
              </tr>
              <tr>
                  <td colspan="20" style="padding: 0px 20px">
                      <dl class="sum">
                          <dt style="margin-right: 70px;font-size: 16px;text-align: left;width: 140px;">定金<?php echo $pre_data['is_pay_dj'] ? '实' : '应'  ?>付</dt>
                          <dt style="text-align: right">￥<?php echo $pre_data['pre_price'] ?: '0.00'; ?></dt>
                      </dl>
                  </td>
              </tr>
              <tr>
                  <td colspan="20" style="padding: 10px 20px">

                  </td>
              </tr>
              <tr>
                  <td colspan="20" style="padding: 0px 20px">
                      <dl  style="line-height: 10px;">
                          <dt style="margin-right: 50px;text-align: left;width: 140px;">
                              <b style="color:#FF875A;font-weight: 400">阶段二: <?php echo ($pre_data['is_pay_wk'] ? "已完成" : "未完成").$pre_data['refund_status_wk']; ?></b><br/>
                              商品尾款
                          </dt>
                          <dd style="text-align: right">￥<?php echo bcadd(isset($pre_data['last_price']) ? $pre_data['last_price'] : 0,$promotion_total,2); ?></dd>
                      </dl>
                  </td>
              </tr>
              <tr>
                  <td colspan="20" style="padding: 0px 20px">
                      <dl>
                          <dt style="margin-right: 50px;text-align: left;width: 140px;">优惠券</dt>
                          <dd style="text-align: right">-￥<?php echo $promotion_total; ?></dd>
                      </dl>
                  </td>
              </tr>
              <tr style="display: block;position: relative;height: 10px;"></tr>
              <tr>
                  <td colspan="20" style="padding: 0px 20px">
                      <dl class="sum"  style="line-height: 30px;">
                          <dt style="margin-right: 70px;font-size: 16px;text-align: left;width: 140px;">尾款<?php echo $pre_data['is_pay_wk'] ? '实' : '应'  ?>付</dt>
                          <dt style="text-align: right">￥<?php echo $pre_data['last_price'] ?: '0.00'; ?></dt>
                      </dl>
                  </td>
              </tr>
              </tfoot>
                  </table>

            <h4>付定金和付尾款时间：</h4>
              <div class="ncsc-order-contnet">
                  <table class="ncsc-default-table order">
                      <tr>
                          <td style="text-align: left;padding: 20px">
                              <p>定金和尾款支付时间：</p>
                              <p>付定金时间：<?php echo $output['order_info']['pre_data']['start_time_dj'].' - '.$output['order_info']['pre_data']['end_time_dj'];?></p>
                              <p>付尾款时间：<?php echo $output['order_info']['pre_data']['start_time_wk'].' - '.$output['order_info']['pre_data']['end_time_wk'];?></p>
                          </td>
                      </tr>
                  </table>
              </div>
          <?php } ?>
          <!--预付定价信息结束-->
      </div>
      <?php if(is_array($output['refund_list']) && !empty($output['refund_list'])) { ?>
      <div class="tabs-panels tabs-hide">
        <div>
          <h4>退款信息</h4>
          <?php foreach($output['refund_list'] as $val) { ?>
          <dl>
            <dt>退款单号<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $val['refund_sn'];?></dd>
            <dt>退款金额<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $lang['currency'];?><?php echo ncPriceFormat(isset($output['order_info']['cycle_refund_total']) ? $output['order_info']['cycle_refund_total'] : $val['refund_amount']); ?></dd>
            <dt>发生时间<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo date("Y-m-d H:i:s",$val['admin_time']); ?></dd>
            <dt>备注<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $val['goods_name'];?></dd>
          </dl>
          <?php } ?>
        </div>
      </div>
      <?php } ?>
      <?php if(is_array($output['return_list']) && !empty($output['return_list'])) { ?>
      <div class="tabs-panels tabs-hide">
        <div>
          <h4>退货信息</h4>
          <?php foreach($output['return_list'] as $val) { ?>
          <dl>
            <dt>退货单号<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $val['refund_sn'];?></dd>
            <dt>退款金额<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $lang['currency'];?><?php echo ncPriceFormat(isset($output['order_info']['cycle_refund_total']) ? $output['order_info']['cycle_refund_total'] : $val['refund_amount']); ?></dd>
            <dt>发生时间<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo date("Y-m-d H:i:s",$val['admin_time']); ?></dd>
            <dt>备注<?php echo $lang['nc_colon'];?></dt>
            <dd><?php echo $val['goods_name'];?></dd>
          </dl>
          <?php } ?>
        </div>
      </div>
      <?php } ?>

    </div>
  </div>
</div>
<script type="text/javascript">
    $(function() {
        $(".tabs-nav > li > a").mousemove(function(e) {
            if (e.target == this) {
                var tabs = $(this).parent().parent().children("li");
                var panels = $(this).parents('.ncap-order-details:first').children(".tabs-panels");
                var index = $.inArray(this, $(this).parents('ul').find("a"));
                if (panels.eq(index)[0]) {
                    tabs.removeClass("current").eq(index).addClass("current");
                   panels.addClass("tabs-hide").eq(index).removeClass("tabs-hide");
                }
            }
        });
    });

    function hideshow($order_id, $type = 1){
        var data = {};
        data.type = $type;
        data.order_id = $order_id;
        if ($type == 3){
            var phone_obj = document.getElementById("show_phone_0_"+$order_id)
            var obj = document.getElementById("showmobile_0_"+$order_id);
        }else{
            var phone_obj = document.getElementById("show_phone_"+$order_id)
            var obj = document.getElementById("showmobile_"+$order_id);
        }
        data.text = obj.innerText
        console.log(obj.innerText)
        console.log(phone_obj.innerText)
        $.ajax({
            type:'POST',
            url:'index.php?act=order&op=showphone',
            cache:false,
            data:data,
            dataType:'json',
            success:function(data){
                if(data.status == 200){
                    if(obj.innerText == '隐藏'){
                        obj.innerText = "显示";
                        obj.className   = "ncap-btn-mini ncap-btn-green"
                    }else{
                        obj.innerText = "隐藏";
                        obj.className   = "ncap-btn-mini ncap-btn-red"
                    }
                    phone_obj.innerText = data.phone
                }else{
                    alert(data.msg);
                }
            }
        });
    }

</script>
