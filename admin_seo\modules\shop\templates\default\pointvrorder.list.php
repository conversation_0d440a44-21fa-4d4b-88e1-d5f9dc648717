<?php defined('InShopNC') or exit('Access Invalid!');?>
<style type="text/css">.w220{max-width: 221px !important;}</style>
<div class="page">
  <div class="fixed-bar">
    <div class="item-title">
      <div class="subject">
        <h3><?php echo $lang['nc_pointprod'];?></h3>
        <h5>平台会员积分兑换礼品管理</h5>
      </div>
      <ul class="tab-base nc-row">
        <li><a href="index.php?act=pointprod&op=pointprod" ><span><?php echo $lang['admin_pointprod_list_title'];?></span></a></li>
        <li><a href="index.php?act=pointprod&op=pointorder_list"><span><?php echo $lang['admin_pointorder_list_title'];?></span></a></li>
        <li><a href="JavaScript:void(0);" class="current"><?php echo $lang['admin_pointorder_vrlist_title'];?></a></li>
      </ul>
    </div>
  </div>

  <div id="flexigrid"></div>

    <div class="ncap-search-ban-s" id="searchBarOpen"><i class="fa fa-search-plus"></i>高级搜索</div>
    <div class="ncap-search-bar">
      <div class="handle-btn" id="searchBarClose"><i class="fa fa-search-minus"></i>收起边栏</div>
      <div class="title">
        <h3>高级搜索</h3>
      </div>
      <form method="get" name="formSearch" id="formSearch">
        <input type="hidden" name="advanced" value="1" />
        <div id="searchCon" class="content">
          <div class="layout-box">
            <dl>
              <dt>兑换单号</dt>
              <dd>
                <input type="text" name="point_ordersn" class="s-input-txt" placeholder="请输入兑换单号" />
              </dd>
            </dl>
            <dl>
              <dt>会员名称</dt>
              <dd>
                <input type="text" name="point_buyername" class="s-input-txt" placeholder="请输入会员名称" />
              </dd>
            </dl>
            <dl>
              <dt>状态</dt>
              <dd>
                <select name="point_status" class="s-select">
                    <option value=""><?php echo $lang['nc_please_choose'];?></option>
                    <option value="1">未使用</option>
                    <option value="2">已使用</option>
                    <option value="3">已过期</option>

                </select>
              </dd>
            </dl>
            <dl>
              <dt>订单来源</dt>
              <dd>
                  <select name="point_type" class="s-select">
                      <option value="">-请选择-</option>
                      <option value="0" >电商</option>
                      <option value="1" >宠医云</option>
                      <option value="2" >阿闻小程序</option>
                  </select>
              </dd>
            </dl>
            <dl>
              <dt>日期筛选</dt>
              <dd>
                  <label>
                      <input readonly id="query_start_date" placeholder="请选择起始时间" name=query_start_date value="" type="text" class="s-input-txt" />
                  </label>
                  <label>
                      <input readonly id="query_end_date" placeholder="请选择结束时间" name="query_end_date" value="" type="text" class="s-input-txt" />
                  </label>
              </dd>
             </dl>
          </div>
        </div>
        <div class="bottom">
          <a href="javascript:void(0);" id="ncsubmit" class="ncap-btn ncap-btn-green">提交查询</a>
          <a href="javascript:void(0);" id="ncreset" class="ncap-btn ncap-btn-orange" title="撤销查询结果，还原列表项所有内容"><i class="fa fa-retweet"></i><?php echo $lang['nc_cancel_search'];?></a>
        </div>
      </form>
    </div>

</div>

<script>
$(function() {
    $('#query_start_date').datepicker();
    $('#query_end_date').datepicker();
    var flexUrl = 'index.php?act=pointprod&op=point_vrorder_list_xml';
    $("#flexigrid").flexigrid({
        url: flexUrl,
        colModel: [
            {display: '操作', name: 'operation', width: 350, sortable: false, align: 'center', className: 'handle w220'},
            {display: '兑换单号', name: 'point_ordersn', width: 150, sortable: false, align: 'left'},
            {display: '会员名称', name: 'point_buyername', width: 80, sortable: false, align: 'left'},
            {display: '兑换积分', name: 'point_allpoint', width: 80, sortable: false, align: 'center'},
            {display: '兑换类别', name: 'point_goodstype', width: 80, sortable: false, align: 'center'},
            {display: '兑换商品', name: 'point_goodsname', width: 200, sortable: false, align: 'left'},
            {display: '兑换时间', name: 'point_addtime_text', width: 150, sortable: false, align: 'center'},
            {display: '状态', name: 'point_orderstatetext', width: 80, sortable: false, align: 'center'},
            {display: 'ERP积分', name: 'erp_points', width: 150, sortable: false, align: 'center'}
        ],
        buttons : [
            {display: '<i class="fa fa-file-excel-o"></i>导出数据', name : 'csv', bclass : 'csv', title : '将选定行数据导出csv文件,如果不选中行，将导出列表所有数据', onpress : fg_operate}
        ],
        searchitems: [
            {display: '兑换单号', name: 'point_ordersn', isdefault: true},
            {display: '会员名称', name: 'point_buyername'}
        ],
        sortname: "point_orderid",
        sortorder: "desc",
        title: '积分兑换列表'
    });

    // 高级搜索提交
    $('#ncsubmit').click(function(){
        $("#flexigrid").flexOptions({url: flexUrl + '&' + $("#formSearch").serialize(),query:'',qtype:''}).flexReload();
    });

    // 高级搜索重置
    $('#ncreset').click(function(){
        $("#flexigrid").flexOptions({url: flexUrl}).flexReload();
        $("#formSearch")[0].reset();
    });

});

$('a.confirm-on-click').live('click', function() {
    return confirm('确定"'+this.innerHTML+'"?');
});
function fg_operate(name, grid) {
    if (name == 'csv') {
        var itemlist = new Array();
        if($('.trSelected',grid).length>0){
            $('.trSelected',grid).each(function(){
                itemlist.push($(this).attr('data-id'));
            });
        }
        fg_csv(itemlist);
    }
}
function fg_csv(ids) {
    id = ids.join(',');
    window.location.href = $("#flexigrid").flexSimpleSearchQueryString()+'&op=export_point_vrorder&order_id='+id;
    //window.location.href = 'index.php?act=pointprod&op=export_pointorder&order_id='+id;
}
</script>
