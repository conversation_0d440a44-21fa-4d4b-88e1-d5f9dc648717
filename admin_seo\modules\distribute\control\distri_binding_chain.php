<?php

use Box\Spout\Common\Type;
use Box\Spout\Reader\ReaderFactory;
use Box\Spout\Writer\Style\StyleBuilder;
use Box\Spout\Writer\WriterFactory;
use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');

class distri_binding_chainControl extends SystemControl
{
    private $_links = array(
        array('url' => 'act=distri_binding_chain&op=index', 'text' => '客服绑定门店'),
        array('url' => 'act=distri_binding_chain&op=binding_chain_log', 'text' => '历史记录'),
    );

    function __construct()
    {
        parent::__construct();
    }

    public function indexOp()
    {
        //输出子菜单
        Tpl::output('top_link', $this->sublink($this->_links, 'index'));
        Tpl::showpage('distri_binding_chain.index');
    }

    public function binding_chain_logOp()
    {
        //输出子菜单
        Tpl::output('top_link', $this->sublink($this->_links, 'binding_chain_log'));
        Tpl::showpage('distri_binding_chain.log');
    }

    /**
     * 客服绑定门店历史记录
     */
    public function get_logOp()
    {
        $model_member = Model('member');
        $condition = array();
        if ($_POST['query'] != '') {
            switch ($_POST['qtype']) {
                case 'member_id':
                    $condition['chain_bind_log.' . $_POST['qtype']] = $_POST['query'];
                    break;
                case 'bill_user_name':
                case 'member_mobile':
                    $condition['member.' . $_POST['qtype']] = $_POST['query'];
                    break;
                case 'account_id':
                    $condition['chain.' . $_POST['qtype']] = $_POST['query'];
                    break;
                case 'chain_name':
                    $condition['chain.' . $_POST['qtype']] = array('like', "%{$_POST['query']}%");
                    break;
            }
        }
        $page = $_POST['rp'];

        $on = 'member.member_id = chain_bind_log.member_id,chain_bind_log.chain_id = chain.chain_id';
        $field = 'chain_bind_log.id,chain_bind_log.chain_member_mobile,member.member_id,member.member_mobile,member.bill_user_name,chain.account_id,chain.chain_name,chain_bind_log.binding_time,chain_bind_log.undinding_time';
        if ($_GET['export']) {
            if ($_GET['id'] != '') {
                $id_array = explode(',', $_GET['id']);
                $condition['chain_bind_log.id'] = array('in', $id_array);
            }
            $member_chain_list = Model()->table('chain_bind_log,member,chain')->field($field)
                ->join('left,left')->on($on)->where($condition)->order('id desc')->limit(false)->select();
        } else {
            $member_chain_list = Model()->table('chain_bind_log,member,chain')->field($field)
                ->join('left,left')->on($on)->where($condition)->order('id desc')->page($page)->select();
        }
        $data = array();
        $data['now_page'] = $model_member->shownowpage();
        $data['total_num'] = $model_member->gettotalnum();
        if ($member_chain_list) {
            $list = [];
            foreach ($member_chain_list as $k => $value) {
                $param = array();
                $list[$k]['member_id'] = $param['member_id'] = $value['member_id'];
                $list[$k]['member_name'] = $param['member_name'] = $value['bill_user_name'];
                $list[$k]['member_mobile'] = $param['member_mobile'] = $_GET['export'] == 1 ? $value['member_mobile'] : hideStr($value['member_mobile']);
                $list[$k]['cash_ratio'] = $param['cash_ratio'] = '1/10';
                $list[$k]['chain_name'] = $param['chain_name'] = $value['chain_name'];
                $list[$k]['account_id'] = $param['account_id'] = $value['account_id'];
                $list[$k]['chain_member_mobile'] = $param['chain_member_mobile'] = $_GET['export'] == 1 ? $value['chain_member_mobile'] : hideStr($value['chain_member_mobile']);
                $list[$k]['binding_time'] = $param['binding_time'] = $value['binding_time'];
                $list[$k]['undinding_time'] = $param['undinding_time'] = $value['undinding_time'];
                $data['list'][$value['id']] = $param;
            }
        }
        if ($_GET['export']) {
            $writer = WriterFactory::create(Type::XLSX);
            $writer->setDefaultRowStyle((new StyleBuilder())->setShouldWrapText(false)->build());
            $writer->openToBrowser('客服绑定门店历史记录-' . date('Y-m-d-H', time()) . '.xlsx');
            $writer->addRow(['分销员ID', '姓名', '分销员手机', '客服提现比例', '门店名称', '财务编码', '门店手机号', '绑定时间', '解绑时间']);
            $writer->addRows($list);
            $writer->close();
        } else {
            echo Tpl::flexigridXML($data);
            exit();
        }
    }

    /**
     * 客服绑定门店列表
     */
    public function get_xmlOp()
    {
        $model_member = Model('member');
        $condition = array();
        if ($_POST['query'] != '') {
            switch ($_POST['qtype']) {
                case 'member_id':
                    $condition['chain_bind.' . $_POST['qtype']] = $_POST['query'];
                    break;
                case 'bill_user_name':
                case 'member_mobile':
                    $condition['member.' . $_POST['qtype']] = $_POST['query'];
                    break;
                case 'account_id':
                    $condition['chain.' . $_POST['qtype']] = $_POST['query'];
                    break;
                case 'chain_name':
                    $condition['chain.' . $_POST['qtype']] = $_POST['query']?array('like', "%{$_POST['query']}%"):'';
                    break;
            }
        }
        $chain_model = Model('chain');
        $chain_list =$chain_model->getChainList(array('store_id'=>$_SESSION['store_id']), 'chain_id', null, '',10000);
        $chainIdsArr = array_column($chain_list,'chain_id');
        $condition['chain_bind.chain_id'] = array('in',$chainIdsArr);
        $page = $_POST['rp'];

        $on = 'member.member_id = chain_bind.member_id,chain_bind.chain_id = chain.chain_id';
        $field = 'chain_bind.id,chain_bind.chain_member_mobile,member.member_id,member.member_mobile,member.bill_user_name,chain.account_id,chain.chain_name,chain_bind.binding_time';
        if ($_GET['export']) {//导出
            if ($_GET['id'] != '') {
                $id_array = explode(',', $_GET['id']);
                $condition['chain_bind.id'] = array('in', $id_array);
            }
            $member_chain_list = Model()->table('chain_bind,member,chain')->field($field)
                ->join('left,left')->on($on)->where($condition)->order('chain_bind.id desc')->limit(false)->select();
        } else {
            $member_chain_list = Model()->table('chain_bind,member,chain')->field($field)
                ->join('left,left')->on($on)->where($condition)->order('chain_bind.id desc')->page($page)->select();
        }

        $data = array();
        $data['now_page'] = $model_member->shownowpage();
        $data['total_num'] = $model_member->gettotalnum();
        if ($member_chain_list) {
            $list = array();
            foreach ($member_chain_list as $k => $value) {
                $param = array();
                $operation = "<a class='btn red' href='javascript:void(0);' onclick=\"fg_unbind(" . $value['id'] . ")\"><i class='fa fa-trash-o'></i>解绑</a>";
                $param['operation'] = $operation;
                $list[$k]['member_id'] = $param['member_id'] = $value['member_id'];
                $list[$k]['member_name'] = $param['member_name'] = $value['bill_user_name'];
                $list[$k]['member_mobile'] = $param['member_mobile'] = $_GET['export'] == 1 ? $value['member_mobile'] : hideStr($value['member_mobile']);
                $list[$k]['cash_ratio'] = $param['cash_ratio'] = '1/10';
                $list[$k]['chain_name'] = $param['chain_name'] = $value['chain_name'];
                $list[$k]['account_id'] = $param['account_id'] = $value['account_id'];
                $list[$k]['chain_member_mobile'] = $param['chain_member_mobile'] = $_GET['export'] == 1 ? $value['chain_member_mobile'] : hideStr($value['chain_member_mobile']);
                $list[$k]['binding_time'] = $param['binding_time'] = $value['binding_time'];
                $data['list'][$value['id']] = $param;
            }
        }
        if ($_GET['export']) {
            $writer = WriterFactory::create(Type::XLSX);
            $writer->setDefaultRowStyle((new StyleBuilder())->setShouldWrapText(false)->build());
            $writer->openToBrowser('客服绑定门店列表-' . date('Y-m-d-H', time()) . '.xlsx');
            $writer->addRow(['分销员ID(客服)', '姓名', '分销员手机', '客服提现比例', '门店名称', '财务编码', '门店手机号', '绑定时间']);
            $writer->addRows($list);
            $writer->close();
        } else {
            echo Tpl::flexigridXML($data);
            exit();
        }
    }

    /**
     * Notes:解绑门店
     * User: rocky
     * DateTime: 2022/12/29 17:35
     */
    public function chain_unbindOp()
    {
        $id = $_GET['id'];
        if (!$id) {
            exit(json_encode(['state' => false, 'msg' => 'id不能为空']));
        }
        $model_chain_bind = Model('chain_bind');
        $model_chain_bind_log = Model('chain_bind_log');
        $chain_bind_info = $model_chain_bind->where(['id' => $id])->find();
        if (!$chain_bind_info) {
            exit(json_encode(['state' => false, 'msg' => '该记录不存在']));
        }
        //删除绑定记录
        $model_chain_bind->where(['id' => $id])->delete();
        //添加解绑日志
        $model_chain_bind_log->insert([
            'member_id' => $chain_bind_info['member_id'],
            'chain_id' => $chain_bind_info['chain_id'],
            'chain_member_mobile' => $chain_bind_info['chain_member_mobile'],
            'binding_time' => $chain_bind_info['binding_time'],
            'undinding_time' => date('Y-m-d H:i:s', time())
        ]);
        exit(json_encode(['state' => true]));
    }

    /**
     * Notes:添加
     * User: rocky
     * DateTime: 2022/12/30 9:26
     */
    public function bind_addOp()
    {
        Tpl::showpage('distri_binding_chain.add');
    }

    /**
     * Notes:绑定门店添加
     * User: rocky
     * DateTime: 2022/12/29 17:35
     */
    public function bind_add_chainOp()
    {
        $mid = $_POST['member_id'];
        $aid = $_POST['account_id'];
        $c_m = $_POST['chain_member_mobile'];
        if (!$mid || !$aid) {
            echo json_encode(['code' => 400, 'msg' => '输入参数有误']);
            exit;
        }

        $resuslt = $this->verify_bind_data($mid, $aid, $c_m);
        if ($resuslt['code'] != 200) {
            echo json_encode(['code' => 400, 'msg' => $resuslt['msg']]);
            exit;
        }
        $chain_info = Model('chain')->getChainInfo(['account_id' => $aid]);
        $res = Model('chain_bind')->insert(['member_id' => $mid, 'chain_id' => $chain_info['chain_id'], 'chain_member_mobile' => $c_m]);
        if ($res) {
            //添加日志
            Model('chain_bind_log')->insert(['member_id' => $mid, 'chain_id' => $chain_info['chain_id'], 'chain_member_mobile' => $c_m]);
            echo json_encode(array('code' => 200, 'msg' => '添加成功'));
            exit;
        }
        echo json_encode(array('code' => 400, 'msg' => '添加失败'));
        exit;
    }

    /**
     * Notes:
     * @param $mid 用户member_id
     * @param $aid 门店财务编码 account_id
     * @param $c_m 门店手机号
     * User: rocky
     * DateTime: 2023/1/6 17:46
     */
    public function verify_bind_data($mid, $aid, $c_m)
    {
        $resust['code'] = 400;
        $resust['msg'] = '';
        if (!preg_match('/^[\d]{11}$/', $c_m)) {
            $resust['msg'] = '手机号应是11位数字';
            return $resust;
        }

        $model_member = Model('member');
        $model_chain = Model('chain');
        $model_chain_bind = Model('chain_bind');
        $member_info = Model()->table('member,chain_brand')->field('member.member_id,member.distri_chainid,member.distri_state,chain_brand.brand_name')
            ->join('inner')->on('member.distri_brandid = chain_brand.chain_brand_id')->where(['member.member_id' => $mid, 'distri_state' => 2])->find();

        if (!$member_info) {
            $resust['msg'] = '未找到审核通过的内部分销员ID';
            return $resust;
        }elseif ($member_info['distri_chainid'] != 1 || $member_info['brand_name'] != '总部') {
            $resust['msg'] = '不是总部客服内部分销员ID';
            return $resust;
        }
        $chain_info = $model_chain->getChainInfo(['account_id' => $aid]);
        if (!$chain_info) {
            $resust['msg'] = '门店财务编码不正确';
            return $resust;
        }elseif ($chain_info['chain_id'] == 1) {
            $resust['msg'] = '门店不能为总部职能';
            return $resust;
        }
        $chain_member_info = $model_member->getMemberInfo(['member_mobile' => $c_m], 'member_id,distri_chainid,distri_state');
        if (!$chain_member_info) {
            $resust['msg'] = '手机号没有注册爱省钱';
            return $resust;
        }elseif ($chain_member_info['distri_state'] == 2 && $chain_member_info['distri_chainid'] == 0) {
            $resust['msg'] = '查不到手机号绑定门店记录';
            return $resust;
        }elseif ($chain_member_info['distri_state'] != 2 || $chain_member_info['distri_chainid'] != $chain_info['chain_id']) {
            $resust['msg'] = '查不到手机号绑定门店记录';
            return $resust;
        }

        $condition['chain_member_mobile'] = $c_m;
        $condition['chain_id'] = $chain_info['chain_id'];
        $condition['_op'] = 'or';
        $chain_bind_info = $model_chain_bind->where($condition)->find();
        if ($chain_bind_info && $chain_bind_info['member_id'] == $mid) {
            $resust['msg'] = '请勿重复添加相同数据';
            return $resust;
        } elseif ($chain_bind_info) {
            $resust['msg'] = '门店已绑定其他内部分销员';
            return $resust;
        }
        $chain_bind_info = $model_chain_bind->where(['member_id' => $mid, 'chain_id' => $chain_info['chain_id']])->find();
        if ($chain_bind_info) {
            $resust['msg'] = '该记录已存在，请勿重复添加';
            return $resust;
        }

        $resust['code'] = 200;
        return $resust;
    }

    public function batch_importOp()
    {
        Tpl::showpage('distri_binding_chain.import');
    }

    /**
     * Notes:下载模板
     * User: rocky
     * DateTime: 2023/1/3 10:07
     */
    public function down_exportOp()
    {
        $writer = WriterFactory::create(Type::XLSX);
        $writer->setDefaultRowStyle((new StyleBuilder())->setShouldWrapText(false)->build());
        $writer->openToBrowser('批量导入内部分销员模板.xlsx');
        $writer->addRow(['内部分销员ID(客服)', '门店财务编码', '门店爱省钱绑定的手机号']);
        $writer->close();
    }

    public function distri_importOp()
    {
        $tmp = $_FILES['cash_export']['tmp_name'];
        $imageName = "25220_" . date("His", time()) . "_" . rand(1111, 9999) . '.xlsx';
        $path = BASE_UPLOAD_PATH . DS . ATTACH_DISTRI . DS;
        $src = $path . DS . $imageName;
        if (!is_dir($path)) mkdir($path, 0777, true);
        if (!is_readable($src)) chmod($src, 0777);
        if (move_uploaded_file($tmp, $src)) {
            $path = $src;
        } else {
            echo 'error';
            exit;
        }
        $type = pathinfo($path);
        $type = strtolower($type["extension"]);
        $type = ($type === 'xlsx') ? Type::XLSX : Type::CSV;
        $reader = ReaderFactory::create($type);
        $reader->setShouldFormatDates(true);
        $reader->open($path);
        $iterator = $reader->getSheetIterator();
        $iterator->rewind();
        $sheet1 = $iterator->current();
        $rowIter = $sheet1->getRowIterator();
        $data = [];
        foreach ($rowIter as $rowIndex => $row) {
            if ($rowIndex > 1) {
                $data[] = $row;
            }
        }
        $reader->close();
        if (!$data) {
            showDialog('导入数据为空', 'reload');
        }
        $model_chain = Model('chain');
        $import_log = $list = $has_data = [];
        foreach ($data as $k => $v) {
            $mid = $v[0];
            $aid = $v[1];
            $c_m = $v[2];
            $import_log[$k]['mid'] = $mid;
            $import_log[$k]['aid'] = $aid;
            $import_log[$k]['c_m'] = $c_m;
            $import_log[$k]['res'] = '导入失败';
            $resuslt = $this->verify_bind_data($mid, $aid, $c_m);
            if ($resuslt['code'] != 200) {
                $import_log[$k]['desc'] = $resuslt['msg'];
                continue;
            }
            //判断导入数据是否重复
            if ($has_data[$aid]) {
                $import_log[$k]['desc'] = '表格内有重复数据';
                continue;
            }
            $has_data[$aid] = $v;
            //添加数据
            $chain_info = $model_chain->getChainInfo(['account_id' => $aid]);
            $list[$k]['member_id'] = $mid;
            $list[$k]['chain_id'] = $chain_info['chain_id'];
            $list[$k]['chain_member_mobile'] = $c_m;
            $import_log[$k]['res'] = '导入成功';
        }

        $content = '全部成功';
        if (count($list) == 0) {
            $content = '全部失败';
        } elseif (count($list) < count($import_log)) {
            $content = '部分成功';
        }
        //导入记录
        $import_model = Model('import');
        $import_model->beginTransaction();
        $res = $import_model->insert(['content' => $content, 'import_content' => json_encode($import_log, JSON_UNESCAPED_UNICODE)]);
        if ($res && $list) {
            $list = array_values($list);
            $resuslt = Model('chain_bind')->insertAll($list);
            $resuslt2 = Model('chain_bind_log')->insertAll($list);
            if (!$resuslt || !$resuslt2) {
                $import_model->rollback();
                showDialog('批量导入失败', '', 'html', 'error');
            }
            $import_model->commit();
            showDialog('批量导入成功', 'reload');
        }
        $import_model->commit();
        showDialog('批量导入失败无数据，请查看结果', '', 'html', 'error');
    }

    public function importOp()
    {
        Tpl::showpage('distri_binding_chain.import_log');
    }

    public function import_logOp()
    {
        $model_import = Model('import');
        $page = $_POST['rp'];
        $import_log = $model_import->page($page)->order('id desc')->select();
        $data = array();
        $data['now_page'] = $model_import->shownowpage();
        $data['total_num'] = $model_import->gettotalnum();
        foreach ($import_log as $k => $value) {
            $param = array();
            $param['create_time'] = $value['create_time'];
            $param['content'] = $value['content'];
            $param['result'] = "<a class='btn red' href='index.php?act=distri_binding_chain&op=import_log_xls&id={$value["id"]}'><i class='fa'></i>查看结果</a>";
            $data['list'][$value['id']] = $param;
        }
        echo Tpl::flexigridXML($data);
        exit();
    }

    public function import_log_xlsOp()
    {
        $id = $_GET['id'];
        $import_log = Model('import')->where(['id' => $id])->find();
        $data = [];
        if ($import_log) {
            $list = json_decode($import_log['import_content'], true);
            foreach ($list as $k => $v) {
                $data[$k]['mid'] = $v['mid'];
                $data[$k]['aid'] = $v['aid'];
                $data[$k]['c_m'] = isset($v['c_m']) ? $v['c_m'] : '';
                $data[$k]['res'] = $v['res'];
                $data[$k]['desc'] = isset($v['desc']) ? $v['desc'] : '';
            }
        }
        $writer = WriterFactory::create(Type::XLSX);
        $writer->setDefaultRowStyle((new StyleBuilder())->setShouldWrapText(false)->build());
        $writer->openToBrowser('查看结果列表-' . date('Y-m-d-H', time()) . '.xlsx');
        $writer->addRow(['内部分销员ID(客服)', '门店财务编码', '门店爱省钱绑定的手机号', '导入结果', '备注']);
        $writer->addRows($data);
        $writer->close();
    }
}