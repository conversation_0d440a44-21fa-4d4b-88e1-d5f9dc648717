<?php
/**
 * 分销-变更管理
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');

class distri_modifyControl extends SystemControl
{

    function __construct()
    {
        parent::__construct();
    }

    /**
     * 更改分销信息列表
     */
    public function indexOp()
    {
        Tpl::showpage('distri_modify.index');
    }

    /**
     * 更改分销信息详情
     */
    public function modify_viewOp()
    {
        $model_modify = Model('dis_modify_log');
        $id = intval($_GET['id']);
        $condition['id'] = $id;
        $modif_info = $model_modify->getDistriModifyInfo($condition, 'image1,image2,image3');
        for($i=1;$i<=3;$i++){
            if ($modif_info['image'.$i]){
                $modif_info['image'.$i] = '/'.DIR_UPLOAD.'/'.ATTACH_PATH.'/distri/'.$modif_info['image'.$i];
            }
        }

        Tpl::output('info',$modif_info);
        Tpl::showpage('distri_modify.view', 'null_layout');
    }

    /**
     * 获取更改分销信息xml数据
     */
    public function get_modify_xmlOp()
    {
        $model_modify = Model('dis_modify_log');
        $condition = array();
        list($condition, $order) = $this->_get_modify_condition($condition);
        $modif_list = $model_modify->getDistriModifyList($condition, '*', $_POST['rp'], $order);
        $data = array();
        $data['now_page'] = $model_modify->shownowpage();
        $data['total_num'] = $model_modify->gettotalnum();
        foreach ($modif_list as $modif_info) {
            $list = array();
            $list['op_date'] = date('Y-m-d H:i:s',$modif_info['op_date']);
            $list['op_name'] = $modif_info['op_name'];
            $list['order_sn'] = $modif_info['order_sn'];
            $list['goods_name'] = $modif_info['goods_name'];
            $list['pay_goods_amount'] = ncPriceFormat($modif_info['pay_goods_amount']);
            $list['dis_pay_amount'] = ncPriceFormat($modif_info['dis_pay_amount']);
            $list['dis_o_memberid'] = ($modif_info['dis_o_memberid'] ? $modif_info['dis_o_memberid'] : '-');
            if ($modif_info['dis_o_memberid']){
                $list['dis_o_chain'] = ($modif_info['dis_o_chain'] ? $modif_info['dis_o_chain'] : '外部代理人或其他');
            }else{
                $list['dis_o_chain'] = '-';
            }
            $list['dis_n_memberid'] = $modif_info['dis_n_memberid'];
            $list['dis_n_chain'] = ($modif_info['dis_n_chain'] ? $modif_info['dis_n_chain'] : '外部代理人或其他');
            $list['operation'] = "<a class='btn green' href='javascript:void(0)' onclick=\"ajax_form('modify_view','查看', 'index.php?act=distri_modify&op=modify_view&id=". $modif_info['id'] ."', 840)\" ><i class='fa fa-list-alt'></i>查看</a>";

            $data['list'][$modif_info['id']] = $list;
        }
        exit(Tpl::flexigridXML($data));
    }

    private function _get_modify_condition($condition) {
        if ($_REQUEST['query'] != '' && $_REQUEST['qtype'] == 'order_sn') {
            $condition[$_REQUEST['qtype']] = $_REQUEST['query'];
        }elseif ($_REQUEST['query'] != '' && $_REQUEST['qtype'] == 'chain_name') {
            $condition['dis_o_chain|dis_n_chain'] = array('like',"%{$_REQUEST['query']}%");
        }elseif ($_REQUEST['query'] != '' && $_REQUEST['qtype'] == 'dis_memberid') {
            $condition['dis_o_memberid|dis_n_memberid'] = $_REQUEST['query'];
        }else{
            $condition[$_REQUEST['qtype']] = array('like', '%' . $_REQUEST['query'] . '%');
        }

        if (is_numeric($_GET["order_sn"])) {
            $condition['order_sn'] = intval($_GET["order_sn"]);
        }
        if ($_GET['goods_name'] != ''){
            if ($_GET['jq_query']) {
                $condition['goods_name'] = $_GET['goods_name'];
            } else {
                $condition['goods_name'] = array('like',"%{$_GET['goods_name']}%");
            }
        }

        $sort_fields = array('order_sn','dis_member_id','goods_name','pay_goods_amount','dis_pay_amount');
        if (in_array($_REQUEST['sortorder'],array('asc','desc')) && in_array($_REQUEST['sortname'],$sort_fields)) {
            $order = $_REQUEST['sortname'].' '.$_REQUEST['sortorder'];
        } else {
            $order = 'id desc';
        }
        $condition['store_id'] = $_SESSION['store_id'];
        return array($condition,$order);
    }
}