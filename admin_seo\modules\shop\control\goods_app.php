<?php
/**
 * 结算管理
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');
class goods_appControl extends SystemControl{

    public function __construct(){
        parent::__construct();
    }

    public function indexOp(){
        $model_app = Model('goods_app');
        $condition  = array();
        $sort_fields = array('rec_id');
        if ($_POST['sortorder'] != '' && in_array($_POST['sortname'],$sort_fields)) {
            $order = $_POST['sortname'].' '.$_POST['sortorder'];
        } else {
            $order = "goods_app.rec_sorts asc";
        }
        if ($_POST['query'] != '' && in_array($_POST['qtype'],array('rec_gc_name'))) {
            $condition[$_POST['qtype']] = array('like',"%{$_POST['query']}%");
        }
        $field = "goods_app.*,goods.goods_name,goods.goods_marketprice,goods.goods_promotion_price,goods.goods_image,goods.store_id,goods.goods_storage";
        $total_num = $model_app->getGoodsAppCount($condition);
        $rec_list = $model_app->getGoodsRecommendList($condition,$_POST['rp'],$order,$field,'','',$total_num);
        if (is_array($rec_list) && !empty($rec_list)) {
            foreach ($rec_list as $key => $val) {
                $rec_list[$key]['goods_image'] = thumb($val,'60');
                $rec_list[$key]['rec_addtime'] = $val['rec_addtime'] > 0 ? date('Y-m-d H:i',$val['rec_addtime']): "";
                $orderCount = $this->getOrderCount($val['rec_goods_id']);
                $rec_list[$key]['order_count'] = $orderCount;
                $rec_list[$key]['order_zhl'] = $this->getOrderRate($orderCount,$val['rec_views']);
             }
        }

        $data = array();
        $data['now_page'] = $model_app->shownowpage();
        $data['total_num'] = $total_num;
        Tpl::output('rec_list', $rec_list);
        Tpl::showpage('goods_app.index');
    }

    /**
     * 新增
     */
    public function addOp(){
        $model_class = Model('goods_class');
        $gc_list = $model_class->getTreeClassList(1);
        Tpl::output('gc_list', $gc_list);

        $rec_gc_id = intval($_GET['rec_gc_id']);
        $goods_list = array();
        if ($rec_gc_id > 0) {
            $rec_list = Model('goods_app')->getGoodsAppList(array('rec_gc_id'=>$rec_gc_id),'','','*','','rec_goods_id');

            if (!empty($rec_list)) {
                $goods_id_arr = array();
                foreach ($rec_list as $val) {
                    $goods_id_arr[] = $val['rec_goods_id'];
                }
                $goods_list = Model('goods')->getGoodsOnlineList(array('goods_id'=>array('in',$goods_id_arr)),'goods_name,goods_id,goods_image');
                if (!empty($goods_list)) {
                    foreach ($goods_list as $k => $v) {
                        $goods_list[$k]['goods_image'] = thumb($v,240);
                    }
                }
            }
        }
        $rec_info['rec_gc_name'] = $rec_list[0]['rec_gc_name'];
        Tpl::output('goods_list_json',json_encode($goods_list));
        Tpl::output('goods_list', $goods_list);
        Tpl::output('rec_info', is_array($rec_list) ?  $rec_info: array());
        Tpl::showpage('goods_app.add');
    }

    /**
     * 上传图片
     */
    public function image_uploadOp() {
        $logic_goods = Logic('goods');

        $result =  $logic_goods->uploadGoodsImage($_POST['name'],1,0);

        if(!$result['state']) {
            echo json_encode(array('error' => $result['msg']));die;
        }

        echo json_encode($result['data']);die;
    }

    /**
     * 保存
     */
    public function saveOp(){
        $gc_id = intval($_POST['gc_id']);
        if (!chksubmit() || $gc_id <= 0) {
            showMessage('非法提交');
        }
        $model_app = Model('goods_app');
        /*$del = $model_app->delGoodsRecommend(array('rec_gc_id' => $gc_id));
        if (!$del) {
            showMessage('保存失败');
        }*/
        $number = 15;
        $count = $model_app->getGoodsAppCount();
        if ($count >= 15) {
            showMessage('最多只能添加15个推荐商品');
        }
        $cha = $number - $count;
        $submitCount = count($_POST['goods_id_list']);
        if ($submitCount > $cha) {
            showMessage('多添加了'.($submitCount-$cha).'个商品');
        }
        $data = array();
        $time = time();
        $rec_list = Model('goods_app')->getGoodsAppList(array('rec_gc_id'=>$gc_id),'','','*','','rec_goods_id');
        $goods_id_arr = array();
        if (!empty($rec_list)) {
            foreach ($rec_list as $val) {
                $goods_id_arr[] = $val['rec_goods_id'];
            }
        }
        if (is_array($_POST['goods_id_list'])) {
            foreach ($_POST['goods_id_list'] as $k => $goods_id) {
                if (in_array($goods_id,$goods_id_arr)) {
                    continue;
                }else {
                    $data[$k]['rec_gc_id'] = $_POST['gc_id'];
                    $data[$k]['rec_gc_name'] = rtrim($_POST['gc_name'],' >');
                    $data[$k]['rec_goods_id'] = $goods_id;
                    $data[$k]['rec_addtime'] = $time;
                }
            }
        }
        $resetData = array_values($data);
        $insert = $model_app->addGoodsRecommend($resetData);
        if ($insert) {
            showMessage('保存成功','index.php?act=goods_app&op=index');
        }
    }

    public function get_xmlOp(){
        $model_app = Model('goods_app');
        $condition  = array();
        $sort_fields = array('rec_id');
        if ($_POST['sortorder'] != '' && in_array($_POST['sortname'],$sort_fields)) {
            $order = $_POST['sortname'].' '.$_POST['sortorder'];
        }
        if ($_POST['query'] != '' && in_array($_POST['qtype'],array('rec_gc_name'))) {
            $condition[$_POST['qtype']] = array('like',"%{$_POST['query']}%");
        }
        $field = "goods_app.*,goods.goods_name,goods.goods_marketprice,goods.goods_promotion_price,goods.goods_imagel,goods.goods_storage";
        $total_num = $model_app->getGoodsAppCount($condition);
        $rec_list = $model_app->getGoodsRecommendList($condition,$_POST['rp'],$order,$field,'','',$total_num);

        $data = array();
        $data['now_page'] = $model_app->shownowpage();
        $data['total_num'] = $total_num;
        foreach ($rec_list as $v) {
            $list = array();
            $list['operation'] = "<a class='btn red' onclick=\"fg_delete({$v['rec_id']})\"><i class='fa fa-trash-o'></i>删除</a><a class='btn blue' href='index.php?act=goods_app&op=add&rec_gc_id={$v['rec_gc_id']}'><i class='fa fa-pencil-square-o'></i>编辑</a>";
            $list['rec_goods_id'] = $v['rec_goods_id'];
            $list['goods_name'] = "<a href='" . urlShop('goods', 'index', array('goods_id' => $v['rec_goods_id'])) . "' target=\"_blank\">".$v['goods_name']."</a>";
            $list['goods_price'] = ncPriceFormat($v['goods_promotion_price']);
            $list['goods_marketprice'] = ncPriceFormat($v['goods_marketprice']);
            $list['goods_image'] = "<a href='javascript:void(0);' class='pic-thumb-tip' onMouseOut='toolTip()' onMouseOver='toolTip(\"<img src=".thumb($v,'60').">\")'><i class='fa fa-picture-o'></i></a>";
            $list['rec_addtime'] = $v['rec_addtime'] > 0 ? date('Y-m-d H:i',$v['rec_addtime']): "";
            $list['rec_views'] = $v['rec_views'];
            $orderCount = $this->getOrderCount($v['rec_goods_id']);
            $list['order_count'] = $orderCount;
            $list['order_zhl'] = $this->getOrderRate($orderCount,$v['rec_views']);
            //$list['rec_sorts'] = "<a class='btn green' href='javascript:void(0)' onclick=\"ajax_form('recharge_info','编辑宠医云商品排序值','index.php?act=goods_app&op=edit_sorts&id=".$v['rec_id']."', '440')\">".$v['rec_sorts']."</a>";
            $list['rec_sorts'] = "<span title='可编辑' column_id=\"".$v['rec_id']." fieldname=\"rec_sorts\" nc_type=\"inline_edit\" class=\"editable\">".$v['rec_sorts']."</span>";
            $data['list'][$v['rec_id']] = $list;
        }
        exit(Tpl::flexigridXML($data));
    }

    public function edit_sortsOp() {
        $id = intval($_GET['id']);
        if ($id <= 0){
            showMessage(Language::get('admin_predeposit_parameter_error'),'index.php?act=goods_app&op=index','','error');
        }
        //查询充值信息
        $model_pd = Model('goods_app');
        $condition = array();
        $condition['rec_id'] = $id;
        $info = $model_pd->getGoodsRecommendInfo($condition);
        if (empty($info)){
            showMessage(Language::get('admin_predeposit_record_error'),'index.php?act=goods_app&op=index','','error');
        }
        Tpl::output('info',$info);
        Tpl::showpage('goods_app.info', 'null_layout');
    }
    //编辑图片
    public function editImgOp() {
        $rec_id = intval($_GET['id']);
        if ($rec_id <= 0){
            showMessage("参数错误",'index.php?act=goods_app&op=index','','error');
        }
        $model_app = Model('goods_app');
        $condition = array();
        $condition['rec_id'] = $rec_id;
        $goods_app = $model_app->getGoodsRecommendInfo($condition);
        $goods_app['goods_image'] = $goods_app['rec_images'];
        Tpl::output('goods_app',$goods_app);
        Tpl::showpage('goods_app.editimg');
    }
    public function saveAppimgOp() {
        $model_app = Model('goods_app');
        if (chksubmit()) {
            $rec_id = intval($_POST['rec_id']);
            if ($rec_id <= 0){
                showMessage("参数错误",'index.php?act=goods_app&op=index','','error');
            }
            $_array = array();
            $_array['rec_images'] = $_POST['image_path'];
            if ($_POST['image_path']) {
                $_array['rec_state'] = 1;
            }
            $state = $model_app->editGoodsAPP($_array, array('rec_id'=>$rec_id));
            if ($state) {
                //$this->log('编辑商品，编号'.$condition['goods_id']);
                showMessage(Language::get('nc_common_save_succ'),'index.php?act=goods_app');
            } else {
                showMessage(Language::get('nc_common_save_fail'));
            }
        }
    }
    public function editGoodsOp() {
        $model_app = Model('goods_app');
        if (chksubmit()) {
            $rec_id = intval($_POST['rec_id']);
            if ($rec_id <= 0){
                showMessage("参数错误",'index.php?act=goods_app&op=index','','error');
            }
            $_array = array();
            $_array['rec_images'] = $_POST['image_path'];
            if ($_POST['image_path']) {
                $_array['rec_state'] = 1;
            }
            $state = $model_app->editGoods($_array, array('rec_id'=>$rec_id));
            if ($state) {
                //$this->log('编辑商品，编号'.$condition['goods_id']);
                showMessage(Language::get('nc_common_save_succ'),'index.php?act=goods_app');
            } else {
                showMessage(Language::get('nc_common_save_fail'));
            }
        }
    }

    //更新排序
    public function updateSortsOp() {
        $model_pd = Model('goods_app');
        $rec_id = intval($_POST['id']);
        $sorts = intval($_POST['sorts']);
        if ($rec_id <= 0){
            exit(json_encode(array('state'=>0,'msg'=>'参数错误')));
        }
        $result = $model_pd->editGoodsAPP(array('rec_sorts'=>$sorts),array('rec_id' => $rec_id));
        if ($result) {
            exit(json_encode(array('state'=>1,'msg'=>'修改成功')));
        }else {
            exit(json_encode(array('state'=>0,'msg'=>'修改失败')));
        }

    }

    /**
     * ajax操作
     */
    public function ajaxOp(){
        switch ($_GET['branch']){
            /**
             * 排序
             */
            case 'rec_sorts':
                $model_app = Model('goods_app');
                $where = array('rec_id' => intval($_GET['id']));
                $update_array = array();
                $update_array['rec_sorts'] = $_GET['value'];
                $model_app->editGoodsAPP($update_array, $where);
                $return = 'true';
                exit(json_encode(array('result'=>$return)));
                break;
        }
    }

    //订单数量
    private function getOrderCount($goods_id) {
        $model = Model();
        $where['order_goods.goods_id'] = $goods_id;
        $where['orders.order_state'] = array('gt',10);
        $where['orders.order_from'] = array('eq',3);
        $count = $model->table('order_goods,orders')->join("left")->on("order_goods.order_id=orders.order_id")->where($where)->count();
        return $count;
    }
    private function getOrderRate($orderCOunt,$views) {
        if ($views == 0) {
            return 0;
        }else {
            $number = ($orderCOunt/$views)*100;
            if ($number > 0) {
                return round($number,2)."%";
            }else {
                return 0;
            }
        }
    }

    /**
     * 删除
     */
    public function deleteOp() {
        $model_rec = Model('goods_app');
        $condition = array();
        if (preg_match('/^[\d,]+$/', $_GET['del_id'])) {
            $_GET['del_id'] = explode(',',trim($_GET['del_id'],','));
            $condition['rec_id'] = array('in',$_GET['del_id']);
        }
        $del = $model_rec->delGoodsRecommend($condition);
        if (!$del){
            $this->log('删除宠医云商品失败',0);
            exit(json_encode(array('state'=>false,'msg'=>'删除失败')));
        }else{
            $this->log('成功删除宠医云商品',1);
            exit(json_encode(array('state'=>true,'msg'=>'删除成功')));
        }
    }

    public function get_goods_listOp(){
        $model_goods = Model('goods');
        $condition = array();
        $condition['gc_id'] = intval($_GET['gc_id']);
        if (!empty($_GET['goods_name'])) {
            $condition['goods_name'] = array('like',"%{$_GET['goods_name']}%");
        }
        $goods_list = $model_goods->getGoodsOnlineList($condition,'*',8);
        $html = "<ul class=\"dialog-goodslist-s2\">";
        foreach($goods_list as $v) {
            $url = urlShop('goods', 'index', array('goods_id' => $v['goods_id']));
            $img = thumb($v,240);
            $html .= <<<EOB
            <li>
            <div class="goods-pic" onclick="select_recommend_goods({$v['goods_id']});">
            <span class="ac-ico"></span>
            <span class="thumb size-72x72">
            <i></i>
            <img width="72" src="{$img}" goods_name="{$v['goods_name']}" goods_id="{$v['goods_id']}" title="{$v['goods_name']}">
            </span>
            </div>
            <div class="goods-name">
            <a target="_blank" href="{$url}">{$v['goods_name']}</a>
            </div>
            </li>
EOB;
        }
        $admin_tpl_url = ADMIN_TEMPLATES_URL;
        $html .= '<div class="clear"></div></ul><div id="pagination" class="pagination">'.$model_goods->showpage(1).'</div><div class="clear"></div>';
        $html .= <<<EOB
        <script>
        $('#pagination').find('.demo').ajaxContent({
                event:'click',
                loaderType:"img",
                loadingMsg:"{$admin_tpl_url}/images/transparent.gif",
                target:'#show_recommend_goods_list'
            });
        </script>
EOB;
        echo $html;
    }
}
