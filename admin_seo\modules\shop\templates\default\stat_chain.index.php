<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title">
      <div class="subject">
        <h3>门店统计</h3>
        <h5>店铺的审核及经营管理操作</h5>
      </div>
      <?php echo $output['top_link'];?>
    </div>
  </div>
  <div class="explanation" id="explanation">
    <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
      <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
      <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
    <ul>
      <li>如果当前门店处于关闭状态，前台将不能继续浏览该门店，但是店主仍然可以编辑该门店</li>
    </ul>
  </div>
  <div id="flexigrid"></div>
    <div class="ncap-search-ban-s" id="searchBarOpen"><i class="fa fa-search-plus"></i>高级搜索</div>
    <div class="ncap-search-bar">
      <div class="handle-btn" id="searchBarClose"><i class="fa fa-search-minus"></i>收起边栏</div>
      <div class="title">
        <h3>高级搜索</h3>
      </div>
      <form method="get" name="formSearch" id="formSearch">
        <div id="searchCon" class="content">
          <div class="layout-box">
            <dl>
              <dt>门店名称</dt>
              <dd>
                <input type="text" value="" name="chain_name" id="chain_name" class="s-input-txt">
              </dd>
            </dl>
              <dl>
                  <dt>门店品牌</dt>
                  <dd>
                      <select name="chain_brand_id" id="">
                          <option value="">选择品牌</option>
                          <?php if($output['chain_brand_list']){ foreach ($output['chain_brand_list']  as $val){?>
                              <option value="<?php echo $val['chain_brand_id']?>" <?php if($output['chain_info']['chain_brand_id']==$val['chain_brand_id']){echo 'selected';}?>> <?php echo $val['brand_name']?></option>
                          <?php }}?>
                      </select>
                  </dd>
              </dl>
              <dl>
                  <dt>门店大区</dt>
                  <dd>
                      <select name="region_id">
                          <option value="0">请选择大区</option>
                          <?php foreach($output['region_list'] as $value) { ?>
                              <option value="<?php echo $value['region_id'];?>" <?php if($output['chain_info']['region_id']==$value['region_id']){echo 'selected';}?>><?php echo $value['region_name'];?></option>
                          <?php } ?>
                      </select>
                  </dd>
              </dl>
              <dl>
                  <dt>日期筛选</dt>
                  <dd>
                      <label>
                          <input readonly id="query_start_date" placeholder="请选择起始时间" name="query_start_date" value="<?php echo date('Y-m-01')?>" type="text" class="s-input-txt" />
                      </label>
                      <label>
                          <input readonly id="query_end_date" placeholder="请选择结束时间" name="query_end_date" value="<?php echo date('Y-m-d')?>" type="text" class="s-input-txt" />
                      </label>
                  </dd>
              </dl>

          </div>
        </div>
        <div class="bottom">
          <a href="javascript:void(0);" id="ncsubmit" class="ncap-btn ncap-btn-green">提交查询</a>
          <a href="javascript:void(0);" id="ncreset" class="ncap-btn ncap-btn-orange" title="撤销查询结果，还原列表项所有内容"><i class="fa fa-retweet"></i><?php echo $lang['nc_cancel_search'];?></a>
        </div>
      </form>
    </div>
</div>
<script type="text/javascript">

    function update_flex(){
        $("#flexigrid").flexigrid({
            url: 'index.php?act=stat_chain&op=get_xml_chain_list&'+$("#formSearch").serialize(),
            colModel : [
                /*
                            {display: '操作', name : 'operation', width : 150, sortable : false, align: 'center', className: 'handle'},
                */
                {display: '门店ID', name : 'chain_id', width : 40, sortable : true, align: 'center'},
                {display: '门店名称', name : 'chain_name', width : 150, sortable : true, align: 'left'},
                {display: '自营', name : 'chain_name', width : 40, sortable : true, align: 'center'},
                {display: '门店账号', name : 'chain_user', width : 120, sortable : true, align: 'left'},
                {display: '实物下单会员数', name : 'chain_order_user', width : 120, sortable : true, align: 'center'},
                {display: '实物下单量', name : 'chain_order_num', width : 120, sortable : true, align: 'center'},
                {display: '实物下单总金额', name : 'chain_order_amount_num', width : 120, sortable : true, align: 'center'},
                {display: '医疗下单会员数', name : 'chain_vr_order_user', width : 120, sortable : true, align: 'center'},
                {display: '医疗下单量', name : 'chain_vr_order_num', width : 120, sortable : true, align: 'center'},
                {display: '医疗下单总金额', name : 'chain_vr_order_amount_num', width : 120, sortable : true, align: 'center'},
                {display: '当前状态', name : 'chain_state', width : 80, sortable : true, align: 'center'}
            ],
            <?php if(C('is_excelport')){?>
            buttons : [
/*
                {display: '<i class="fa fa-file-excel-o"></i>导出订单数据', name : 'csv', bclass : 'csv', title : '将选定行数据导出CSV文件', onpress : fg_operation },

                {display: '<i class="fa fa-file-excel-o"></i>门店实际核销数据', name : 'csv', bclass : 'csv', title : '将选定行数据导出CSV文件', onpress : fg_operation_code },
                */
                {display: '<i class="fa fa-file-excel-o"></i>导出门店销售数据', name : 'csv', bclass : 'csv', title : '将选定行数据导出CSV文件', onpress : fg_operation_chain },
                {display: '<i class="fa fa-file-excel-o"></i>门店核销数据', name : 'csv', bclass : 'csv', title : '将选定行数据导出CSV文件', onpress : fg_operation_code },

            ],
            <?php }?>
            searchitems : [
                {display: '门店名称', name : 'chain_name', isdefault: true}

            ],
            sortname: "chain_id",
            sortorder: "asc",
            title: '门店列表'
        });
    }



$(function () {
    //绑定时间控件
    $('#query_start_date').datepicker();
    $('#query_end_date').datepicker();

    update_flex();
    $('#ncsubmit').click(function(){
        $('.flexigrid').after('<div id="flexigrid"></div>').remove();
        update_flex();
    });

    // 高级搜索重置
    $('#ncreset').click(function(){
        $('.flexigrid').after('<div id="flexigrid"></div>').remove();
        $("#formSearch")[0].reset();
        update_flex();
    });

    $('#searchBarOpen').click();


});
function fg_operation(name, bDiv) {
    if (name == 'csv') {
        if ($('.trSelected', bDiv).length == 0) {
            if (!confirm('您确定要下载全部数据吗？')) {
                return false;
            }
        }
        var itemids = new Array();
        $('.trSelected', bDiv).each(function(i){
            itemids[i] = $(this).attr('data-id');
        });
        fg_csv(itemids);
    }
}
function fg_operation_code(name, bDiv) {
    if (name == 'csv') {
        if ($('.trSelected', bDiv).length == 0) {
            if (!confirm('您确定要下载全部数据吗？')) {
                return false;
            }
        }
        var itemids = new Array();
        $('.trSelected', bDiv).each(function(i){
            itemids[i] = $(this).attr('data-id');
        });
        fg_csv_code(itemids);
    }
}
function fg_operation_chain(name, bDiv) {
    if (name == 'csv') {
        if ($('.trSelected', bDiv).length == 0) {
            if (!confirm('您确定要下载全部数据吗？')) {
                return false;
            }
        }
        var itemids = new Array();
        $('.trSelected', bDiv).each(function(i){
            itemids[i] = $(this).attr('data-id');
        });
        fg_csv_chain(itemids);
    }
}

function fg_csv(ids) {
    id = ids.join(',');
    window.location.href = $("#flexigrid").flexSimpleSearchQueryString()+'&op=export_csv&id=' + id;
}
    function fg_csv_code(ids) {
        id = ids.join(',');
        window.location.href = $("#flexigrid").flexSimpleSearchQueryString()+'&op=export_csv_code&id=' + id;
    }

    function fg_csv_chain(ids) {
        id = ids.join(',');
        window.location.href = $("#flexigrid").flexSimpleSearchQueryString()+'&op=export_csv_chain&id=' + id;
    }
</script>