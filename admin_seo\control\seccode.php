<?php
/**
 * 验证码
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');

class seccodeControl{

    public function __construct(){
    }
    /**
     * 产生验证码
     *
     */
    public function makecodeOp(){
        $refererhost = parse_url($_SERVER['HTTP_REFERER']);
        $refererhost['host'] .= !empty($refererhost['port']) ? (':'.$refererhost['port']) : '';

        $seccode = makeSeccode($_GET['nchash']);

        @header("Expires: -1");
        @header("Cache-Control: no-store, private, post-check=0, pre-check=0, max-age=0", FALSE);
        @header("Pragma: no-cache");

        echo \Shopnc\Lib::imager()->createCaptcha($seccode, 90, 26);
    }

    /**
     * AJAX验证
     *
     */
    public function checkOp(){
        if (checkSeccode($_GET['nchash'],$_GET['captcha'])){
            exit('true');
        }else{
            exit('false');
        }
    }

    /**
     * Notes:平台账号邮箱发送
     * User: rocky
     * DateTime: 2021/9/30 16:24
     */
    public function send_auth_codeOp()
    {
        if (empty($_GET['admin_email'])){
            exit(json_encode(array('state'=>'false','msg'=>'请输入有效邮箱地址')));
        }
        $model_admin = Model('admin');
        $array  = array();
        $array['admin_email']    = $_GET['admin_email'];
        $admin_info = $model_admin->where(['admin_name'=>$_GET['admin_name'],'admin_email'=>$_GET['admin_email']])->find();
        if (empty($admin_info)){
            exit(json_encode(array('state'=>'false','msg'=>'输入信息不匹配，请重新输入！')));
        }
        $verify_code = rand(100,999).rand(100,999);
        $model_tpl = Model('mail_templates');
        $tpl_info = $model_tpl->getTplInfo(array('code'=>'authenticate'));

        $param = array();
        $param['send_time'] = date('Y-m-d H:i',TIMESTAMP);
        $param['verify_code'] = $verify_code;
        $param['site_name'] = C('site_name');
        $subject = ncReplaceText($tpl_info['title'],$param);
        $message = ncReplaceText($tpl_info['content'],$param);
        try {
            Model('realtime_msg')->send($admin_info["admin_email"],$subject,$message);
            $result = true;
        } catch (Exception $ex) {
            $result = false;
        }
        if ($result) {
            $update_data = array();
            $update_data['admin_id'] = $admin_info['admin_id'];
            $update_data['auth_code'] = $verify_code;
            $update_data['send_acode_time'] = TIMESTAMP;
            $update_data['send_acode_times'] = array('exp','send_acode_times+1');
            $update = $model_admin->updateAdmin($update_data);
            if (!$update) {
                exit(json_encode(array('state'=>'false','msg'=>'系统发生错误，如有疑问请与管理员联系')));
            }
            exit(json_encode(array('state'=>'true','msg'=>'验证码已发出，请注意查收')));
        } else {
            exit(json_encode(array('state'=>'false','msg'=>'验证码发送失败')));
        }
    }
}
