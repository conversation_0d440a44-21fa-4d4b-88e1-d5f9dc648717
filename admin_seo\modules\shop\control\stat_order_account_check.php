<?php
/**
 * 交易管理
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');
class stat_order_account_checkControl extends SystemControl{
    /**
     * 每次导出订单数量
     * @var int
     */
    const EXPORT_SIZE = 5000;

    public function __construct(){
        parent::__construct();
        Language::read('trade');
        $this->order_type_arr = array( //订单类型
            1=>"普通",
            2=>"预定",
            3=>"门店自提",
            4=>"拼团",
            5=>"门店配送",
            8=>"积分兑换",
            9=>"周期购",
            10=>"新人专享",
            11=>"预售",
            12=>"秒杀",
            99=>"助力",
            -1=>"分销",
            13=>"互联网医院"
        );
    }

    public function indexOp(){
        //显示支付接口列表(搜索)
        $payment_list = Model('payment')->getPaymentOpenList();
        $payment_list['wxpay'] = array(
            'payment_code' => 'wxpay',
            'payment_name' => '微信支付'
        );

        $payment_list['card'] = [
            'payment_code' => 'card',
            'payment_name' => '储值卡'
        ];

        Tpl::output('admin_id',$this->admin_info['id']);
        Tpl::output('payment_list',$payment_list);
        Tpl::showpage('stat_order_account_check.index');
    }

    public function get_xmlOp(){
        $model_order = Model('order');
        $model_stat = Model('stat');
        $condition  = array();

        $this->_get_condition($condition);

        $order = 'orders.order_id desc';
        $fields = "orders.order_id, orders.pay_sn, orders.order_sn, orders.pay_sn, orders.trade_no, orders.order_type, orders.add_time, orders.payment_time, orders.payment_code, orders.order_amount, orders.shipping_fee, orders.refund_amount, refund_return.refund_sn, refund_return.add_time as refund_time, orders.is_head, orders.store_name, orders.chain_id, orders.order_state, orders.is_dis, orders.order_father, orders.lock_state";
        $order_list = $model_order->getOrdersList($condition,$_POST['rp'],$fields,$order,'',array('order_goods','order_common'),'');
        $data = array();
        $data['now_page'] = $model_order->shownowpage();
        $data['total_num'] = $model_order->gettotalnum();
        $order_allnum = $model_stat->statByOrder($condition, 'sum(order_amount) as allnum');
       // $_SESSION['order_sellnum'] = $order_allnum[0]['allnum'];
        foreach ($order_list as $order_id => $order_info) {

            $list = $this->formatOrder($order_info);

            $data['list'][$order_info['order_id']] = $list;
        }
        exit(Tpl::flexigridXML($data));
    }
//    public function get_order_sellnumOp(){
//            echo json_encode(array('order_all_num'=>$_SESSION['order_sellnum']));
//    }

    /**
     * 导出订单数据
     *
     */
    public function export_step2Op(){
    	$lang   = Language::getLangContent();

    	$model_order = Model('order');
    	$condition  = array();
    	if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
    		$_GET['order_id'] = explode(',',trim($_GET['order_id'],','));
    		$condition['orders.order_id'] = array('in',$_GET['order_id']);
    	}
    	$this->_get_condition($condition);

        $order = 'orders.order_id desc';
        $fields = "orders.order_id, orders.pay_sn, orders.order_sn, orders.pay_sn, orders.trade_no, orders.order_type, orders.add_time, orders.payment_time, orders.payment_code, orders.order_amount, orders.shipping_fee, orders.refund_amount, refund_return.refund_sn, refund_return.add_time as refund_time, orders.is_head, orders.store_name, orders.chain_id,orders.order_state, orders.is_dis, orders.order_father, orders.lock_state";
    	if (!is_numeric($_GET['curpage'])){
    		$count = $model_order->getOrdersCount($condition);
    		$array = array();
    		if ($count > self::EXPORT_SIZE ){   //显示下载链接
    			$page = ceil($count/self::EXPORT_SIZE);
    			for ($i=1;$i<=$page;$i++){
    				$limit1 = ($i-1)*self::EXPORT_SIZE + 1;
    				$limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
    				$array[$i] = $limit1.' ~ '.$limit2 ;
    			}
    			Tpl::output('list',$array);
    			Tpl::output('murl','index.php?act=stat_order_account_check&op=index');
    			Tpl::showpage('export.excel');
    		}else{  //如果数量小，直接下载
    			$data = $model_order->getOrdersList($condition,'',$fields,$order,self::EXPORT_SIZE,array('order_common','chain','order_goods'));
    			$this->createExcel2($data);
    		}

    	}else{  //下载
    		$limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
    		$limit2 = self::EXPORT_SIZE;
    		$data = $model_order->getOrdersList($condition,'',$fields,$order,"{$limit1},{$limit2}",array('order_common','chain','order_goods'));
    		$this->createExcel2($data);

    	}    	
    }
    
    /**
     * 生成excel2
     *
     * @param array $data
     */
    private function createExcel2($data = array()){    	
    	Language::read('export');
    	import('libraries.excel');
    	$excel_obj = new Excel();
    	$excel_data = array();
    	//设置样式
    	$excel_obj->setStyle(array('id'=>'s_title','Font'=>array('FontName'=>'宋体','Size'=>'12','Bold'=>'1')));
    	//header
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'生成订单时间');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'支付时间');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'订单名称');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'支付方式');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'尾款支付方式');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'支付单号');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'尾款支付单号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'主单金额');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'主订单号');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'子订单号');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'退款单号');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'退款时间');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'退款金额');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'主订单状态');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'子订单状态');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'是否分销');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'门店');
    	$excel_data[0][] = array('styleid'=>'s_title','data'=>'分销员ID');
    	//data
    	foreach ((array)$data as $k=>$order_info){
            $list = $this->formatOrder($order_info);

    		$tmp = array();
    		$tmp[] = array('data'=>$list['add_time']);
    		$tmp[] = array('data'=>$list['payment_time']);
    		$tmp[] = array('data'=>$list['order_name']);
    		$tmp[] = array('data'=>$list['payment_code']);
    		$tmp[] = array('data'=>$list['last_payment_code']);
    		$tmp[] = array('data'=>$list['pay_sn']);
    		$tmp[] = array('data'=>$list['last_pay_sn']);
    		$tmp[] = array('data'=>$list['order_amount']);
    		$tmp[] = array('data'=>$list['main_order_sn']);
    		$tmp[] = array('data'=>$list['child_order_sn']);
    		$tmp[] = array('data'=>$list['refund_order_sn']);
    		$tmp[] = array('data'=>$list['refund_time']);
    		$tmp[] = array('data'=>$list['refund_amount']);
    		$tmp[] = array('data'=>$list['order_state']);
    		$tmp[] = array('data'=>$list['child_order_state']);
    		$tmp[] = array('data'=>$list['is_distribute']);
    		$tmp[] = array('data'=>$list['store_name']);
    		$tmp[] = array('data'=>$list['dis_member_id']);
            $excel_data[] = $tmp;
    	}
    	$excel_data = $excel_obj->charset($excel_data,CHARSET);
    	$excel_obj->addArray($excel_data);
    	$excel_obj->addWorksheet($excel_obj->charset(L('exp_od_order'),CHARSET));
    	$excel_obj->generateXML('order-'.$_GET['curpage'].'-'.date('Y-m-d-H',time()));
    }

    /**
     * 处理搜索条件
     */
    private function _get_condition(& $condition) {
        $condition['order_demolition'] = 0;
        if ($_REQUEST['query'] != '' && in_array($_REQUEST['qtype'],array('main_order_sn','sub_order_sn','refund_return.refund_sn', 'orders.pay_sn'))) {
            if($_REQUEST['qtype'] == 'main_order_sn') {
                $condition['cycle_push_info.parent_order_sn|orders.order_sn'] = array('like',"%{$_REQUEST['query']}%");
            } else if($_REQUEST['qtype'] == 'sub_order_sn') {
                $condition['orders.order_sn'] = array('like',"%{$_REQUEST['query']}%");
            } else {
                $condition[$_REQUEST['qtype']] = array('like',"%{$_REQUEST['query']}%");
            }
        }
        if (!in_array($_GET['qtype_time'],array('orders.add_time','orders.payment_time','orders.finnshed_time','refund_return.refund_time'))) {
            $_GET['qtype_time'] = null;
        }
        $if_start_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_start_date']);
        $if_end_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_end_date']);
        $start_unixtime = $if_start_time ? strtotime($_GET['query_start_date']) : null;
        $end_unixtime = $if_end_time ? strtotime($_GET['query_end_date']): null;
        if ($_GET['qtype_time'] && ($start_unixtime || $end_unixtime)) {
            $condition[$_GET['qtype_time']] = array('time',array($start_unixtime,$end_unixtime));
        }
        if ($_GET['order_from'] > 0) {
            if ($_GET['order_from'] == 99) { // 视频号
                $condition['orders.is_live'] = 2;
                $condition['orders.payment_code'] = 'wx_jsapi';
            } else {
                $condition['orders.order_from'] = $_GET['order_from'];
            }
        }
        $condition['orders.store_id'] = $_SESSION['store_id'];
    }

    /** 格式化订单信息
     * @param $order_info
     * @return array
     */
    private function formatOrder($order_info) {
        $cycle_push_info = Model('cycle_push_info');
        $model_order = Model('order');
        $model_chain = Model('chain');

        //周期购子订单信息
        $cycle_info = array();
        if($order_info['order_type'] == 9 && $order_info['is_head'] == 0) {
            $child_order_sn = $order_info['order_sn'];
            $cycle_info = $cycle_push_info->getPushInfo(['erp_order_sn|order_sn'=>$child_order_sn]);
        }
        //反查主订单信息
        $main_order_info = array();
        if(!empty($cycle_info)) {
            if($cycle_info['parent_order_sn']) {
                $main_order_info = $model_order->getOrderInfo(['order_sn'=>$cycle_info['parent_order_sn']]);
            }
        }
        //获取预售订单信息
        $preOrderInfo = array();
        if($order_info['order_type'] == 11) {
            $preOrderInfo = \Upet\Models\OrderPresale::getPreOrderInfo(['erp_order_sn'=>$order_info['order_sn']]);
        }
        //获取门店信息
        $chainInfo = array();
        if($order_info['chain_id'] > 0) {
            $chainInfo = $model_chain->getChainInfo(['chain_id'=>$order_info['chain_id']],'chain_name');
        }
        //取得订单其它扩展信息
        $model_order->getOrderExtendInfo($order_info);

        $order_info['state_desc'] = orderState($order_info);
        $order_type = '';
        if(isset($this->order_type_arr[$order_info['order_type']])){
            $order_type = $this->order_type_arr[$order_info['order_type']];
        }

        if($order_info['order_type'] == 9 ) { //周期购订单
            if ($order_info['is_head'] == 1) {
                $order_info['order_name'] = $order_type.'_主单';

                $order_info['order_state'] = $order_info['state_desc'];
                $order_info['child_order_state'] = '';

                $order_info['main_order_sn'] = $order_info['order_sn'];
                $order_info['child_order_sn'] = '';
            } else {
                $order_info['order_name'] = $order_type.'_子单';

                $order_info['order_state'] = '';
                $order_info['child_order_state'] = $order_info['state_desc'];

                $order_info['main_order_sn'] = isset($cycle_info['parent_order_sn']) ? $cycle_info['parent_order_sn'] : '';
                $order_info['child_order_sn'] = $order_info['order_sn'].'_'.$cycle_info['push_type'].'/'.$cycle_info['cycle_num'];
            }
        } else if($order_info['order_type'] == 11) { //预售订单
            $order_info['order_name'] = $order_type;
            $order_info['main_order_sn'] = $preOrderInfo['parent_order_sn'];
            $order_info['child_order_sn'] = $preOrderInfo['erp_order_sn'];
            $order_info['order_state'] = '';
            $order_info['child_order_state'] = $order_info['state_desc'];
            if($preOrderInfo['pre_status'] == 1) {
                $order_info['order_amount_other'] = '(定金: '.$preOrderInfo['pre_price'].')';
            } else if($preOrderInfo['pre_status'] == 2) {
                $order_info['main_order_sn'] = '';
                $order_info['last_payment_code'] = $preOrderInfo['last_payment_code'];
                $order_info['last_pay_sn'] = $preOrderInfo['last_pay_sn'];
                $order_info['order_amount_other'] = '(定金:'.$preOrderInfo['pre_price'].' 尾款: '.$preOrderInfo['last_price'].')';
            }

        } else {

            $order_info['order_name'] = $order_type;

            if($order_info['order_father'] == 0) {
                $order_info['order_state'] = $order_info['state_desc'];
                $order_info['child_order_state'] = '';

                $order_info['main_order_sn'] = $order_info['order_sn'];
                $order_info['child_order_sn'] = '';
            } else {
                $order_info['order_state'] = '';
                $order_info['child_order_state'] = $order_info['state_desc'];

                $order_info['main_order_sn'] = '';
                $order_info['child_order_sn'] = $order_info['order_sn'];
            }
        }

        $list = array();
        $list['add_time'] = date('Y-m-d H:i:s',$order_info['add_time']);
        $list['payment_time'] = !empty($order_info['payment_time']) ? (intval(date('His',$order_info['payment_time'])) ? date('Y-m-d H:i:s',$order_info['payment_time']) : date('Y-m-d',$order_info['payment_time'])) : '';
        if($order_info['is_dis'] == 1) {
            $order_type = '分销';
        }
        $list['order_name'] = $order_info['order_name'];
        $list['payment_code'] = orderPaymentName($order_info['payment_code']);
        $list['last_payment_code'] = orderPaymentName($order_info['last_payment_code']);
        $list['pay_sn'] = $order_info['pay_sn'];
        $list['last_pay_sn'] = $order_info['last_pay_sn'];
        $list['order_amount'] = ncPriceFormat($order_info['order_amount']);
        if ($order_info['shipping_fee']) {
            $list['order_amount'] .= '(含运费'.ncPriceFormat($order_info['shipping_fee']).')'.$order_info['order_amount_other'];
        }
        $list['main_order_sn'] = $order_info['main_order_sn'];
        $list['child_order_sn'] = $order_info['child_order_sn'];
        $list['refund_order_sn'] = isset($order_info['refund_sn']) ? $order_info['refund_sn']: '';
        $list['refund_time'] = isset($order_info['refund_time']) ?  date('Y-m-d H:i:s',$order_info['refund_time']): '';
        $list['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
        $list['order_state'] = $order_info['order_state'];
        $list['child_order_state'] = $order_info['child_order_state'];
        $list['is_distribute'] = $order_info['is_dis'] == 1 ? '是': '否';
        $list['store_name'] = isset($chainInfo['chain_name']) ? $chainInfo['chain_name'] : '';
        $list['dis_member_id'] = $order_info['extend_order_goods'][0]['dis_member_id'];
        return $list;
    }
}
