<?php defined('InShopNC') or exit('Access Invalid!');?>
  <div class="ncap-form-default">
    <dl class="row">
      <dt class="tit">
        <label>结算编号</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['log_id']; ?>
        <p class="notic"></p>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit">
        <label>订单编号</label>
      </dt>
      <dd class="opt"><?php if ($output['info']['order_sn']){
              echo $output['info']['order_sn'];
          }else{
              echo "-";
          }
          ?>
        <p class="notic"></p>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit">
        <label>商品名称</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['goods_name']; ?>
        <p class="notic"></p>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit">
        <label>支付金额</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['pay_goods_amount']; ?>
        <p class="notic"></p>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit">
        <label>分销佣金比例</label>
      </dt>
      <dd class="opt"><?php echo floatval($output['info']['dis_commis_rate']); ?>%
        <p class="notic"></p>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit">
        <label>分销佣金</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['dis_pay_amount']; ?>
        <p class="notic"></p>
      </dd>
    </dl>

      <dl class="row">
          <dt class="tit">
              <label>分销员ID</label>
          </dt>
          <dd class="opt"><?php echo $output['info']['dis_member_id']; ?>
              <p class="notic"></p>
          </dd>
      </dl>
    <div class="bot" id="submit-holder"><a class="ncap-btn-big ncap-btn-green" href="javascript:modify_win()">更改</a>&nbsp;&nbsp;&nbsp;&nbsp;<a class="ncap-btn-big ncap-btn-red" href="javascript:;" onclick="close_win()" style="margin-right: 100px;">关闭</a> </div>

  </div>

<script>
    function modify_win(id){
        ajax_form('bill_info','更改分销员', 'index.php?act=distri_bill&op=bill_modify&id=<?php echo $output['info']['log_id']; ?>', 840)
    }

    function close_win()
    {
        $('.dialog_close_button').click();
    }


</script>