<?php
defined('InShopNC') or exit('Access Invalid!');
$lang['shareset_list_tip'] 		= '开启并设置以下接口配置信息后，SNS分享店铺和商品信息功能中将可以使用站外分享信息功能';
$lang['shareset_list_appname'] 		= '应用名称';
$lang['shareset_list_appurl'] 		= '网址';
$lang['shareset_list_appstate'] 	= '启用状态';
$lang['shareset_list_closeprompt'] 	= '确认要关闭该接口吗？';


$lang['shareset_edit_title'] 		= '接口设置';
$lang['shareset_edit_appisuse'] 	= '是否启用该接口';
$lang['shareset_edit_appcode'] 		= '域名验证信息';
$lang['shareset_edit_appid'] 		= '应用标识';
$lang['shareset_edit_applylike'] 		= '立即在线申请';
$lang['shareset_edit_appkey'] 		= '应用密钥';