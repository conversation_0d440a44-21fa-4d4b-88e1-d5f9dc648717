<?php
/**
 * 交易管理
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Box\Spout\Common\Type;
use Box\Spout\Writer\Style\StyleBuilder;
use Box\Spout\Writer\WriterFactory;
use Shopnc\Tpl;
use Upet\Models\OrderPresale;
use Upet\Models\RefundPreOrder;
use Upet\Integrates\Redis\RedisManager as Redis;

defined('InShopNC') or exit('Access Invalid!');
class orderControl extends SystemControl{
    /**
     * 每次导出订单数量
     * @var int
     */
    const EXPORT_SIZE = 5000;

    public function __construct(){
        parent::__construct();
        Language::read('trade');
    }

    public function indexOp(){
        //显示支付接口列表(搜索)
        $payment_list = Model('payment')->getPaymentOpenList();
        $payment_list['wxpay'] = array(
            'payment_code' => 'wxpay',
            'payment_name' => '微信支付'
        );

        $payment_list['card'] = [
            'payment_code' => 'card',
            'payment_name' => '储值卡'
        ];
        $order_type_list = array( //订单类型
            1=>"普通订单",
            4=>"拼团订单",
            9=>"周期购订单",
            10=>"新人订单",
            11=>"预售订单",
            12=>"秒杀订单",
            19=>"付费会员礼包",
            21=>"付费会员实物订单",
            99=>"助力订单",
            -1=>"分销订单",
            13=>"互联网医院",
            -3=>"互联网推荐",
            -2=>"电商订单",
        );

        Tpl::output('admin_id',$this->admin_info['id']);
        Tpl::output('payment_list',$payment_list);
        Tpl::output('order_type_list',$order_type_list);
        Tpl::showpage('order.index');
    }

    public function get_xmlOp(){
        $model_order = Model('order');
        $model_address = Model('address');
        $model_goods   = Model('goods');
        $model_stat = Model('stat');
        $condition  = array();
        $order_type_arr = array( //订单类型
            1=>"",
            2=>"[预定]",
            3=>"[门店自提]",
            4=>"[拼团]",
            5=>"[门店配送]",
            8=>"[积分兑换]",
            9=>"[周期购]",
            10=>"[新人订单]",
            11=>"[预售]",
            12=>"[秒杀]",
            13=>"[医院]",
            19=>"[付费会员礼包]",
            21=>"[付费会员实物订单]",
            99=>"[助力]",
            -1=>"[分销]"
        );

        $this->_get_condition($condition);
        $sort_fields = array('buyer_name','store_name','order_id','payment_code','order_state','order_amount','order_from','pay_sn','rcb_amount','pd_amount','payment_time','finnshed_time','evaluation_state','refund_amount','buyer_id','store_id');
        if ($_POST['sortorder'] != '' && in_array($_POST['sortname'],$sort_fields)) {
            $order = $_POST['sortname'].' '.$_POST['sortorder'];
        }

        $order_list = $model_order->getOrderList($condition,$_POST['rp'],'*',$order,'',array('order_goods','order_common'),'');
        $data = array();
        $data['now_page'] = $model_order->shownowpage();
        $data['total_num'] = $model_order->gettotalnum();
        $order_allnum = $model_stat->statByOrder($condition, 'sum(order_amount) as allnum');
        $_SESSION['order_sellnum_'.$_SESSION['store_id']] = $order_allnum[0]['allnum'];


        // 门店列表
        $chain_key_list = Model('chain')->getChainKeybyId($_SESSION['store_id']);
        foreach ($order_list as $order_id => $order_info) {
            $order_info['if_system_cancel'] = $model_order->getOrderOperateState('system_cancel',$order_info);
            $order_info['if_system_receive_pay'] = $model_order->getOrderOperateState('system_receive_pay',$order_info);
            $order_info['state_desc'] = orderState($order_info);
				
            //取得订单其它扩展信息
            $model_order->getOrderExtendInfo($order_info);

            $list = array();
            $operation_detail = '';
            $list['operation'] = "<a class=\"btn green\" href=\"index.php?act=order&op=show_order&order_id={$order_info['order_id']}\"><i class=\"fa fa-list-alt\"></i>查看</a>";
            if ($order_info['if_system_cancel']) {
                $operation_detail .= "<li><a href=\"javascript:void(0);\" onclick=\"fg_cancel({$order_info['order_id']})\">取消订单</a></li>";
            }
            if ($order_info['if_system_receive_pay']) {
                $op_name = $order_info['system_receive_pay_op_name'] ? $order_info['system_receive_pay_op_name'] : '收到货款';
                $operation_detail .= "<li><a href=\"index.php?act=order&op=change_state&state_type=receive_pay&order_id={$order_info['order_id']}\">{$op_name}</a></li>";
            }
            if ($operation_detail) {
                $list['operation'] .= "<span class='btn'><em><i class='fa fa-cog'></i>设置 <i class='arrow'></i></em><ul>{$operation_detail}</ul>";
            }
            $order_type = '';
            if(isset($order_type_arr[$order_info['order_type']])){
                $order_type .= $order_type_arr[$order_info['order_type']];
            }
            if($order_info['is_dis'] == 1) {
                $order_type .= '[分销]';
            }
            $list['order_sn'] = $order_info['order_sn'].$order_type;
            $list['order_from'] = orderFromName($order_info['order_from']);
            $list['chain_name'] = $order_info['chain_id'] ? $chain_key_list[$order_info['chain_id']]['chain_name']:$this->initShopCode[$_SESSION['store_id']]['shop_code'];
            $list['account_id'] = $order_info['chain_id'] ? $chain_key_list[$order_info['chain_id']]['account_id']:$this->initShopCode[$_SESSION['store_id']]['shop_name'];
            //  ******** michael修改
            $dis_member_ids = [];
            if(isset($order_info['extend_order_goods']) && !empty($order_info['extend_order_goods'])) {
            	foreach($order_info['extend_order_goods'] as $v) {
            	    $list['goods_name'] .= rtrim($v['goods_name'].",");
            		$list['goods_num'] += $v['goods_num'];
                    if ($v['dis_member_id']) {
                        $dis_member_ids[$v['order_id']] = $v['dis_member_id'];
                    }
            		// ******** michael修改
            		$product_info = $model_goods->getCustomGoodsAttribute($v['goods_id']);
            		if(is_array($product_info)&& count($product_info)>0) {
            			foreach ($product_info as $k=>$value) {
            				if(is_array($value)) {
            					foreach ($value as $k1=>$v1) {
            						if($value[$k1]=='规格') {
            							$list['goods_spec'] = $value['value'];
            						}
            						if($value[$k1]=='单位') {
            							$list['goods_unit'] = $value['value'];
            						}
            					}
            				}
            			}
            		}
            	}
            }else {
            	$list['goods_name'] = '';
            	$list['goods_num'] = '';
            }
            $list['add_times'] = date('Y-m-d H:i:s',$order_info['add_time']);
			$list['order_amount'] = ncPriceFormat($order_info['order_amount']);
			if ($order_info['shipping_fee']) {
			    $list['order_amount'] .= '(含运费'.ncPriceFormat($order_info['shipping_fee']).')';
			}
			$list['order_state'] = $order_info['state_desc'];
            $list['pay_sn'] = empty($order_info['pay_sn']) ? '' : $order_info['pay_sn'];
			$list['payment_code'] = orderPaymentName($order_info['payment_code']);
			$list['payment_time'] = !empty($order_info['payment_time']) ? (intval(date('His',$order_info['payment_time'])) ? date('Y-m-d H:i:s',$order_info['payment_time']) : date('Y-m-d',$order_info['payment_time'])) : '';
            $list['shipping_code'] = $order_info['shipping_code'];
            $list['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
			$list['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s',$order_info['finnshed_time']) : '';
			$list['evaluation_state'] = str_replace(array(0,1,2), array('未评价','已评价','未评价'),$order_info['evaluation_state']);
			$list['store_id'] = $order_info['store_id'];
			$list['store_name'] = $order_info['store_name'];
			$list['dis_member_id'] = $dis_member_ids[$order_id];
			$list['buyer_id'] = $order_info['buyer_id'];
			$list['buyer_name'] = $order_info['buyer_name'];
			$list['buyer_address'] = $order_info['extend_order_common']['reciver_info']['address'];
			$data['list'][$order_info['order_id']] = $list;
        }
        exit(Tpl::flexigridXML($data));
    }


    public function get_order_sellnumOp(){
            echo json_encode(array('order_all_num'=>$_SESSION['order_sellnum_'.$_SESSION['store_id']]));
    }
    /**
     * 平台订单状态操作
     *
     */
    public function change_stateOp() {
        $order_id = intval($_GET['order_id']);
        if($order_id <= 0){
            showMessage(L('miss_order_number'),$_POST['ref_url'],'html','error');
        }
        $model_order = Model('order');

        //获取订单详细
        $condition = array();
        $condition['order_id'] = $order_id;
        $order_info = $model_order->getOrderInfo($condition);

        //取得其它订单类型的信息
        $model_order->getOrderExtendInfo($order_info);

        if ($_GET['state_type'] == 'cancel') {
            $result = $this->_order_cancel($order_info);
        } elseif ($_GET['state_type'] == 'receive_pay') {
            $result = $this->_order_receive_pay($order_info,$_POST);
        }
        if (!$result['state']) {
            showMessage($result['msg'],$_POST['ref_url'],'html','error');
        } else {
            showMessage($result['msg'],$_POST['ref_url']);
        }
    }

    /**
     * 系统取消订单
     */
    private function _order_cancel($order_info) {
        $order_id = $order_info['order_id'];
        $model_order = Model('order');
        $logic_order = Logic('order');
        $if_allow = $model_order->getOrderOperateState('system_cancel',$order_info);
        if (!$if_allow) {
            return callback(false,'无权操作');
        }
        if (TIMESTAMP - 86400 < $order_info['api_pay_time']) {
            $_hour = ceil(($order_info['api_pay_time']+86400-TIMESTAMP)/3600);
            exit(json_encode(array('state'=>false,'msg'=>'该订单曾尝试使用第三方支付平台支付，须在'.$_hour.'小时以后才可取消')));
        }
        if ($order_info['order_type'] == 2) {
            //预定订单
            $result = Logic('order_book')->changeOrderStateCancel($order_info, 'admin', $this->admin_info['name']);
        } else {
            $cancel_condition = array();
            if ($order_info['payment_code'] != 'offline') {
                $cancel_condition['order_state'] = ORDER_STATE_NEW;
            }
            $result =  $logic_order->changeOrderStateCancel($order_info,'admin', $this->admin_info['name'],'',true,$cancel_condition);
        }
        if ($result['state']) {
            $this->log(L('order_log_cancel').','.L('order_number').':'.$order_info['order_sn'],1);
        }
        if ($result['state']) {
            exit(json_encode(array('state'=>true,'msg'=>'取消成功')));
        } else {
            exit(json_encode(array('state'=>false,'msg'=>'取消失败')));
        }
    }

    /**
     * 系统收到货款
     * @throws Exception
     */
    private function _order_receive_pay($order_info, $post) {
        $order_id = $order_info['order_id'];
        $model_order = Model('order');
        $logic_order = Logic('order');
        $order_info['if_system_receive_pay'] = $model_order->getOrderOperateState('system_receive_pay',$order_info);

        if (!$order_info['if_system_receive_pay']) {
            return callback(false,'无权操作');
        }

        if (!chksubmit()) {
            Tpl::output('order_info',$order_info);
            //显示支付接口列表
            $payment_list = Model('payment')->getPaymentOpenList();
            //去掉预存款和货到付款
            foreach ($payment_list as $key => $value){
                if ($value['payment_code'] == 'predeposit' || $value['payment_code'] == 'offline') {
                   unset($payment_list[$key]);
                }
            }
            Tpl::output('payment_list',$payment_list);
            Tpl::showpage('order.receive_pay');
            exit();
        }
        //预定支付尾款时需要用到已经支付的状态
        $order_list = $model_order->getOrderList(array('pay_sn'=>$order_info['pay_sn'],'order_state'=>array('in',array(ORDER_STATE_NEW,ORDER_STATE_PAY))));

        //取订单其它扩展信息
        $result = Logic('payment')->getOrderExtendList($order_list,'admin');
        if (!$result['state']) {
            return $result;
        }
        $result = $logic_order->changeOrderReceivePay($order_list,'admin',$this->admin_info['name'],$post);
        if ($result['state']) {
            $this->log('将订单改为已收款状态,'.L('order_number').':'.$order_info['order_sn'],1);
            //记录消费日志
            $api_pay_amount = $order_info['order_amount'] - $order_info['pd_amount'] - $order_info['rcb_amount'];
            QueueClient::push('addConsume', array('member_id'=>$order_info['buyer_id'],'member_name'=>$order_info['buyer_name'],
            'consume_amount'=>$api_pay_amount,'consume_time'=>TIMESTAMP,'consume_remark'=>'管理员更改订单为已收款状态，订单号：'.$order_info['order_sn']));
        }
        return $result;
    }

    /**
     * 查看订单
     *
     */
    public function show_orderOp(){
        $order_id = intval($_GET['order_id']);
        if($order_id <= 0 ){
            showMessage(L('miss_order_number'));
        }
        $model_refund = Model('refund_return');
        $model_order    = Model('order');
        $order_info = $model_order->getOrderInfo(array('order_id'=>$order_id),array('order_goods','order_common','store'));
        $order_list = array();
        $order_list[$order_id] = $order_info;
        $order_list = $model_refund->getGoodsRefundList($order_list, 1);//订单商品的退款退货显示
        $order_info = $order_list[$order_id];
        $order_info['if_refund_cancel'] = $model_order->getOrderOperateState('refund_cancel',$order_info);
        $order_info['if_part_refund'] = $model_order->getOrderOperateState('part_refund',$order_info);
        $allGift = true;
        foreach ((array)$order_info['extend_order_goods'] as $value) {
            if ($value['goods_type'] != 5){
                $allGift = false;
                break;
            }
        }

        foreach ($order_info['extend_order_goods'] as $value) {
            $value['image_60_url'] = cthumb($value['goods_image'], 60, $value['store_id']);
            $value['image_240_url'] = cthumb($value['goods_image'], 240, $value['store_id']);
            $value['goods_type_cn'] = orderGoodsType($value['goods_type']);
            $value['goods_url'] = urlShop('goods','index',array('goods_id'=>$value['goods_id']));
            if ($value['goods_type'] == 5 && !$allGift) {
                $order_info['zengpin_list'][] = $value;
            } else {
                $order_info['goods_list'][] = $value;
            }
        }
        
        if (empty($order_info['zengpin_list'])) {
            $order_info['goods_count'] = count($order_info['goods_list']);
        } else {
            $order_info['goods_count'] = count($order_info['goods_list'] ?: []) + 1;
        }

        //取得订单其它扩展信息
        $model_order->getOrderExtendInfo($order_info);

        //订单变更日志
        $log_list   = $model_order->getOrderLogList(array('order_id'=>$order_info['order_id']));
        Tpl::output('order_log',$log_list);

        //退款退货信息
        $condition = array();
        $condition['order_id'] = $order_info['order_id'];
        $condition['seller_state'] = 2;
        $condition['admin_time'] = array('gt',0);
        $return_list = $model_refund->getReturnList($condition);
        Tpl::output('return_list',$return_list);

        //退款信息
        $refund_list = $model_refund->getRefundList($condition);
        Tpl::output('refund_list',$refund_list);

        //商家信息
        $store_info = Model('store')->getStoreInfo(array('store_id'=>$order_info['store_id']));
        Tpl::output('store_info',$store_info);

        //商家发货信息
        if (!empty($order_info['extend_order_common']['daddress_id'])) {
            $daddress_info = Model('daddress')->getAddressInfo(array('address_id'=>$order_info['extend_order_common']['daddress_id']));
            Tpl::output('daddress_info',$daddress_info);
        }

        //显示快递信息
        if ($order_info['shipping_code'] != '') {
            $express = rkcache('express',true);
            $order_info['express_info']['e_code'] = $express[$order_info['extend_order_common']['shipping_express_id']]['e_code'];
            $order_info['express_info']['e_name'] = $express[$order_info['extend_order_common']['shipping_express_id']]['e_name'];
            $order_info['express_info']['e_url'] = $express[$order_info['extend_order_common']['shipping_express_id']]['e_url'];
        }

        //如果订单已取消，取得取消原因、时间，操作人
        if ($order_info['order_state'] == ORDER_STATE_CANCEL) {
            $order_info['close_info'] = $model_order->getOrderLogInfo(array('order_id'=>$order_info['order_id'],'log_orderstate'=>ORDER_STATE_CANCEL),'log_id desc');
        }

        //如果订单已支付，取支付日志信息(主要是第三方平台支付单号)
        if ($order_info['order_state'] == ORDER_STATE_PAY) {
            $order_info['pay_info'] = $model_order->getOrderLogInfo(array('order_id'=>$order_info['order_id'],'log_orderstate'=>ORDER_STATE_PAY),'log_id desc');
        }
        //如果存在门店信息，显示门店信息
        if ($order_info['chain_id']) {
            $chain_model = Model('chain');
            $chain_info = $chain_model->getChainInfo(array('chain_id' => $order_info['chain_id']),"chain_name,account_id");
            $order_info['chain_name'] = $chain_info['chain_name'];
            $order_info['account_id'] = $chain_info['account_id'];
        }else{
            $order_info['chain_name'] =$this->initShopCode[$_SESSION['store_id']]['shop_name'];
            $order_info['account_id'] = $this->initShopCode[$_SESSION['store_id']]['shop_code'];
        }
        //周期购订单信息
        if($order_info['order_type'] == 9) {
            $cycle_order_sn_arr[] = $order_info['order_sn'];
            if($order_info['is_head'] == 1) {
                $push_info_model = Model('cycle_push_info');
                $push_info_list = $push_info_model->getPushInfo(['order_sn'=>$order_info['order_sn']]);
                $delivery_interval = $push_info_list['delivery_interval']; //发货间隔
                $delivery_times = $push_info_list['push_date']; //所有推送期数
                $push_num = $push_info_list['push_num']; //已推送期数
                $delivery_times_arr = explode(",", $delivery_times);
                $delivery_list = [];
                foreach($delivery_times_arr as $i=>$time) {
                    $delivery_list[$i]['date_desc'] = '第'.($i+1).'期';
                    if ($i == 0) {
                        $delivery_list[$i]['delivery_date'] = '支付后';
                    }else{
                        $delivery_list[$i]['delivery_date'] = date("m", strtotime($time)).'月'.date('d',strtotime($time)).'日起';
                    }
                    $delivery_list[$i]['delivery_info'] = '48小时内发货';
                    $delivery_list[$i]['state'] = 0;
                    if ($push_num > 0 && $i < $push_num){
                        $delivery_list[$i]['state'] = 1;
                    }
                }
                $order_info['delivery_info'] = [
                    "delivery_interval"=>$delivery_interval,
                    "delivery_times"=>$delivery_list,
                ];

                //获取所有子订单
                $childOrders = $push_info_model->getPushInfoList(['parent_order_sn'=>$order_info['order_sn'], 'is_head'=>0], "*", "id asc");
                $erpOrderSn = array();
                if(!empty($childOrders)) {
                    foreach($childOrders as $child) {
                        $cycle_order_sn_arr[] = $child['erp_order_sn'];
                        $erpOrderSn[] = $child['erp_order_sn'];
                    }
                }
                $pushed_condition['cycle_push_info.erp_order_sn'] = array('in', $erpOrderSn);
                $sub_order_info = $model_order->getPushedOrders($pushed_condition, "order_id asc", array('order_goods','order_common','member'));
                Tpl::output('sub_order_info',$sub_order_info);
            } else if($order_info['is_head'] == 0) {
                $push_info_model = Model('cycle_push_info');
                $push_condition['order_sn'] = $order_info['order_sn'];
                $push_condition['erp_order_sn'] = $order_info['order_sn'];
                $push_condition['_op'] = 'or';
                $push_info = $push_info_model->getPushInfo($push_condition, "parent_order_sn");
                Tpl::output('parent_order_info',$push_info);
            }
            // 计算总退款金额
            $order_info['cycle_refund_total'] = \Upet\Models\RefundReturn::whereIn('order_sn', $cycle_order_sn_arr)->where('dy_state',2)->sum('refund_amount');
        }
        //预售订单信息
        if ($order_info['order_type'] == 11){
            $order_info['pre_data'] = $this->_getPreOrderInfo($order_info);
            $model = new OrderPresale();
            $pre_info = $model->where('erp_order_sn', $order_info['order_sn'])->find();
            Tpl::output('pre_info',$pre_info);
        }
        //判断显示运费退款,如果运费大于0，且订单状态为30或40，且完成时间15天内
        $order_info['show_refund_shipping_fee'] = false;

        if ($order_info['shipping_fee'] > 0 && $order_info['lock_state'] ==0 &&  (
            $order_info['order_state'] == 30 ||
            //order_state=40 且finnshed_time的时间戳在15天之内
            (($order_info['order_state'] == 40 && $order_info['finnshed_time'] > (time()-15*24*3600))
            ))) {
            $order_info['show_refund_shipping_fee'] = true;
        }

        Tpl::output('order_info',$order_info);
        Tpl::showpage('order.view');
    }
    /**
     * 添加全部退款即取消订单
     *
     */
    public function add_refund_allOp(){
        $model_trade = Model('trade');
        $model_refund = Model('refund_return');
        $order_id = intval($_GET['order_id']);
        $condition = array();
        $condition['order_id'] = $order_id;
        $order = $model_refund->getRightOrderList($condition);

        //禁止退款金额
        $lock_amount = Logic('order_book')->getDepositAmount($order);
        $order['allow_refund_amount'] = $order['order_amount'] - $lock_amount-$order['refund_amount'];
        Tpl::output('order',$order);
//        $order_amount = $order['allow_refund_amount'];//订单金额
        $condition = array();
        $condition['buyer_id'] = $order['buyer_id'];
        $condition['order_id'] = $order['order_id'];
        $condition['goods_id'] = '0';
        $condition['seller_state'] = 1;
        $refund_list = $model_refund->getRefundReturnList($condition);
        $refund = array();
        if (!empty($refund_list) && is_array($refund_list)) {
            $refund = $refund_list[0];
        }
        $order_paid = $model_trade->getOrderState('order_paid');//订单状态20:已付款
        $payment_code = $order['payment_code'];//支付方式
        if ($refund['refund_id'] > 0 || !in_array($order['order_state'],[20,30,40])  || $payment_code == 'offline') {//检查订单状态,防止页面刷新不及时造成数据错误
            showDialog(Language::get('wrong_argument'),'reload','error');
        }
        if (chksubmit()) {
            $refund_amount = floatval($_POST['refund_amount']);//退款金额
            if (($refund_amount < 0) || ($refund_amount > $order['order_amount'])) {
                $refund_amount = $order['order_amount'];
            }
            $refund_array = array();
            $refund_array['refund_type'] = '1';//类型:1为退款,2为退货
            $refund_array['seller_state'] = '1';//状态:1为待审核,2为同意,3为不同意
            $refund_array['order_lock'] = '2';//锁定类型:1为不用锁定,2为需要锁定
            $refund_array['goods_id'] = '0';
            $refund_array['order_goods_id'] = '0';
            $refund_array['reason_id'] = '0';
            $refund_array['reason_info'] = '取消订单，全部退款';
            $refund_array['goods_name'] = '订单商品全部退款';
            $refund_array['refund_amount'] = ncPriceFormat($refund_amount);
            $refund_array['buyer_message'] = $_POST['buyer_message'];
            $refund_array['add_time'] = time();
            //判断周期购主订单退款，不走数据中心
            if ($order['order_type'] == 9 && $order['is_head'] == 1){

            }else {
                $erp_refund['order_id'] = $order_id;
                $erp_refund['add_time'] = $refund_array['add_time'];
                $erp_refund['order_sn'] = $order['order_sn'];
                $erp_refund['refund_amount'] = $refund_array['refund_amount'];
                $erp_refund_sn = $model_refund->getRefundsn($order['store_id']);
                $erp_refund['refund_sn'] = $erp_refund_sn;
                $erp_refund['refund_type'] = $refund_array['refund_type'];
                $erp_refund['reason_info'] = $refund_array['reason_info'];
                $erp_refund['buyer_message'] = $refund_array['buyer_message'];
                $erp_refund['goods_id'] = $refund_array['goods_id'];
                $erp_refund['refund_pic'] = '';
                //退款售后同步ERP数据中心
                try {
                    $erp_result = Logic('refund')->refundOrderApply($erp_refund);
                }catch (Exception $e){
                    showDialog($e->getMessage());
                }

                $refund_array['refund_order_sn'] = $erp_result['refund_order_sn'];
            }
            $state = $model_refund->addRefundReturn($refund_array,$order);

            if ($state) {
                $model_refund->editOrderLock($order_id);
                //判断周期购
                if ($order['order_type'] == 9) {
                    Model('cycle_push_info')->refundMainOrder($order);
                }
                showDialog(Language::get('nc_common_save_succ'),'reload','succ', 'CUR_DIALOG.close();');
            } else {
                showDialog(Language::get('nc_common_save_fail'),'reload','error');
            }
        }
        Tpl::showpage('order_refund_all', 'null_layout');
    }

    /**
     * 添加订单商品部分退款
     *
     */
    public function add_refundOp(){
        $model_refund = Model('refund_return');
        $condition = array();
        $reason_list = $model_refund->getReasonList($condition);//退款退货原因
        Tpl::output('reason_list',$reason_list);
        $order_id = intval($_GET['order_id']);
        $goods_id = intval($_GET['goods_id']);//订单商品表编号
        if ($order_id < 1 || $goods_id < 1) {//参数验证
            showDialog(Language::get('wrong_argument'),'reload','error');
        }
        $condition = array();
        $condition['order_id'] = $order_id;
        $order = $model_refund->getRightOrderList($condition, $goods_id);
        $order_id = $order['order_id'];
        $order_amount = $order['order_amount'];//订单金额
        $order_refund_amount = $order['refund_amount'];//订单退款金额
        $goods_list = $order['goods_list'];
        $goods = $goods_list[0];
        $goods_pay_price = $goods['goods_pay_price'];//商品实际成交价
        if ($order_amount < ($goods_pay_price + $order_refund_amount)) {
            $goods_pay_price = $order_amount - $order_refund_amount;
            $goods['goods_pay_price'] = $goods_pay_price;
        }
        Tpl::output('goods',$goods);

        $goods_id = $goods['rec_id'];
        $condition = array();
        $condition['buyer_id'] = $order['buyer_id'];
        $condition['order_id'] = $order['order_id'];
        $condition['order_goods_id'] = $goods_id;
        $condition['seller_state'] = array('lt','3');
        $refund_list = $model_refund->getRefundReturnList($condition);
        $refund = array();
        if (!empty($refund_list) && is_array($refund_list)) {
            $refund = $refund_list[0];
        }
        $refund_state = $model_refund->getRefundState($order);//根据订单状态判断是否可以退款退货
        if ($refund_state != 1) {//检查订单状态,防止页面刷新不及时造成数据错误
            showDialog(Language::get('wrong_argument'),'reload','error');
        }
        if (chksubmit() && $goods_id > 0){
            $refund_array = array();
            $refund_amount = floatval($_POST['refund_amount']);//退款金额
            if (($refund_amount < 0) || ($refund_amount > $goods_pay_price)) {
                $refund_amount = $goods_pay_price;
            }
            $goods_num = intval($_POST['goods_num']);//退货数量
            if (($goods_num < 0) || ($goods_num > $goods['goods_num'])) {
                $goods_num = 1;
            }
            $refund_array['reason_info'] = '';
            $reason_id = intval($_POST['reason_id']);//退货退款原因
            $refund_array['reason_id'] = $reason_id;
            $reason_array = array();
            $reason_array['reason_info'] = '其他';
            $reason_list[0] = $reason_array;
            if (!empty($reason_list[$reason_id])) {
                $reason_array = $reason_list[$reason_id];
                $refund_array['reason_info'] = $reason_array['reason_info'];
            }

            $model_trade = Model('trade');
            $order_shipped = $model_trade->getOrderState('order_shipped');//订单状态30:已发货
            if ($order['order_state'] == $order_shipped) {
                $refund_array['order_lock'] = '2';//锁定类型:1为不用锁定,2为需要锁定
            }
            $refund_array['refund_type'] = $_POST['refund_type'];//类型:1为退款,2为退货
            $refund_array['return_type'] = '2';//退货类型:1为不用退货,2为需要退货
            if ($refund_array['refund_type'] != '2') {
                $refund_array['refund_type'] = '1';
                $refund_array['return_type'] = '1';
            }
            $refund_array['seller_state'] = '1';//状态:1为待审核,2为同意,3为不同意
            $refund_array['refund_amount'] = ncPriceFormat($refund_amount);
            $refund_array['goods_num'] = $goods_num;
            $refund_array['buyer_message'] = $_POST['buyer_message'];
            $refund_array['add_time'] = time();

            $erp_refund['order_id']      = $order_id;
            $erp_refund['add_time']      = $refund_array['add_time'];
            $erp_refund['order_sn']      = $order['order_sn'];
            $erp_refund['refund_amount'] = $refund_array['refund_amount'];
            $erp_refund_sn               = $model_refund->getRefundsn($order['store_id']);
            $erp_refund['refund_sn']     = $erp_refund_sn;
            $erp_refund['refund_type']   = $refund_array['refund_type'];
            $erp_refund['reason_info']   = $refund_array['reason_info'];
            $erp_refund['buyer_message'] = $refund_array['buyer_message'];
            $erp_refund['goods_id']      = $goods['goods_id'];
            $erp_refund['goods_num']     = $refund_array['goods_num'];
            $erp_refund['order_goods_id']= $goods_id;

            try{
                $erp_result = Logic('refund')->refundOrderApply($erp_refund);
            }catch (Exception $e){
                showDialog($e->getMessage());
            }

            $refund_array['refund_order_sn'] = $erp_result['refund_order_sn'];
            $state = $model_refund->addRefundReturn($refund_array,$order,$goods);

            if ($state) {
                if ($order['order_state'] == $order_shipped) {
                    $model_refund->editOrderLock($order_id);
                }
                //更新管家婆订单状态
                //Logic('gjp_qqd')->updateOrderStatus($order['order_sn'],1,2);
                //todo 有赞
                showDialog(Language::get('nc_common_save_succ'),'reload','succ', 'CUR_DIALOG.close();');
            } else {
                showDialog(Language::get('nc_common_save_fail'),'reload','error');
            }
        }
        Tpl::showpage('order_refund_add', 'null_layout');
    }

    /**
     * type = 2 订单操作退款，默认运费退款
     * 添加实物订单运费退款
     */
    public function refund_freightOp(){
        $model_refund = Model('refund_return');
        $type = intval($_GET['type']);
        $order_id = intval($_GET['order_id']);
        if ($order_id < 1 ) {//参数验证
            showDialog(Language::get('wrong_argument'),'reload','error');
        }
        $condition = array();
        $reason_list = $model_refund->getReasonList($condition);//退款退货原因
        Tpl::output('reason_list',$reason_list);

        $condition['order_id'] = $order_id;
        if ($type ==2){
            $goods_id = 1002;
            $goods_name = '操作退款';
        }else{
            $goods_id = 1001;
            $goods_name = '运费';
        }
        $order = $model_refund->getRightOrderList($condition);

        $shipping_fee = $order['shipping_fee'];//运费金额
        $condition['goods_id'] = $goods_id;
        $condition['refund_state'] = array('elt','3');
        //运费已退款金额
        $shipping_refund_amount = $model_refund->getRefundReturnSum($condition,'refund_amount');
        if ($type ==2){
            //实现可退金额,考虑精度问题
            $allow_refund_amount = ncPriceFormat($order['order_amount'] - $order['refund_amount']);
            if ($allow_refund_amount <= 0) {
                showDialog('无可退金额', 'reload', 'error');
            }
        }else {
            //实现可退运费金额,考虑精度问题
            $allow_refund_amount = ncPriceFormat($shipping_fee - $shipping_refund_amount);
            if ($allow_refund_amount <= 0 && $type != 2) {
                showDialog('无可退运费金额', 'reload', 'error');
            }
        }
        Tpl::output('type',$type);
        Tpl::output('allow_refund_amount',$allow_refund_amount);

        $condition = array();
        $condition['buyer_id'] = $order['buyer_id'];
        $condition['order_id'] = $order['order_id'];
        $condition['order_goods_id'] = $goods_id;
        $condition['seller_state'] = array('lt','3'); // 退款状态小于3
        $refund_list = $model_refund->getRefundReturnList($condition);
        $refund = array();
        if (!empty($refund_list) && is_array($refund_list)) {
            $refund = $refund_list[0];
        }
//        $refund_state = $model_refund->getRefundState($order);//根据订单状态判断是否可以退款退货
        if ($refund['refund_id'] > 0 || $order['order_state'] <= 20) {//检查订单状态,防止页面刷新不及时造成数据错误
            showDialog(Language::get('wrong_argument'),'reload','error');
        }
        if (chksubmit()){
            $lock = Redis::lock('edit_store_refund:'.$_GET['order_id'])->setAutoRelease();
            if (!$lock->get()) {
                showDialog('处理中，请勿频繁操作...');
            }
            $refund_array = array();
            $refund_amount = floatval($_POST['refund_amount']);//退款金额
            if (($refund_amount < 0) || ($refund_amount > $allow_refund_amount)) {
                $refund_amount = $allow_refund_amount;
            }
            try {
                $refund_array['reason_info'] = '';
                $reason_id = intval($_POST['reason_id']);//退货退款原因
                $refund_array['reason_id'] = $reason_id;
                $reason_array = array();
                $reason_array['reason_info'] = '其他';
                $reason_list[0] = $reason_array;
                if (!empty($reason_list[$reason_id])) {
                    $reason_array = $reason_list[$reason_id];
                    $refund_array['reason_info'] = $reason_array['reason_info'];
                }

                $refund_array['refund_type'] = 1;//类型:1为退款,2为退货
                $refund_array['return_type'] = 1;//退货类型:1为不用退货,2为需要退货
                $refund_array['seller_state'] = 2;//状态:1为待审核,2为同意,3为不同意
                $refund_array['refund_state'] = 3;//状态:3为已完成
                $refund_array['refund_amount'] = ncPriceFormat($refund_amount);
                $refund_array['goods_num'] = 1;
                $refund_array['buyer_message'] = $_POST['buyer_message'];
                $refund_array['add_time'] = $refund_array['seller_time'] = $refund_array['admin_time'] = time();

                $model_refund->beginTransaction();
                $refund_sn = $model_refund->getRefundsn($order['store_id']);
                $refund_id = $model_refund->addRefundReturn($refund_array, $order, ['goods_id' => $goods_id, 'goods_name' => $goods_name],$refund_sn);
                //添加退款日志
                $refund['refund_id'] = $refund_id;
                $refund['order_id'] = $order_id;
                $refund['refund_amount'] = $refund_amount;
                $detail_id = $model_refund->addDetail($refund, $order);
                // 判断$refund_id和$detail_id是否为空
                if (!$refund_id || !$detail_id){
                    throw new Exception('添加退款记录失败');
                }
                //累加订单退款金额
                $order_amount = $order['refund_amount'] + $refund_amount;
                $res = Model('order')->editOrder(array('refund_amount'=>$order_amount),array('order_id'=>$order_id));
                if (!$res){
                    throw new Exception('订单退款金额累加失败');
                }
                //请求电银退款
                $refund_data['trade_no'] = $order['trade_no'];
                $refund_data['refund_amount'] = $refund_amount;
                $refund_data['order_type'] = 1;
                $refund_data['extendInfo'] = json_encode(['order_type' => 'r']);
                $refund_data['appId'] = getShopStoreId($order['store_id']);
                $refund_data['refund_sn']  = $refund_sn;
                /** @var dianyin_payLogic $dianyin_pay */
                $dianyin_pay = Logic('dianyin_pay');
                $dianyin_result = $dianyin_pay->orderRefund($refund_data);
                $data = $dianyin_result['data'];
                if ($dianyin_result['state'] && in_array($data['rspCode'], ['1', '2', '0'])) {
                    $model_refund->commit();
                    $refund_array['admin_message'] = $this->admin_info['name'] . "处理在线退款";
                    $refund_array['dy_refund_id'] = $data['refundId'];
                    $refund_array['dy_transaction_no'] = $data['transactionNo'];
                    $refund_array['dy_state'] = 2;
                    $refund_array['finnshed_time'] = $refund_array['dy_dealtime'] = time();
                    $model_refund->editRefundReturn(array('refund_id' => $refund_id), $refund_array);

                    $detail_array['pd_amount'] = 0;
                    $detail_array['refund_state'] = 2;
                    $detail_array['pay_amount'] = ncPriceFormat($data['refundAmt'] / 100);
                    $detail_array['pay_time'] = time();
                    $model_refund->editDetail(array('refund_id' => $refund_id), $detail_array);
                } else {
                    throw new Exception($dianyin_result['msg']);
                }

                showDialog(Language::get('nc_common_save_succ'), 'reload', 'succ', 'CUR_DIALOG.close();');

            } catch (Exception $e) {
                $model_refund->rollback();
                showDialog($e->getMessage(), 'reload');
            }
        }
        Tpl::showpage('order_refund_freight', 'null_layout');
    }

    /**
     * 导出
     *
     */
    public function export_step1Op(){   
    	set_time_limit(0);
        $lang   = Language::getLangContent();

        $model_order = Model('order');
        $model_dis_order = Model('dis_order');
        $condition  = array();
        if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
            $_GET['order_id'] = explode(',',trim($_GET['order_id'],','));
            $condition['orders.order_id'] = array('in',$_GET['order_id']);
        }
        $condition['orders.store_id'] = $_SESSION['store_id'];
        $this->_get_conditionGoods($condition);
        $sort_fields = array('buyer_name','store_name','order_id','payment_code','order_state','order_amount','order_from','pay_sn','rcb_amount','pd_amount','payment_time','finnshed_time','evaluation_state','refund_amount','buyer_id','store_id');
        if ($_POST['sortorder'] != '' && in_array($_POST['sortname'],$sort_fields)) {
            $order = $_POST['sortname'].' '.$_POST['sortorder'];
        } else {
            $order = 'order_id desc';
        }
        $on = 'orders.order_id = order_goods.order_id,orders.order_id = order_common.order_id,order_goods.goods_id=goods.goods_id and order_goods.store_id=goods.store_id,order_goods.dis_member_id=member.member_id,orders.chain_id = chain.chain_id';
        $field = "orders.order_id,orders.order_sn,orders.chain_id,orders.order_type,orders.order_from,orders.lock_state,orders.add_time,orders.order_amount,orders.shipping_fee,orders.order_state,orders.chain_code,orders.pay_sn,orders.payment_code,orders.payment_time,orders.rcb_amount,orders.pd_amount,orders.shipping_code,orders.refund_amount,orders.finnshed_time,orders.evaluation_state,orders.store_id,orders.store_name,orders.buyer_id,orders.buyer_name,
        order_goods.goods_id,order_goods.goods_name,order_goods.dis_member_id,order_goods.goods_num,order_goods.goods_pay_price,order_goods.goods_spec,order_goods.sku,order_common.reciver_name,order_common.reciver_info,order_common.order_message,goods.goods_serial,
        member.bill_user_name,chain.chain_name,chain.account_id,order_goods.is_dis,orders.demolition_from,orders.warehouse_type";
        if (!is_numeric($_GET['curpage'])){
            $count = $model_dis_order->getDisOrderCount($condition,$on);
           
            $array = array();
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=order&op=index');
                Tpl::showpage('export.excel');
                exit();
            }else{  //如果数量小，直接下载
                $data = $model_dis_order->getOrderListExport($condition,$field,$on,self::EXPORT_SIZE,'orders.order_id desc',self::EXPORT_SIZE,array('order_common'));
                $this->createOrderGoodsXls($data);
            }
        }else{  //下载
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $data = $model_dis_order->getOrderListExport($condition,$field,$on,'','orders.order_id desc',"{$limit1},{$limit2}",array('order_common'));
            $this->createOrderGoodsXls($data);
        }
    }

    /**
     * 商品订单-导出商品数据
     * @param array $data
     */
    private function createOrderGoodsXls($data){
        $writer = WriterFactory::create(Type::XLSX);
        //不自动换行
        $writer->setDefaultRowStyle((new StyleBuilder())->setShouldWrapText(false)->build());
        $writer->openToBrowser('order_goods-'.$_GET['curpage'].'-'.date('Y-m-d-H',time()).'.xlsx');
        $writer->addRow(['订单编号','商品名称','商品货号','订单来源','商品数量','商品实际成交价','商品SKU规格','下单时间','订单金额(元)',
            '运费金额(元)','代金券面额','订单状态','拼团状态','支付单号','支付方式','支付时间','充值卡支付(元)','预存款支付(元)','发货物流单号',
            '退款金额(元)','订单完成时间','是否评价','店铺ID','店铺名称','分销员ID','买家ID','买家账号','收货人','买家手机号','买家留言',
            '买家收货地址','是否分销','分销员','门店名称','门店财务编码','门店ID','拆单来源','指定药品仓']);
        $list = array();
        foreach ((array)$data as $k=>$order_info){
            $list[$k]['order_sn'] = $order_info['order_sn'].Logic('order')->getOrderTypeText($order_info['order_type']);
            $list[$k]['goods_name'] = htmlentities($order_info['goods_name']).","."数量:".$order_info['goods_num'];
            $list[$k]['goods_serial'] = $order_info['sku']?:$order_info['goods_serial'];
            $list[$k]['order_from'] = orderFromName($order_info['order_from']);
            $list[$k]['goods_num'] = $order_info['goods_num'];
            $list[$k]['goods_pay_price'] = $order_info['goods_pay_price'];
            $list[$k]['goods_sku_spec'] = $order_info['goods_spec'];
            $list[$k]['add_time'] = date('Y-m-d H:i:s',$order_info['add_time']);
            $list[$k]['order_amount'] = ncPriceFormat($order_info['order_amount']);
            $list[$k]['shipping_fee'] = ncPriceFormat($order_info['shipping_fee']);
            $list[$k]['voucher_price'] =  $order_info['extend_order_common']['voucher_price']?$order_info['extend_order_common']['voucher_price']:"-";
            $list[$k]['order_state'] =$order_info['order_state'].(intval($order_info['lock_state']) ? "(退款退货中)" : false);
            $_pintuan_state="-";
            if($order_info['order_type']==4){
                $model_pintuan = Model('p_pintuan');
                $_info = $model_pintuan->getOrderInfo(array('order_id'=> $order_info['order_id']));
                $_pintuan_state=$_info['lock_state']?"未成团":"已成团";
            }
            $list[$k]['pintuan_state'] =$_pintuan_state;
            $list[$k]['pay_sn'] = empty($order_info['pay_sn']) ? '' : $order_info['pay_sn'];
            $list[$k]['payment_code'] = orderPaymentName($order_info['payment_code']);
            $list[$k]['payment_time'] = !empty($order_info['payment_time']) ? (intval(date('His',$order_info['payment_time'])) ? date('Y-m-d H:i:s',$order_info['payment_time']) : date('Y-m-d',$order_info['payment_time'])) : '';
            $list[$k]['rcb_amount'] = ncPriceFormat($order_info['rcb_amount']);
            $list[$k]['pd_amount'] = ncPriceFormat($order_info['pd_amount']);
            $list[$k]['shipping_code'] = $order_info['shipping_code'];
            $list[$k]['refund_amount'] = $order_info['refund_amount']>$order_info['order_amount']?ncPriceFormat($order_info['order_amount']):ncPriceFormat($order_info['refund_amount']);
            $list[$k]['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s',$order_info['finnshed_time']) : '';
            $list[$k]['evaluation_state'] = str_replace(array(0,1,2), array('未评价','已评价','未评价'),$order_info['evaluation_state']);
            $list[$k]['store_id'] = $order_info['store_id'];
            $list[$k]['store_name'] = $order_info['store_name'];
            $list[$k]['dis_member_id'] = $order_info['dis_member_id'] > 0 ? $order_info['dis_member_id'] : '';
            $list[$k]['buyer_id'] = $order_info['buyer_id'];
            $list[$k]['buyer_name'] = $order_info['buyer_name'];
            $list[$k]['receiver_name'] = $order_info['reciver_name'];
            $list[$k]['buyer_phone'] = hideStr($order_info['reciver_info']['mob_phone']);
            $list[$k]['buyer_msg'] = $order_info['order_message'];
            $list[$k]['buyer_address'] = $order_info['reciver_info']['address'];
            $list[$k]['is_dis_text'] = $order_info['is_dis']?'是':'否';
            $list[$k]['dis_member_name'] = $order_info['bill_user_name'];
            $list[$k]['chain_name'] = $order_info['chain_name']?:$this->initShopCode[$_SESSION['store_id']]['shop_name'];
            $list[$k]['account_id'] = $order_info['account_id']?:$this->initShopCode[$_SESSION['store_id']]['shop_code'];
            $list[$k]['chain_id'] = $order_info['chain_id'];
            $list[$k]['demolition_from'] = str_replace(array(0,1,2,3), array('默认','全渠道','管易','门店'),$order_info['demolition_from']);
            $list[$k]['warehouse_type'] = $order_info['warehouse_type'] ? '巨星药品仓' : '否';
        }
        $writer->addRows($list);
        $writer->close();
        $this->log('导出实物订单商品数据');
    }
    
    /**
     * 商品订单--导出订单数据
     */
    public function export_step2Op(){
        ini_set('memory_limit','256M');
    	$lang   = Language::getLangContent();

    	$model_order = Model('order');
    	$condition  = array();
    	if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
    		$_GET['order_id'] = explode(',',trim($_GET['order_id'],','));
    		$condition['order_id'] = array('in',$_GET['order_id']);
    	}
    	$this->_get_condition($condition);
    	$sort_fields = array('buyer_name','store_name','order_id','payment_code','order_state','order_amount','order_from','pay_sn','rcb_amount','pd_amount','payment_time','finnshed_time','evaluation_state','refund_amount','buyer_id','store_id');
    	if ($_POST['sortorder'] != '' && in_array($_POST['sortname'],$sort_fields)) {
    		$order = $_POST['sortname'].' '.$_POST['sortorder'];
    	} else {
    		$order = 'order_id desc';
    	}
    	$field = 'order_id,order_sn,order_type,lock_state,order_from,add_time,order_amount,shipping_fee,order_state,pay_sn,payment_code,payment_time,shipping_code,refund_amount,finnshed_time,evaluation_state,store_id,store_name,buyer_id,buyer_name,chain_id,is_dis,demolition_from,warehouse_type,trade_no,order_father';
    	if (!is_numeric($_GET['curpage'])){
    		$count = $model_order->getOrderCount($condition);
    		$array = array();
    		if ($count > self::EXPORT_SIZE || $condition['payment_time']){   //显示下载链接
    			$page = ceil($count/self::EXPORT_SIZE);
    			for ($i=1;$i<=$page;$i++){
    				$limit1 = ($i-1)*self::EXPORT_SIZE + 1;
    				$limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
    				$array[$i] = $limit1.' ~ '.$limit2 ;
    			}
    			Tpl::output('list',$array);
    			Tpl::output('presaleLastExport',(bool)$condition['payment_time']);
    			Tpl::output('murl','index.php?act=order&op=index');
    			Tpl::showpage('export.excel');
    		}else{  //如果数量小，直接下载
    			$data = $model_order->getOrderList($condition,'',$field,$order,self::EXPORT_SIZE,array('order_goods','order_common','presale'));
    			$this->createExcel2($data);
    		}
    	}else{  //下载
    		$limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
    		$limit2 = self::EXPORT_SIZE;
    		$data = $model_order->getOrderList($condition,'',$field,$order,"{$limit1},{$limit2}",array('order_goods','order_common','presale'));
    		$this->createExcel2($data);
    	}
    }

    /**
     * 商品订单--导出订单数据
     * @param array $data
     */
    private function createExcel2($data){
        $writer = WriterFactory::create(Type::XLSX);
        //不自动换行
        $writer->setDefaultRowStyle((new StyleBuilder())->setShouldWrapText(false)->build());
        $writer->openToBrowser('order_goods-'.$_GET['curpage'].'-'.date('Y-m-d-H',time()).'.xlsx');
        $writer->addRow(['订单编号','订单来源','下单时间','订单金额(元)','支付金额(元)','运费金额(元)','代金券面额(元)','订单状态','支付单号','交易单号','支付方式','支付时间','发货物流单号','退款金额(元)','订单完成时间','是否评价','店铺ID','店铺名称','分销员ID','买家ID','买家账号','门店名称','门店财务编码','门店ID','分销订单','拆单来源','指定药品仓']);
        $list =[];
        // 门店列表
        $chain_key_list = Model('chain')->getChainKeybyId($_SESSION['store_id']);
        foreach ((array)$data as $k=>$order_info){
            $order_info['state_desc'] = orderState($order_info);
            $list[$k]['order_sn'] = $order_info['order_sn'].Logic('order')->getOrderTypeText($order_info['order_type']);
            $list[$k]['order_from'] = orderFromName($order_info['order_from']);
            $list[$k]['add_time'] = date('Y-m-d H:i:s',$order_info['add_time']);
            $list[$k]['order_amount'] = ncPriceFormat($order_info['order_amount']);
            if ($order_info['order_type'] == 11) {
                // 尾款
                if($order_info['last_price'] > 0){
                    $list[$k]['order_sn'] = $order_info['order_sn'] . "[预售尾款]";
                    // 考虑赠品订单
                    $list[$k]['pay_amount'] = $order_info['order_amount'] > 0 ? ncPriceFormat($order_info['last_price']) : 0;
                }else{
                    $list[$k]['order_sn'] = $order_info['order_sn'] . "[预售定金]";
                    // 考虑赠品订单
                    $list[$k]['pay_amount'] = ($order_info['extend_presale'] && $order_info['order_amount'] > 0) ? ncPriceFormat($order_info['extend_presale']['pre_price']) : 0;
                }
            } elseif ($order_info['order_type'] == 9) {
                $list[$k]['order_sn'] = $order_info['order_sn'] . ($order_info['order_father'] > 1 ? '[周期购子订单]' : '[周期购]');
                $list[$k]['pay_amount'] = ncPriceFormat($order_info['order_father'] > 1 ? 0 : $order_info['order_amount']);
            } else {
                $list[$k]['order_sn'] = $order_info['order_sn'] . Logic('order')->getOrderTypeText($order_info['order_type']);
                $list[$k]['pay_amount'] = $list[$k]['order_amount'];
            }

            $list[$k]['shipping_fee'] = ncPriceFormat($order_info['shipping_fee']);
            $list[$k]['voucher_price'] = $order_info['extend_order_common']['voucher_price']?$order_info['extend_order_common']['voucher_price']:"-";
            $list[$k]['order_state'] =orderState($order_info).(intval($order_info['lock_state']) ? "(退款退货中)" : false);//$order_info['state_desc']
            $list[$k]['pay_sn'] = empty($order_info['pay_sn']) ? '' : $order_info['pay_sn'];
            $list[$k]['trade_no'] = $order_info['trade_no'] ?: '';
            $list[$k]['payment_code'] = orderPaymentName($order_info['payment_code']);
            if(empty($order_info['payment_time'])){
                $list[$k]['payment_time'] = '';
            }else{
                $list[$k]['payment_time'] = preg_match('/^\d+$/',$order_info['payment_time']) ? date('Y-m-d H:i:s',$order_info['payment_time']) : $order_info['payment_time'];
            }
            $dis_member_ids = [];
            if(isset($order_info['extend_order_goods']) && !empty($order_info['extend_order_goods'])) {
                foreach($order_info['extend_order_goods'] as $v) {
                    if ($v['dis_member_id']) {
                        $dis_member_ids[$v['order_id']] = $v['dis_member_id'];
                    }
                }
            }
            $list[$k]['shipping_code'] = $order_info['shipping_code'];
            $list[$k]['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
            $list[$k]['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s',$order_info['finnshed_time']) : '';
            $list[$k]['evaluation_state'] = str_replace(array(0,1,2), array('未评价','已评价','未评价'),$order_info['evaluation_state']);
            $list[$k]['store_id'] = $order_info['store_id'];
            $list[$k]['store_name'] = $order_info['store_name'];
            $list[$k]['dis_member_id'] = $dis_member_ids[$k];
            $list[$k]['buyer_id'] = $order_info['buyer_id'];
            $list[$k]['buyer_name'] = $order_info['buyer_name'];
            $list[$k]['chain_name'] = $order_info['chain_id'] ? $chain_key_list[$order_info['chain_id']]['chain_name']:$this->initShopCode[$_SESSION['store_id']]['shop_name'];
            $list[$k]['account_id'] = $order_info['chain_id'] ? $chain_key_list[$order_info['chain_id']]['account_id']:$this->initShopCode[$_SESSION['store_id']]['shop_code'];
            $list[$k]['chain_id'] = $order_info['chain_id'];
            $list[$k]['is_dis'] = $order_info['is_dis']==1?'是':'否';
            $list[$k]['demolition_from'] = str_replace(array(0,1,2,3), array('默认','全渠道','管易','门店'),$order_info['demolition_from']);
            $list[$k]['warehouse_type'] = $order_info['warehouse_type'] ? '巨星药品仓' : '否';
        }
        $writer->addRows($list);
        $writer->close();
        $this->log('导出实物订单数据');
    }

    /**
     * 处理搜索条件
     */
    private function _get_condition(&$condition) {
        $condition['order_demolition'] = 0;
        $_REQUEST['query'] = trim($_REQUEST['query']);
        $_GET['keyword'] = trim($_GET['keyword']);

        if ($_REQUEST['query'] != '' && in_array($_REQUEST['qtype'],array('order_sn','store_id','store_name','buyer_name','pay_sn','trade_no','goods_name'))) {
            if($_REQUEST['qtype'] == 'goods_name'){
                $condition['order_id'] = ['in raw',"select og.order_id from upet_goods g join upet_order_goods og force index(goods_id) 
                   on og.goods_id = g.goods_id where g.goods_name like '%{$_REQUEST['query']}%'"];
            }else{
                $condition[$_REQUEST['qtype']] = $_REQUEST['query'];
            }
        }
        if ($_GET['keyword'] != '' && in_array($_GET['keyword_type'],array('order_sn','store_name','buyer_name','pay_sn','shipping_code','trade_no','goods_name'))) {
            if($_GET['keyword_type'] == 'goods_name'){
                $condition['order_id'] = ['in raw',"select og.order_id from upet_goods g join upet_order_goods og force index(goods_id) 
                   on og.goods_id = g.goods_id where g.goods_name like '%{$_GET['keyword']}%'"];
            }else{
                $condition[$_GET['keyword_type']] = $_GET['keyword'];
            }
        }
        if (!in_array($_GET['qtype_time'],array('add_time','payment_time','finnshed_time'))) {
            $_GET['qtype_time'] = null;
        }

        $if_start_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_start_date']);
        $if_end_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_end_date']);
        $start_unixtime = $if_start_time ? strtotime($_GET['query_start_date']) : null;
        $end_unixtime = $if_end_time ? strtotime($_GET['query_end_date']): null;
        if ($_GET['qtype_time'] && ($start_unixtime || $end_unixtime)) {
            $condition[$_GET['qtype_time']] = array('time',array($start_unixtime,$end_unixtime));
        }
        if($_GET['payment_code']) {
            if ($_GET['payment_code'] == 'wxpay') {
                $condition['payment_code'] = array('in',array('wxpay','wx_saoma','wx_jsapi'));
            } elseif($_GET['payment_code'] == 'alipay') {
                $condition['payment_code'] = array('in',array('alipay','ali_native'));
            } else {
                $condition['payment_code'] = $_GET['payment_code'];
            }
        }
        if(in_array($_GET['order_state'],array('0','10','20','30','40','50'))){
            $condition['order_state'] = $_GET['order_state'];
        }
        //金额筛选
        if (in_array($_GET['query_amount'], array('order_amount', 'shipping_fee', 'refund_amount'))) {
            if (floatval($_GET['query_start_amount']) != '' && floatval($_GET['query_end_amount']) != '') {
                $condition[$_GET['query_amount']] = array('between', floatval($_GET['query_start_amount']) . ',' . floatval($_GET['query_end_amount']));
            }else if (floatval($_GET['query_start_amount']) != '' ){
                $condition[$_GET['query_amount']] = ['egt',$_GET['query_start_amount']];
            }else if (floatval($_GET['query_end_amount']) != '' ){
                $condition[$_GET['query_amount']] = ['elt',$_GET['query_end_amount']];
            }
        }


        if ($_GET['order_from'] > 0) {
            if ($_GET['order_from'] == 99) { // 视频号
                $condition['is_live'] = 2;
                $condition['payment_code'] = 'wx_jsapi';
            } else {
                $condition['order_from'] = $_GET['order_from'];
            }
        }
        if(isset($_GET['warehouse_type']) && $_GET['warehouse_type'] != ''){
            $condition['warehouse_type'] = $_GET['warehouse_type'];
        }

        if(isset($_GET['trade_no']) && $_GET['trade_no'] != ''){
            $condition['trade_no'] = $_GET['trade_no'];
        }
        //增加订单类型
        if(isset($_GET['order_type']) && $_GET['order_type'] != ''){
            if($_GET['order_type'] > 0) {
                $condition['order_type'] = $_GET['order_type'];
            } else if($_GET['order_type'] == -1) {
                $condition['is_dis'] = 1;
            } else if($_GET['order_type'] == -2) {
                $condition['order_type'] = ['not in', [13]];
            } else if($_GET['order_type'] == -3) {
                $condition['hospital_recommend_id'] = array(array('gt',0),array('lt',100000),'and');
            }
        }
        $condition['store_id'] = $_SESSION['store_id'];
    }

    /**
     * 处理搜索条件
     */
    private function _get_conditionGoods(& $condition) {
        $condition['orders.order_demolition'] = 0;


        $_REQUEST['query'] = trim($_REQUEST['query']);
        $_GET['keyword'] = trim($_GET['keyword']);

        if ($_REQUEST['query'] != '' && in_array($_REQUEST['qtype'],array('order_sn','store_name','buyer_name','pay_sn','trade_no','goods_name'))) {
            if($_REQUEST['qtype'] == 'goods_name'){
                $condition['orders.order_id'] = ['in raw',"select og.order_id from upet_goods g join upet_order_goods og force index(goods_id) 
                   on og.goods_id = g.goods_id where g.goods_name like '%{$_REQUEST['query']}%'"];
            }else{
                $condition['orders.'.$_REQUEST['qtype']] = $_REQUEST['query'];
            }
        }
        if ($_GET['keyword'] != '' && in_array($_GET['keyword_type'],array('order_sn','store_name','buyer_name','pay_sn','shipping_code','trade_no','goods_name'))) {
            if($_GET['keyword_type'] == 'goods_name'){
                $condition['orders.order_id'] = ['in raw',"select og.order_id from upet_goods g join upet_order_goods og force index(goods_id) 
                   on og.goods_id = g.goods_id where g.goods_name like '%{$_GET['keyword']}%'"];
            }else{
                $condition['orders.'.$_GET['keyword_type']] = $_GET['keyword'];
            }
        }
        if (!in_array($_GET['qtype_time'],array('add_time','payment_time','finnshed_time'))) {
            $_GET['qtype_time'] = null;
        }
        $if_start_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_start_date']);
        $if_end_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_end_date']);
        $start_unixtime = $if_start_time ? strtotime($_GET['query_start_date']) : null;
        $end_unixtime = $if_end_time ? strtotime($_GET['query_end_date']): null;
        if ($_GET['qtype_time'] && ($start_unixtime || $end_unixtime)) {
            $condition['orders.'.$_GET['qtype_time']] = array('time',array($start_unixtime,$end_unixtime));
        }
        if($_GET['payment_code']) {
            if ($_GET['payment_code'] == 'wxpay') {
                $condition['orders.payment_code'] = array('in',array('wxpay','wx_saoma','wx_jsapi'));
            } elseif($_GET['orders.payment_code'] == 'alipay') {
                $condition['orders.payment_code'] = array('in',array('alipay','ali_native'));
            } else {
                $condition['orders.payment_code'] = $_GET['payment_code'];
            }
        }
        if(in_array($_GET['order_state'],array('0','10','20','30','40','50'))){
            $condition['orders.order_state'] = $_GET['order_state'];
        }

        if (in_array($_GET['query_amount'], array('order_amount', 'shipping_fee', 'refund_amount'))) {
            if (floatval($_GET['query_start_amount']) != '' && floatval($_GET['query_end_amount']) != '') {
                $condition['orders.' . $_GET['query_amount']] = array('between', floatval($_GET['query_start_amount']) . ',' . floatval($_GET['query_end_amount']));
            }else if (floatval($_GET['query_start_amount']) != '' ){
                $condition[$_GET['orders.' .'query_amount']] = ['egt',$_GET['query_start_amount']];
            }else if (floatval($_GET['query_end_amount']) != '' ){
                $condition[$_GET['orders.' .'query_amount']] = ['elt',$_GET['query_end_amount']];
            }
        }
        // 销售渠道
        if ($_GET['order_from'] > 0) {
            if ($_GET['order_from'] == 99) { // 视频号
                $condition['orders.is_live'] = 2;
                $condition['orders.payment_code'] = 'wx_jsapi';
            } else {
                $condition['orders.order_from'] = $_GET['order_from'];
            }
        }

        //增加订单类型
        if(isset($_GET['order_type']) && $_GET['order_type'] != ''){
            if($_GET['order_type'] > 0) {
                $condition['orders.order_type'] = $_GET['order_type'];
            } else if($_GET['order_type'] == -1) {
                $condition['orders.is_dis'] = 1;
            } else if($_GET['order_type'] == -2) {
                $condition['orders.order_type'] = ['not in', [13]];
            }
        }
    }

    /**
     * 预售订单相关信息（平台订单）
     * 与rp-field\core\upet\Models\OrderPresale.php中formatPreInfo判断逻辑保持一致
     * @param $order_info
     * @return array
     */
    function _getPreOrderInfo($order_info)
    {
        // 默认值
        $data = array(
            'pay_time_dj' => '', 'is_pay_dj' => 0,
            'pay_time_wk' => '', 'is_pay_wk' => 0,
            'start_time_dj' => '', 'end_time_dj' => '',
            'start_time_wk' => '', 'end_time_wk' => '',
            'pre_price' => '', 'last_price' => '',
            'refund_status_dj' => '', 'refund_status_wk' => '',
        );

        // 查询预售订单相关表 `upet_order_presale`
        $model = new OrderPresale();
        $pre_info = $model->where('erp_order_sn', $order_info['order_sn'])->find();
        if (isset($pre_info->id) && $pre_info = $pre_info->toArray()) {
            // 定金支付时间，取订单主表
            if ($pre_info['pre_status'] > 0) {
                $data['is_pay_dj'] = 1;
                $data['pay_time_dj'] = $order_info['payment_time'] > 0 ? date('Y-m-d H:i:s', $order_info['payment_time']) : '';
            }
            $data['pre_price'] = $pre_info['pre_price'];
            $data['last_price'] = $pre_info['last_price'];

            // 尾款状态和时间，退款信息只在付了尾款后才会有
            if ($pre_info['pre_status'] == 2) {
                $data['is_pay_wk'] = 1;
                $data['pay_time_wk'] = $pre_info['last_pay_time'];
                $refunds = (new RefundPreOrder())->getPreAllRefundStatusInfo($order_info, $pre_info);
                $data['refund_status_dj'] = isset($refunds[1]) ? "($refunds[1])" : "";
                $data['refund_status_wk'] = isset($refunds[2]) ? "($refunds[2])" : "";
            }

            // 付定金和付尾款时间段
            $data['start_time_dj'] = date('Y-m-d H:i', strtotime($pre_info['pre_start_time']));
            $data['end_time_dj'] = date('Y-m-d H:i', strtotime($pre_info['pre_end_time']));
            $data['start_time_wk'] = date('Y-m-d H:i', strtotime($pre_info['last_start_time']));
            $data['end_time_wk'] = date('Y-m-d H:i', strtotime($pre_info['last_end_time']));
        }

        return $data;
    }

    /**
     * 导出预售尾款支付订单
     */
    public function export_presale_lastOp()
    {
        ini_set('memory_limit','512M');
        $lang = Language::getLangContent();

        $condition = array();
        if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
            $_GET['order_id'] = explode(',', trim($_GET['order_id'], ','));
            $condition['order_id'] = array('in', $_GET['order_id']);
        }
        $this->_get_condition($condition);

        $maps = [
            'pay_sn' => 'last_pay_sn',
            'payment_code' => 'last_payment_code',
            'payment_time' => 'last_pay_time',
            'trade_no' => 'last_trade_no',
        ];

        $replaces =[];
        foreach ($condition as $key => $value){
            if(array_key_exists($key,$maps)){
                if($key == 'payment_time'){
                    $value = ['between',[date('Y-m-d H:i:s', $value[1][0]),date('Y-m-d H:i:s', $value[1][1] + 24*3600 -1)]];
                }
                $replaces[$maps[$key]] = $value;
            }else{
                $replaces[$key] = $value;
            }
        }

        $field = 'order_id,order_sn,order_type,lock_state,order_from,add_time,order_amount,shipping_fee,order_state,shipping_code,refund_amount,finnshed_time,evaluation_state,store_id,store_name,buyer_id,buyer_name,chain_id,is_dis,demolition_from,warehouse_type,order_father,
        last_pay_sn as pay_sn,last_payment_code as payment_code,last_pay_time as payment_time,last_trade_no as trade_no,last_price';

        $list = Model()->table('order_presale,orders')->join('inner')
            ->on('order_presale.erp_order_sn = orders.order_sn')
            ->field($field)
            ->where($replaces)
            ->limit(10000)
            ->select();

        $data = Model('order')->extendOrderList($list, ['order_common','chain']);

        $this->createExcel2($data);
    }

    /**
     * Notes:显示手机号查询记录
     * @param order_id 订单号id
     * @type 1:实物接收手机号 2：虚拟订单接收手机号 3：实物用户手机号
     * User: rocky
     * DateTime: 2022/9/19 16:29
     */
    public function showphoneOp()
    {
        $order_id = trim($_POST['order_id']);
        $type = $_POST['type'];
        $res = showphone($type, $order_id,$this->admin_info['name']);
        if ($res['status'] == 200 && $_POST['text'] == '显示'){
            $data['content']    = '查看手机号，order_id:'.$order_id.'，type:'.$type;
            $data['admin_name'] = $this->admin_info['name'];
            $data['createtime'] = TIMESTAMP;
            $data['admin_id']   = $this->admin_info['id'];;
            $data['ip']         = getIp();
            $data['url']        = $_REQUEST['act'].'&'.$_REQUEST['op'];
            \Upet\Models\AdminLog::create($data);
        }
        echo json_encode($res);
        exit;
    }
}
