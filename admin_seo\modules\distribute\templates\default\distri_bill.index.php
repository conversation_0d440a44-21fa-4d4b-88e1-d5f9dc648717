<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>结算管理</h3>
                <h5>实物商品分销结算账单表</h5>
            </div>
        </div>
    </div>
    <!-- 操作说明 -->
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
            <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
            <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
        <ul>
            <li>查看分销员分销佣金结算情况</li>
        </ul>
    </div>
    <div class="sumdata">
        <h3><i class="fa fa-bar-chart"></i>佣金总金额：<span id="dis_pay_sellnum" style="color: red;"><?php echo $_SESSION['dis_pay_sellnum_'.$_SESSION['store_id']]?></span>
            个人业绩总金额：<span id="dis_outside_amount" style="color: red;">0.00</span></h3>
    </div>
    <div id="flexigrid"></div>
    <div class="ncap-search-ban-s" id="searchBarOpen"><i class="fa fa-search-plus"></i>高级搜索</div>
    <div class="ncap-search-bar">
        <div class="handle-btn" id="searchBarClose"><i class="fa fa-search-minus"></i>收起边栏</div>
        <div class="title">
            <h3>高级搜索</h3>
        </div>
        <form method="get" name="formSearch" id="formSearch">
            <div id="searchCon" class="content">
                <div class="layout-box">
                    <dl>
                        <dt>订单编号</dt>
                        <dd>
                            <input type="text" value="" name="order_sn" id="order_sn" class="s-input-txt">
                        </dd>
                    </dl>
                    <dl>
                        <dt>商品名称</dt>
                        <dd>
                            <label><input type="text" value="" name=goods_name id="goods_name" class="s-input-txt"></label>
                            <label><input type="checkbox" value="1" name="jq_query">精确</label>
                        </dd>
                    </dl>
                    <dl>
                        <dt>结算状态</dt>
                        <dd>
                            <select class="s-select" name="log_state">
                                <option value="">-请选择-</option>
                                <option value="0">未结算</option>
                                <option value="1">已经结算</option>
                            </select>
                        </dd>
                    </dl>
                    <dl>
                        <dt>时期筛选</dt>
                        <dd>
                            <label>
                                <input type="text" name="stime" data-dp="1" class="s-input-txt" placeholder="请选择开始时间" />
                            </label>
                            <label>
                                <input type="text" name="etime" data-dp="1" class="s-input-txt" placeholder="请选择结束时间"  />
                            </label>
                        </dd>
                    </dl>
                    <dl>
                        <dt>分销员身份</dt>
                        <dd>
                            <label>
                                <select name="dis_tri_state" class="s-select">
                                    <option value=""><?php echo $lang['nc_please_choose'];?></option>
                                    <option value="1">内部分销员</option>
                                    <option value="0">外部代理人</option>

                                </select>
                            </label>
                        </dd>
                    </dl>
                    <dl>
                        <dt>会员ID</dt>
                        <dd>
                            <label>
                                <input type="text" value="" name="dis_member_id" id="dis_member_id" class="s-input-txt" placeholder="输入会员ID">
                            </label>
                        </dd>
                    </dl>
                </div>
            </div>
            <div class="bottom">
                <a href="javascript:void(0);" id="ncsubmit" class="ncap-btn ncap-btn-green">提交查询</a>
                <a href="javascript:void(0);" id="ncreset" class="ncap-btn ncap-btn-orange" title="撤销查询结果，还原列表项所有内容"><i class="fa fa-retweet"></i><?php echo $lang['nc_cancel_search'];?></a>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript">
    $(function(){
        $("input[data-dp='1']").datepicker();
        // 高级搜索提交
        $('#ncsubmit').click(function(){
            $("#flexigrid").flexOptions({url: 'index.php?act=distri_bill&op=get_bill_xml&'+$("#formSearch").serialize(),query:'',qtype:''}).flexReload();
            $.ajax({
                type: "get",
                dataType: "json",
                url: "index.php?act=distri_bill&op=get_sellnum",
                data: {},
                success: function(data){
                    $('#dis_pay_sellnum').html(data.dis_pay_sellnum);
                }
            });
        });

        // 高级搜索重置
        $('#ncreset').click(function(){
            $("#flexigrid").flexOptions({url: 'index.php?act=distri_bill&op=get_bill_xml'}).flexReload();
            $("#formSearch")[0].reset();
        });
        $("#flexigrid").flexigrid({
            url: 'index.php?act=distri_bill&op=get_bill_xml',
            colModel : [
                {display: '操作', name : 'operation', width : 60, sortable : false, align: 'center', className: 'handle-s'},
                {display: '结算编号', name : 'log_id', width : 60, sortable : true, align: 'center'},
                {display: '订单编号', name : 'order_sn', width : 120, sortable : true, align: 'center'},
                {display: '商品名称', name : 'goods_name', width : 100, sortable : true, align: 'center'},
                {display: '添加时间', name : 'add_time', width : 100, sortable : true, align: 'center'},
                {display: '支付金额', name : 'pay_goods_amount', width: 50, sortable : true, align : 'center'},
                {display: '退款金额', name : 'refund_amount', width : 60, sortable : true, align: 'center'},
                {display: '分销佣金比例', name : 'dis_commis_rate', width: 80, sortable : true, align : 'center'},
                {display: '分销佣金', name : 'dis_pay_amount', width: 70, sortable : true, align : 'center'},
                {display: '个人业绩', name : 'dis_order_amount', width: 70, sortable : true, align : 'center'},
                {display: '结算时间', name : 'dis_pay_time', width : 80, sortable : true, align: 'center'},
                {display: '结算状态', name : 'log_state', width : 70, sortable : true, align: 'center'},
                {display: '商家ID', name : 'store_id', width : 90, sortable : true, align: 'center'},
                {display: '分销员ID', name : 'dis_member_id', width : 90, sortable : true, align: 'center'},
                {display: '备注', name : 'remark', width : 150, sortable : true, align: 'center'}
            ],
            <?php if(C('is_excelport')){?>
            buttons : [
                {display: '<i class="fa fa-file-excel-o"></i>导出虚拟结算数据', name : 'csv', bclass : 'csv', title : '将选定行数据导出csv文件,如果不选中行，将导出列表所有数据', onpress : fg_operate},
                {display: '<i class="fa fa-file-excel-o"></i>导出实物结算数据', name : 'csv', bclass : 'csv', title : '将选定行数据导出csv文件,如果不选中行，将导出列表所有数据', onpress : fg_operate_r}
            ],
            <?php }?>
            searchitems : [
                {display: '订单编号', name : 'order_sn'},
                {display: '商品名称', name : 'goods_name'}
            ],
            sortname: "log_id",
            sortorder: "desc",
            title: '分销佣金结算列表',
            onSuccess:function(){
                $.ajax({
                    type: "get",
                    dataType: "json",
                    url: "index.php?act=distri_bill&op=get_sellnum",
                    data: {},
                    success: function(data){
                        $('#dis_pay_sellnum').html(data.dis_pay_sellnum);
                    }
                });
            }
        });
    });
    function fg_operate(name, grid) {
        if (name == 'csv') {
            var itemlist = new Array();
            if($('.trSelected',grid).length>0){
                $('.trSelected',grid).each(function(){
                    itemlist.push($(this).attr('data-id'));
                });
            }
            fg_csv(itemlist);
        }
    }
    function fg_csv(ids) {
        id = ids.join(',');
        window.location.href = $("#flexigrid").flexSimpleSearchQueryString()+'&op=export_bill&ob_id='+id;
    }

    function fg_operate_r(name, grid) {
        if (name == 'csv') {
            var itemlist = new Array();
            if($('.trSelected',grid).length>0){
                $('.trSelected',grid).each(function(){
                    itemlist.push($(this).attr('data-id'));
                });
            }
            fg_csv_r(itemlist);
        }
    }
    function fg_csv_r(ids) {
        id = ids.join(',');
        window.location.href = $("#flexigrid").flexSimpleSearchQueryString()+'&op=export_bill_r&ob_id='+id;
    }
</script> 
