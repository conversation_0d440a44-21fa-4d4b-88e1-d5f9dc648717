<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title"><a class="back" href="index.php?act=goods_app&op=index" title="返回宠医云商品列表"><i class="fa fa-arrow-circle-o-left"></i></a>
      <div class="subject">
        <h3>分销员设置</h3>
        <h5>分销员绑定关系规则</h5>
      </div>
        <?php echo $output['top_link'];?>
    </div>
  </div>
    <!-- 操作说明 -->
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
            <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
            <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
        <ul>
            <?php echo $output['top_tips'];?>
        </ul>
    </div>
  <form id="member_form" method="post" action='index.php?act=distri_member&op=manage_save'>
    <input type="hidden" name="form_submit" value="ok" />
    <div class="ncap-form-default" id="explanation">


      <dl class="row">
        <dt class="tit">
          <label><em>*</em>粉丝关系保效期</label>
        </dt>
        <dd class="opt">
          <ul class="ncsc-form-radio-list">            
            <li><label><input name="distri_user" value="0" <?php if($output['setting_info']['value'] == 0){echo 'checked';}?> type="radio">无保护期</label></li>
            <li>
              <label><input name="distri_user" value="1" <?php if($output['setting_info']['value'] > 0){echo 'checked';}?> type="radio">有效期</label>
              <span class="property">
                <label class="mr5"></label>
                <input class="text w60" type="text" name="distri_user_time" onchange="if(parseInt(this.value)<1||parseInt(this.value)>365){this.value='';alert('请输入1-365之间的数字。')}" value="<?php if($output['setting_info']['value'] > 0){echo $output['setting_info']['value'];}?>">
                <em class="add-on">天结束后解绑</em><p class="notic">填写1-365的整数</p>
              </span>
            </li>
          </ul>
        </dd>
      </dl>
      <div class="bot"><a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="submitBtn" >确认提交</a></div>
    </div>
  </form>
</div>
<script src="<?php echo RESOURCE_SITE_URL;?>/js/common_select.js" charset="utf-8"></script> 
<script src="<?php echo RESOURCE_SITE_URL;?>/js/jquery.ajaxContent.pack.js" type="text/javascript"></script> 
<script>
    $(document).ready(function(){

        $("#submitBtn").click(function(){
            $("form").submit();
        });
    });
</script> 
