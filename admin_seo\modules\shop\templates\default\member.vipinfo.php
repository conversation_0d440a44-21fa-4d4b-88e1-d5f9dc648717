<?php defined('InShopNC') or exit('Access Invalid!');?>



<style type="text/css">
.flexigrid.memberinfoonly{background:#fafafa; padding: 0 20px; margin: 0;}
.flexigrid.memberinfoonly .explanation{width:150px; float: left; margin: 18px 12px 18px 0; background: #FFF; border: 1px solid #e5e5e5; padding: 10px 12px 8px 34px; overflow: hidden; position: relative;}
.flexigrid.memberinfoonly .explanation .title{margin-bottom: 0;position: unset;}
.flexigrid.memberinfoonly .explanation h4{color:#222; font-size:14px; font-weight: 700;}
.flexigrid.memberinfoonly .explanation ul{margin-left:0;}
.flexigrid.memberinfoonly .explanation ul li{background:unset;padding-left:0;}
.flexigrid.memberinfoonly .explanation .title i.fa{position: absolute; top: -1px; left: 10px; font-size: 20px; pointer-events: none;  color: #f8411c;}
</style>
<div class="flexigrid memberinfoonly" style="">
	<?php if($member_info['member_isbzk'] ==1):?>
	<div class="explanation" title="<?php echo $member_info['member_bzkstime']?date('Y-m-d H:i:s',trim($member_info['member_bzkstime'])):'-'; ?>至<?php echo $member_info['member_bzketime']?date('Y-m-d H:i:s',trim($member_info['member_bzketime'])):'-'; ?>" style=" ">
	    <div class="title">
	    <i class="fa fa-bookmark"></i>
	    <h4>爱宠保障卡</h4>
	    <ul><li><?php echo $member_info['member_bzketime']?date('Y-m-d',trim($member_info['member_bzketime'])):'-'; ?>到期</li></ul>
	    </div>
	</div>
	<?php endif;?>
    <?php if($member_info['member_isvip'] ==1):?>
	<div class="explanation" title="<?php echo $member_info['member_bzkstime']?date('Y-m-d H:i:s',trim($member_info['member_bzkstime'])):'-'; ?>至<?php echo $member_info['member_bzketime']?date('Y-m-d H:i:s',trim($member_info['member_bzketime'])):'-'; ?>">
	    <div class="title">
	    <i class="fa fa-bookmark"></i>
	    <h4>198会员卡</h4>
	    <ul><li><?php echo $member_info['member_vipetime']?date('Y-m-d',trim($member_info['member_vipetime'])):'-'; ?>到期</li></ul>
	    </div>
	</div>
	<?php endif;?>
    <?php if($output['vipinfo_list']):?>
	<div class="hDiv" style="border-color: #eee transparent;">
		<div class="hDivBox">
			<table width="100%" cellpadding="0" cellspacing="0">
				<thead>
				<tr>
				<th><div style="text-align: center; ">描述说明</div></th>
				<th width="100"><div style="text-align: center;">状态</div></th>
				<th width="180"><div style="text-align: center; ">时间</div></th>
				</tr>
				</thead>
			</table>
		</div>
	</div>
	<div class="bDiv" style="min-height: unset;max-height: 200px; margin-bottom: 20px;">
		<div>
			<table cellpadding="0" cellspacing="0" border="0">
				<tbody>
				<?php foreach ($output['vipinfo_list'] as $v):?>
				<tr class="erow">
				<td align="left"><div style="text-align: center;"><?php echo trim($v['mv_msg']); ?></div></td>
				<td align="center" width="100">
					<div style="text-align: center;">
				      <?php if($v['mv_state'] ==1):?>正常<?php elseif($v['mv_state'] ==2):?>退款中
					  <?php elseif($v['mv_state'] ==3):?>已退款
			          <?php elseif($v['mv_state'] ==4):?>已过期<?php else:?>开卡中
					  <?php endif;?>
					</div>
				</td>
				<td align="center" width="180"><div style="text-align: center;"><?php echo $v['mv_time']?date('Y-m-d H:i:s',trim($v['mv_time'])):'-'; ?></div></td>
				</tr>
				<?php endforeach;?>
				</tbody>
			</table>
		</div>
	</div>
</div>
<?php else:?>
<div class="ncap-form-default"><dl class="row"><dd class="opt">&nbsp;&nbsp;暂无信息</dd></dl>
</div>
<?php endif;?>
