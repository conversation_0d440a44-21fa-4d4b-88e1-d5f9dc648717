<?php
/**
 * 商品标签管理
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');
class goods_tagControl extends SystemControl{

    public function __construct(){
        parent::__construct();
        Language::read('goods_tag');
    }

    public function indexOp() {
        $this->tags_listOp();
    }

    /**
     * 分类管理
     */
    public function tags_listOp(){
        Tpl::output('show_type', $this->show_type);
        $model_tag = Model('tags_goods');
        //列表
        $map = array();
        $tag_list = $model_tag->getTagsList($map);

        Tpl::output('tag_list',$tag_list);
        Tpl::showpage('goods_tag.index');
    }

    /**
     * 输出XML数据
     */
    public function get_xmlOp() {
        $model_tag = Model('tags_goods');
        // 设置页码参数名称
        $condition = array();
        if ($_POST['query'] != '') {
            if ($_POST['qtype'] == "tag_id") {
                $condition[$_POST['qtype']] = array('eq', intval($_POST['query']));
            }else{
                $condition[$_POST['qtype']] = array('like', '%' . $_POST['query'] . '%');
            }
        }
        $order = '';
        $param = array('tag_id', 'tag_name', 'tag_sort', 'gc_name');
        if (in_array($_POST['sortname'], $param) && in_array($_POST['sortorder'], array('asc', 'desc'))) {
            $order = $_POST['sortname'] . ' ' . $_POST['sortorder'];
        }

        //店铺列表
        $tag_list = $model_tag->getTagsList($condition, '*',$_POST['rp'],  $order);

        $data = array();
        $data['now_page'] = $model_tag->shownowpage();
        $data['total_num'] = $model_tag->gettotalnum();
        foreach ((array)$tag_list as $value) {
            $param = array();
            if ($value['tag_id'] == 225) {
                $operation = "<a class='btn blue' href='index.php?act=goods_tag&op=goods_tag_edit&tag_id=".$value['tag_id']."'><i class='fa fa-pencil-square-o'></i>编辑</a>";
            } else {
                $operation = "<a class='btn red' href='javascript:void(0);' onclick='fg_del(". $value['tag_id'] .")'><i class='fa fa-trash-o'></i>删除</a><a class='btn blue' href='index.php?act=goods_tag&op=goods_tag_edit&tag_id=".$value['tag_id']."'><i class='fa fa-pencil-square-o'></i>编辑</a>";
            }
            $param['operation'] = $operation;
            $param['tag_id'] = $value['tag_id'];
            $param['tag_sort'] = $value['tag_sort'];
            $param['tag_name'] = $value['tag_name'];
            $param['tag_type_id'] = $value['tag_type_id'] ==  '1' ? '活动标签' : '默认';
            $gc_name = "";
            $gc_list = $model_tag->getTagGcList(array('tag_id'=>$value['tag_id']),"gc_name,gc_id");
            if (is_array($gc_list) && !empty($gc_list)) {
                foreach ($gc_list as $kk => $vv) {
                    $gc_name .= $vv['gc_name'].'<br/>,';
                }
            }
            $gc_name = rtrim($gc_name,",");
            $param['gc_name'] = $gc_name;
            $param['tag_state'] = $value['tag_state'] ==  '1' ? '<span class="yes"><i class="fa fa-check-circle"></i>是</span>' : '<span class="no"><i class="fa fa-ban"></i>否</span>';
            $data['list'][$value['tag_id']] = $param;
        }
        echo Tpl::flexigridXML($data);exit();
    }

    /**
     * 商品分类添加
     */
    public function goods_tag_addOp(){
        $lang   = Language::getLangContent();
        $model_class = Model('goods_class');
        if (chksubmit()){
            $obj_validate = new Validate();
            $obj_validate->validateparam = array(
                array("input"=>$_POST["tag_name"], "require"=>"true", "message"=>$lang['goods_tag_add_name_null']),
                array("input"=>$_POST["tag_sort"], "require"=>"true", 'validator'=>'Number', "message"=>$lang['goods_tag_add_sort_int']),
            );
            $error = $obj_validate->validate();
            if ($error != ''){
                showMessage($error);
            }else {
                if(isset($_POST['cate_id']) && is_array($_POST['cate_id']) && count($_POST['cate_id'])>0){
                    $cate_id = intval($_POST['cate_id'][0]);
                }
                $insert_array = array();
                $insert_array['tag_name']        = trim($_POST['tag_name']);
                $insert_array['tag_sort']        = intval($_POST['tag_sort']);
                $insert_array['tag_state']        = intval($_POST['tag_state']);
                $insert_array['tag_type_id']        = intval($_POST['tag_type_id']);
                //$insert_array['gc_id']              = $cate_id;
                /*$taggc_data = [];
                if (is_array($_POST['cate_ids']) && !empty($_POST['cate_ids'])) {
                    foreach ($_POST['cate_ids'] as $key => $val){
                        echo $key.'  =='.$val.'<br/>';
                        $tagsInfo = explode(",",$val);
                        $taggc_data[$key]['gc_id'] = $tagsInfo[0];
                        $taggc_data[$key]['gc_name'] = $tagsInfo[1];
                    }
                } else {
                    $taggc_data[0]['gc_id'] = 0;
                    $taggc_data[0]['gc_name'] = "";
                }
                foreach ($_POST['cate_ids'] as $key => $val){
                    echo $key.'  =='.$val.'<br/>';
                }
                ppp($taggc_data);
                ppp($_POST['cate_ids'],2);*/
                /*if ($cate_id > 0) {
                    $goods_class = $model_class->getGoodsClassLineForTag($cate_id);
                    $insert_array['gc_id_1']            = intval($goods_class['gc_id_1']);
                    $insert_array['gc_id_2']            = intval($goods_class['gc_id_2']);
                    $insert_array['gc_id_3']            = intval($goods_class['gc_id_3']);
                    $insert_array['gc_name']            = $goods_class['gc_tag_name'];
                }else{
                    $insert_array['gc_id_1']            = 0;
                    $insert_array['gc_id_2']            = 0;
                    $insert_array['gc_id_3']            = 0;
                }*/
                $result = Model('tags_goods')->addGoodsTag($insert_array);
                if ($result){
                    $taggc_data = [];
                    if($cate_id>0 && !isset($_POST['cate_ids'])){
                        $_POST['cate_ids'] = array($cate_id);
                    }else if($cate_id>0 && isset($_POST['cate_ids']) && !in_array($cate_id,$_POST['cate_ids'])){
                        array_push($_POST['cate_ids'],$cate_id);
                    }
                    if (is_array($_POST['cate_ids']) && !empty($_POST['cate_ids'])) {
                        foreach ($_POST['cate_ids'] as $key => $val){
                            $val = intval($val);
                            $goods_class = $model_class->getGoodsClassLineForTag($val);
                            $taggc_data[$key]['gc_id_1']            = intval($goods_class['gc_id_1']);
                            $taggc_data[$key]['gc_id_2']            = intval($goods_class['gc_id_2']);
                            $taggc_data[$key]['gc_id_3']            = intval($goods_class['gc_id_3']);
                            $taggc_data[$key]['gc_name']            = $goods_class['gc_tag_name'];
                            $taggc_data[$key]['gc_id'] = $val;
                            $taggc_data[$key]['tag_id'] = $result;
                        }
                    } else {
                        $taggc_data[0]['gc_id'] = 0;
                        $taggc_data[0]['gc_id_1'] = 0;
                        $taggc_data[0]['gc_id_2'] = 0;
                        $taggc_data[0]['gc_id_3'] = 0;
                        $taggc_data[0]['gc_name'] = "";
                        $taggc_data[0]['tag_id'] = $result;
                    }
                    Model('tags_goods')->addTagsGc($taggc_data);
                    $url = array(
                        array(
                            'url'=>'index.php?act=goods_tag&op=goods_tag_add&gc_parent_id='.$_POST['gc_parent_id'],
                            'msg'=>$lang['goods_tag_add_again'],
                        ),
                        array(
                            'url'=>'index.php?act=goods_tag&op=tags_list',
                            'msg'=>$lang['goods_tag_add_back_to_list'],
                        )
                    );
                    $this->log(L('nc_add,goods_tag_index_tag').'['.$_POST['tag_name'].']',1);
                    showMessage($lang['nc_common_save_succ'],$url);
                }else {
                    $this->log(L('nc_add,goods_tag_index_tag').'['.$_POST['tag_name'].']',0);
                    showMessage($lang['nc_common_save_fail']);
                }
            }
        }

        //父类列表，只取到第二级
        $parent_list = $model_class->getTreeClassList(2);
        $gc_list = array();
        if (is_array($parent_list)){
            foreach ($parent_list as $k => $v){
                $parent_list[$k]['gc_name'] = str_repeat("&nbsp;",$v['deep']*2).$v['gc_name'];
                if($v['deep'] == 1) $gc_list[$k] = $v;
            }
        }
        Tpl::output('gc_list', $gc_list);
        Tpl::showpage('goods_tag.add');
    }

    /**
     * 编辑
     */
    public function goods_tag_editOp(){
        $lang   = Language::getLangContent();
        $model_tag = Model('tags_goods');
        $model_class = Model('goods_class');
        if (chksubmit()){
            $obj_validate = new Validate();
            $obj_validate->validateparam = array(
                array("input"=>$_POST["tag_name"], "require"=>"true", "message"=>$lang['goods_tag_add_name_null']),
                array("input"=>$_POST["tag_sort"], "require"=>"true", 'validator'=>'Number', "message"=>$lang['goods_tag_add_sort_int']),
            );
            $error = $obj_validate->validate();
            if ($error != ''){
                showMessage($error);
            }
            if(isset($_POST['cate_id']) && is_array($_POST['cate_id']) && count($_POST['cate_id'])>0){
                $cate_id = intval($_POST['cate_id'][0]);
            }

            // 更新分类信息
            $tag_id = intval($_POST['tag_id']);
            $where = array('tag_id' => $tag_id);
            $tags_info = $model_tag->getTagsInfo($tag_id,"tag_name,tag_id");
            $update_array = array();
            $update_array['tag_name']        = trim($_POST['tag_name']);
            //$update_array['gc_id']        = intval($_POST['gc_id']);
            $update_array['tag_sort']        = intval($_POST['tag_sort']);
            $update_array['tag_state']        = intval($_POST['tag_state']);
            $update_array['tag_type_id']        = intval($_POST['tag_type_id']);
            /*$update_array['gc_id']              = $cate_id;
            if ($cate_id > 0) {
                $goods_class = $model_class->getGoodsClassLineForTag($cate_id);
                $update_array['gc_id_1']            = intval($goods_class['gc_id_1']);
                $update_array['gc_id_2']            = intval($goods_class['gc_id_2']);
                $update_array['gc_id_3']            = intval($goods_class['gc_id_3']);
                $update_array['gc_name']            = $goods_class['gc_tag_name'];
            }*/
            $result = $model_tag->editTags($update_array, $where);
            if (!$result){
                $this->log(L('nc_edit,goods_tag_index_tag').'['.$_POST['tag_name'].']',0);
                showMessage($lang['goods_tag_batch_edit_fail']);
            }

            $has__list = $model_tag->getTagGcList(array('tag_id'=>$tag_id),'gc_id');
            $gc_id_arr = array();
            if (!empty($has__list)) {
                foreach ($has__list as $val) {
                    $gc_id_arr[] = $val['gc_id'];
                }
            }

            $taggc_data = [];
            if($cate_id>0 && !isset($_POST['cate_ids'])){
                $_POST['cate_ids'] = array($cate_id);
            }else if($cate_id>0 && isset($_POST['cate_ids']) && !in_array($cate_id,$_POST['cate_ids'])){
                array_push($_POST['cate_ids'],$cate_id);
            }


            if (is_array($_POST['cate_ids']) && !empty($_POST['cate_ids'])) {
                foreach ($_POST['cate_ids'] as $key => $val){
                    $val = intval($val);
                    if (in_array($val,$gc_id_arr) || $val==0) {
                        continue;
                    }else{
                        $goods_class = $model_class->getGoodsClassLineForTag($val);
                        $taggc_data[$key]['gc_id_1']            = intval($goods_class['gc_id_1']);
                        $taggc_data[$key]['gc_id_2']            = intval($goods_class['gc_id_2']);
                        $taggc_data[$key]['gc_id_3']            = intval($goods_class['gc_id_3']);
                        $taggc_data[$key]['gc_name']            = $goods_class['gc_tag_name'];
                        $taggc_data[$key]['gc_id'] = $val;
                        $taggc_data[$key]['tag_id'] = $tag_id;
                    }
                }
            }else{
                $taggc_data[0]['gc_id'] = 0;
                $taggc_data[0]['gc_id_1'] = 0;
                $taggc_data[0]['gc_id_2'] = 0;
                $taggc_data[0]['gc_id_3'] = 0;
                $taggc_data[0]['gc_name'] = "";
                $taggc_data[0]['tag_id'] = $tag_id;
            }
            $resetData = array_values($taggc_data);
            if (is_array($resetData) && !empty($resetData)) {
                // 删除分类为0的 记录
                $model_tag->delTagsGc(array('tag_id'=>$tag_id,'gc_id_1'=>0,'gc_id_2'=>0,'gc_id_3'=>0,'gc_id'=>0));
                $model_tag->addTagsGc($resetData);
            }


            if($tags_info['tag_name'] != $update_array['tag_name']) {
                $model_tag->editTagsGoods(array("tag_title"=>$update_array['tag_name']),array('tag_id' => $tag_id));
            }

            $url = array(
                array(
                    'url'=>'index.php?act=goods_tag&op=goods_tag_edit&tag_id='.intval($_POST['tag_id']),
                    'msg'=>$lang['goods_tag_batch_edit_again'],
                ),
                array(
                    'url'=>'index.php?act=goods_tag&op=tags_list',
                    'msg'=>$lang['goods_tag_add_back_to_list'],
                )
            );
            $this->log(L('nc_edit,goods_tag_index_tag').'['.$_POST['tag_name'].']',1);
            showMessage($lang['goods_tag_batch_edit_ok'],$url,'html','succ',1,5000);
        }
        //编辑
        $tag_id = intval($_GET['tag_id']);
        $tag_info  = $model_tag->getTagsInfo($tag_id);
        if($tag_info['gc_id'] > 0) {
            $class_array = $model_class->getGoodsClassInfoById(intval($tag_info['gc_id']));
            $tag_info['cate_name'] = $class_array['gc_name'];
        }
        if(!$tag_info){
            showMessage($lang['param_error']);
        }
        $tag_gc_list = $model_tag->getTagGcList(array('tag_id'=>$tag_id));
        // 一级分类列表
        $gc_list = Model('goods_class')->getGoodsClassListByParentId(0);
        Tpl::output('gc_list', $gc_list);
        Tpl::output('taggc_list', $tag_gc_list);
        Tpl::output('tag_info',$tag_info);
        Tpl::showpage('goods_tag.edit');
    }

    /**
     * 删除标签
     */
    public function goods_tag_delOp(){
        $id = trim($_GET['id']);
        if ($id != ''){
            //删除关联商品标签
            Model('tags_goods')->delGoodsTags(array('tag_id'=>array('in',$id)));
            Model('tags_goods')->delTagsGc(array('tag_id'=>array('in',$id)));
            //删除标签
            $result = Model('tags_goods')->delTags(array('tag_id'=>array('in',$id)));
            if ($result) {

                $this->log(L('goods_tag_delete') . '[ID:' . $id . ']',1);
                exit(json_encode(array('state'=>true,'msg'=>'删除成功')));
            }else{
                exit(json_encode(array('state'=>false,'msg'=>'删除失败')));
            }
        }else {
            exit(json_encode(array('state'=>false,'msg'=>'无效请求')));
        }
    }

    //异步获取分类数据
    public function get_cate_listOp()
    {
        $sign = intval($_GET['sign']);
        $cid = intval($_GET['cid']);
        if($cid)
        {
            $model_class = Model('goods_class');
            $cate_list=$model_class->getGoodsClassList(array('gc_parent_id'=>$cid),"gc_id,gc_name");
            if($sign==1)$head='<select id="second_cate" name="second_cate" onchange="showSecondCate()"><option value="0">请选择</option>';
            elseif($sign==2) $head='<select id="three_cate" name="three_cate" onchange="showThreeCate()"><option value="0">请选择</option>';
            else $head='<select id="area" name="area"><option value="0">请选择</option>';
            if(is_array($cate_list) && !empty($cate_list))
            {
                foreach($cate_list as $rs)
                {
                    $id=$rs['gc_id'];
                    $name=$rs['gc_name'];
                    $mid.="<option value='$id'>$name</option>";
                }
                header("Cache-Control: no-cache");
                echo $head.$mid.'</select>';
            }
        }else{
            echo'';
        }
    }

    /**
     * ajax操作
     */
    public function ajaxOp(){
        $model_tag = Model('tags_goods');
        switch ($_GET['branch']){
            /**
             * 更新分类
             */
            case 'tag_name':
                $where = array('tag_id' => intval($_GET['id']));
                $update_array = array();
                $update_array['tag_name'] = trim($_GET['value']);
                $model_tag->editTags($update_array, $where);
                $return = true;
                exit(json_encode(array('result'=>$return)));
                break;
            /**
             * 分类 排序 显示 设置
             */
            case 'tag_sort':
                $where = array('tag_id' => intval($_GET['id']));
                $update_array = array();
                $update_array['tag_sort'] = $_GET['value'];
                $model_tag->editTags($update_array, $where);
                $return = 'true';
                exit(json_encode(array('result'=>$return)));
                break;
            /**
             * 添加、修改操作中 检测类别名称是否有重复
             */
            case 'check_tag_name':
                $condition['tag_name'] = trim($_GET['tag_name']);
                $condition['gc_id'] = array('neq', intval($_GET['gc_id']));
                $tag_list = $model_tag->getTagsList($condition);
                if (empty($tag_list)){
                    echo 'true';exit;
                }else {
                    echo 'false';exit;
                }
                break;
           case 'deltag';
               $condition['tag_gc_id'] = intval($_GET['id']);
               $result = $model_tag->delTagsGc($condition);
               if (!empty($result)){
                   echo 1;exit;
               }else {
                   echo 0;exit;
               }
                break;
        }
    }
}
