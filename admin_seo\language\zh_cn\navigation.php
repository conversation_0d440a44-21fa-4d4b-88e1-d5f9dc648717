<?php
defined('InShopNC') or exit('Access Invalid!');
/**
 * index
 */
$lang['navigation_index_del_succ']		= '删除页面导航成功';
$lang['navigation_index_choose_del']	= '请选择要删除的内容';
$lang['navigation_index_top']			= '头部';
$lang['navigation_index_center']		= '中部';
$lang['navigation_index_bottom']		= '底部';
$lang['navigation_index_nav']			= '页面导航';
$lang['navigation_index_nav_subhead']	= '网站全局顶部/底部及商城主导航设置';
$lang['navigation_index_title']			= '标题';
$lang['navigation_index_location']		= '所在位置';
$lang['navigation_index_url']			= '链接';
$lang['navigation_index_open_new']		= '新窗口打开';
/**
 * 添加导航
 */
$lang['navigation_add_partner_null']	= '标题不能为空';
$lang['navigation_add_sort_int']		= '分类排序仅能为数字';
$lang['navigation_add_url_wrong']		= '链接格式不正确';
$lang['navigation_add_goods_class_null']	= '商品分类不能为空';
$lang['navigation_add_article_class_null']	= '文章分类不能为空';
$lang['navigation_add_activity_null']	= '活动不能为空';
$lang['navigation_add_back_to_list']	= '返回页面导航列表';
$lang['navigation_add_again']			= '继续新增';
$lang['navigation_add_succ']			= '添加页面导航成功';
$lang['navigation_add_fail']			= '添加页面导航失败';
$lang['navigation_add_article_class']	= '文章分类';
$lang['navigation_add_goods_class']		= '商品分类';
$lang['navigation_add_custom']			= '自定义导航';
$lang['navigation_add_type']			= '导航类型';
$lang['navigation_add_activity']		= '活动';
/**
 * 编辑导航
 */
$lang['navigation_edit_again']	= '重新编辑该页面导航';
$lang['navigation_edit_succ']	= '编辑页面导航成功';
$lang['navigation_edit_fail']	= '编辑页面导航失败';