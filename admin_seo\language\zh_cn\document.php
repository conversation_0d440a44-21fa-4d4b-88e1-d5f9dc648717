<?php
defined('InShopNC') or exit('Access Invalid!');
/**
 * index
 */
$lang['document_index_document']		= '会员协议';
$lang['document_index_document_subhead']= '网站会员协议设置管理';
$lang['document_index_title']			= '标题';
$lang['document_index_content']			= '文章内容';
$lang['document_index_pic_upload']		= '图片上传';
$lang['document_index_batch_upload']	= '批量上传';
$lang['document_index_normal_upload']	= '普通上传';
$lang['document_index_uploaded_pic']	= '已传图片';
$lang['document_index_insert']			= '插入编辑器';
$lang['document_index_title_null']		= '文章标题不能为空';
$lang['document_index_content_null']	= '文章内容不能为空';
$lang['document_index_del_fail']		= '删除失败';
$lang['document_index_help1']			= '在相关操作处可查看具体内容，例：在注册会员时须查看用户服务协议';
/**
 * 编辑系统文章
 */
$lang['document_edit_back_to_list']		= '返回文章列表';
$lang['document_edit_again']			= '重新编辑该文章';
$lang['document_edit_time']				= '时间';
$lang['article_add_img_wrong']      = '图片限于png,gif,jpeg,jpg格式';
/**
 * iframe上传
 */
$lang['document_iframe_upload_fail']	= '上传失败';
$lang['document_iframe_upload']         = '上传';