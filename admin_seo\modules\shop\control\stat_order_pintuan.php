<?php
/**
 * 交易管理
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;
use Upet\Models\DcOrder\PinOrderGroup;
use Upet\Models\Order;
use Upet\Models\VrOrder;
use Upet\Models\DcOrder\PinOrderNotify;
use Box\Spout\Common\Type;
use Box\Spout\Writer\Style\StyleBuilder;
use Box\Spout\Writer\WriterFactory;

defined('InShopNC') or exit('Access Invalid!');
class stat_order_pintuanControl extends SystemControl{
    /**
     * 每次导出订单数量
     * @var int
     */
    const EXPORT_SIZE = 5000;

    public function __construct(){
        parent::__construct();
        Language::read('trade');
    }

    public function indexOp(){
        Tpl::output('admin_id',$this->admin_info['id']);
        Tpl::showpage('stat_order_pintuan');
    }

    public function get_xmlOp(){
        $_REQUEST['page'] = $_REQUEST['curpage'];
        if ($_SESSION['store_id'] > 1) {
            exit(Tpl::flexigridXML(array(), 0, array('total' => 0)));
        }

        $list = PinOrderGroup::alias('g');
        $list = $list->join('dc_order.pin_order_main m','g.parent_pin_order_sn = m.pin_head_order_sn');
        $list = $list->field('g.id,g.status,g.create_time,g.pin_order_sn,g.parent_pin_order_sn,g.pay_price,g.is_virtual,m.status as p_status');
        $list = $list->where('g.pin_order_sn','<>','');
        if ($_REQUEST['query'] != '') {
            $list = $list->where('g.pin_order_sn',$_REQUEST['query']);
        }
        if ($_REQUEST['id'] != '') {
            $list = $list->whereIn('g.id',$_REQUEST['id']);
        }
        $start_time = $_GET['query_start_date'];
        $end_time = $_GET['query_end_date'];
        if ($_GET['qtype_time'] && ($start_time || $end_time)) {
            $query_end_date = $_GET['query_end_date'] ? date('Y-m-d', strtotime($end_time) + 86400) : '';
            $list = $list->whereTime('g.create_time','between',[$start_time,$query_end_date]);
        }
        $list = $list->order('g.id','desc');
        $data = $param = array();
        if ($_REQUEST['export_type'] != 1){
            $list = $list->paginate($_POST['rp']);
            $data['total_num'] = $list->total();
            $order_list = $list->items();
            $data['now_page'] =$list->currentPage();
        }else {
            if (!is_numeric($_GET['curpage'])) {
                $list = $list->paginate($_POST['rp']);
                $count = $list->total();
                $array = array();
                if ($count > self::EXPORT_SIZE) {   //显示下载链接
                    $page = ceil($count / self::EXPORT_SIZE);
                    for ($i = 1; $i <= $page; $i++) {
                        $limit1 = ($i - 1) * self::EXPORT_SIZE + 1;
                        $limit2 = $i * self::EXPORT_SIZE > $count ? $count : $i * self::EXPORT_SIZE;
                        $array[$i] = $limit1 . ' ~ ' . $limit2;
                    }
                    Tpl::output('list',$array);
                    Tpl::output('murl','index.php?act=stat_order_pintuan&op=index');
                    Tpl::showpage('export.excel');
                    exit();
                } else {  //如果数量小，直接下载
                    if($count > 15) {
                        $list = $list->paginate(self::EXPORT_SIZE);
                    }
                    $order_list = $list->items();
                }

            } else {  //下载
                $list = $list->paginate(self::EXPORT_SIZE);
                $order_list = $list->items();
            }
        }

        $pin_order_sn = array_column($order_list, 'pin_order_sn');
        //拼团成功的虚拟订单
        $refund_info = VrOrder::alias('o')
            ->field('o.order_sn,o.erp_order_sn,o.order_state,o.payment_code,o.payment_time,r.refund_sn,o.refund_amount,r.dy_dealtime')
            ->leftJoin('vr_refund r', 'r.order_id = o.order_id')
            ->whereIn('o.order_sn', $pin_order_sn)
            ->select()->toArray();
        //将$refund1转换成以order_sn为键的数组
        $refund_info = array_column($refund_info, null, 'order_sn');

        //拼团成功的实物订单
        $refund2 = Order::alias('o')
            ->field('o.order_sn,o.pay_sn,o.shipping_fee,o.order_state,o.payment_code,o.payment_time,r.refund_sn,o.refund_amount,r.dy_dealtime')
            ->leftJoin('refund_return r', 'r.order_id = o.order_id')
            ->whereIn('o.pay_sn', $pin_order_sn)
            ->where('o.order_father', ['>',0])
            ->select()->toArray();
        //将$refund2转换成以pay_sn为键的数组
        $refund2 = array_column($refund2, null, 'pay_sn');

        //拼团失败的订单
        $refund3 = PinOrderNotify::alias('n')
            ->field('n.pin_order_sn,n.notify_type,n.create_time,r.trade_no,r.refund_amount,r.add_time,n.pay_mode')
            ->leftJoin('pay_center.pay_info p','n.pin_order_sn = p.order_id')
            ->leftJoin('pay_center.refund_info r','p.id = r.pay_id')
            ->whereIn('n.notify_type',[1,4])
            ->whereIn('n.pin_order_sn',$pin_order_sn)
            ->select()->toArray();
        //将$refund3转换成以pin_order_sn为键的数组
        foreach ($refund3 as $k => $v) {
            $refund[$v['pin_order_sn']][] = $v;
        }

        foreach ($order_list as $v) {
            $param['id'] = $v->id;
            //状态：0 已取消 10未支付 20 拼团进行中 30拼团成功 40拼团失败
            $param['status'] = $v->status == 0 ? '已取消' : ($v->status == 10 ? '未支付' : ($v->status == 20 ? '拼团进行中' : ($v->status == 30 ? '拼团成功' : ($v->status == 40 ? '拼团失败' : ''))));
            $param['create_time'] = $v->create_time;
            $param['parent_pin_order_sn'] = $v->parent_pin_order_sn;
            $param['pin_order_sn'] = $v->pin_order_sn;
            $param['pay_sn'] =  $v->pin_order_sn;
            $param['is_virtual'] = $v->is_virtual == 1 ? '是' : '否';
            $order_sn = $child_status = $payment_time = $payment_code = $refund_sn = $refund_time = $refund_amount = $shipping_fee = '';

            //如果拼团成功，则查询电商的退款信息；否则查询拼团的退款记录
            if ($v->p_status == 20) {
                if($v->is_virtual == 1) {
                    $refund1 = $refund_info[$v->pin_order_sn];
                    $order_sn = $refund1? $refund1['erp_order_sn'] : '';
                    //订单状态：0(已取消)10(默认):未付款;20:已付款;40:已完成;
                    $child_status = $refund1? ($refund1['order_state'] == 0 ? '已取消' : ($refund1['order_state'] == 10 ? '未付款' : ($refund1['order_state'] == 20 ? '已付款' : ($refund1['order_state'] == 40 ? '已完成' : '')))) : '';
                } else {
                    $refund1 = $refund2[$v->pin_order_sn];
                    if ($refund1) {
                        $order_sn = $refund1['order_sn'];
                        $shipping_fee = $refund1['shipping_fee'] > 0?number_format($refund1['shipping_fee'], 2):'';
                        //订单状态：0(已取消)10(默认):未付款;20:已付款;30:已发货;40:已收货;50:部分发货;
                        $child_status = $refund1['order_state'] == 0 ? '已取消' : ($refund1['order_state'] == 10 ? '未付款' : ($refund1['order_state'] == 20 ? '已付款' : ($refund1['order_state'] == 30 ? '已发货' : ($refund1['order_state'] == 40 ? '已收货' : ($refund1['order_state'] == 50 ? '部分发货' : '')))));
                    }
                }
                if ($refund1) {
                    $payment_time = date('Y-m-d H:i:s', $refund1['payment_time']);
                    $payment_code = orderPaymentName($refund1['payment_code']);
                    $refund_sn = $refund1['refund_sn']?:'';
                    $refund_time = $refund1['dy_dealtime']?date('Y-m-d H:i:s', $refund1['dy_dealtime']) : '';
                    $refund_amount =   number_format($refund1['refund_amount'], 2) ?: '';
                }

            }
            $refund3 = $refund[$v->pin_order_sn];
            if ($refund3) {
                foreach ($refund3 as $k => $vv) {
                    if ($vv['notify_type'] == 1) {//支付信息
                        $payment_time = $vv['create_time'];
                        //支付类型1支付宝 2微信 4其他,8 储蓄卡支付
                        $payment_code = $vv['pay_mode'] == 1 ? '支付宝' : ($vv['pay_mode'] == 2 ? '微信' : ($vv['pay_mode'] == 4 ? '其他' : ($vv['pay_mode'] == 8 ? '储蓄卡支付' : '')));
                    } elseif($vv['notify_type'] == 4) {//退款信息
                        $refund_sn = $vv['trade_no'];
                        $refund_time = $vv['create_time'];
                        $refund_amount = number_format($vv['refund_amount'] / 100, 2);
                    }
                }
            }

            $param['order_sn'] = $order_sn;
            $param['pay_price'] = number_format($v->pay_price / 100, 2);
            $param['shipping_fee'] = $shipping_fee;
            $param['child_status'] = $child_status;
            $param['payment_time'] = $payment_time;
            $param['payment_code'] = $payment_code;
            $param['refund_sn'] = $refund_sn;
            $param['refund_time'] = $refund_time;
            $param['refund_amount'] = $refund_amount?:'';

            $data['list'][$v->id] = $param;
        }
        if ($_REQUEST['export_type'] == 1) {
            $this->createExcel2($data);
            exit();
        }

        exit(Tpl::flexigridXML($data));
    }

    /**
     * 导出拼团订单对账明细
     *
     */
    private function createExcel2($data = array()){
        $writer = WriterFactory::create(Type::XLSX);
        $writer->setDefaultRowStyle((new StyleBuilder())->setShouldWrapText(false)->build());//不自动换行
        $writer->openToBrowser('财务对账明细-拼团-'.$_GET['curpage'].'-'.date('Y-m-d-H',time()).'.xlsx');
        $writer->addRow(['拼团状态','生成订单时间','团长订单','主订单号','支付单号','是否虚拟订单','子订单号','主单金额','运费',
            '子订单状态','支付时间','支付方式','退款单号','退款时间','退款金额']);
        $list = array();
        foreach ((array)$data['list'] as $k=>$order_info){
            $list[$k]['status'] = $order_info['status'];
            $list[$k]['create_time'] = $order_info['create_time'];
            $list[$k]['parent_pin_order_sn'] = $order_info['parent_pin_order_sn'];
            $list[$k]['pin_order_sn'] = $order_info['pin_order_sn'];
            $list[$k]['pay_sn'] = $order_info['pay_sn'];
            $list[$k]['is_virtual'] = $order_info['is_virtual'];
            $list[$k]['order_sn'] = strval($order_info['order_sn']);
            $list[$k]['pay_price'] = $order_info['pay_price'];
            $list[$k]['shipping_fee'] = $order_info['shipping_fee'];
            $list[$k]['child_status'] = $order_info['child_status'];
            $list[$k]['payment_time'] = $order_info['payment_time'];
            $list[$k]['payment_code'] = $order_info['payment_code'];
            $list[$k]['refund_sn'] = $order_info['refund_sn'];
            $list[$k]['refund_time'] = $order_info['refund_time'];
            $list[$k]['refund_amount'] = $order_info['refund_amount']>0?$order_info['refund_amount']:'';
        }
        $writer->addRows($list);
        $writer->close();
    }
}
