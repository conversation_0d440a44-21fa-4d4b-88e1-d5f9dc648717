<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3><?php echo $output['top_title'];?></h3>
                <h5><?php echo $output['top_desc'];?></h5>
            </div>
            <ul class="tab-base nc-row">
                <?php echo $output['top_tab'];?>
            </ul>
        </div>
        <div class="explanation" id="explanation">
            <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
                <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
                <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
            <ul>
                <li>针对健康会员卡的分销信息变更为主动购买</li>
            </ul>
        </div>
        <div id="flexigrid"></div>

    <form id="add_form" method="post" enctype="multipart/form-data" action="index.php?act=order_delay_delivery&op=update_vip_card_state">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="member_mobile">会员手机号：</label>
                </dt>
                <dd class="opt">
                    <input name="member_mobile" id="member_mobile" value="" type="text" class="input-txt" autocomplete="off" placeholder="请输入手机号">
                    <p class="notic"></p>
                    <span class="err"></span>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">会员状态</dt>
                <dd class="opt">
                    <div class="onoff">
                        <label for="voucher_allow_1" class="cb-enable <?php if($output['list_setting']['state'] == '1'){ ?>selected<?php } ?>" title="<?php echo $lang['open'];?>"><?php echo $lang['open'];?></label>
                        <label for="voucher_allow_0" class="cb-disable <?php if($output['list_setting']['state'] == '0'){ ?>selected<?php } ?>" title="<?php echo $lang['close'];?>"><?php echo $lang['close'];?></label>
                        <input id="voucher_allow_1" name="state" <?php if($output['list_setting']['state'] == '1'){ ?>checked="checked"<?php } ?> value="1" type="radio">
                        <input id="voucher_allow_0" name="state" <?php if($output['list_setting']['state'] == '0'){ ?>checked="checked"<?php } ?> value="0" type="radio">
                    </div>
                    <p class="notic"><?php echo $lang['voucher_allow_notice'];?></p>
                </dd>
            </dl>
            <div class="bot"><a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="btn_add"><?php echo $lang['nc_submit'];?></a></div>
        </div>
    </form>
</div>
</div>
<script type="text/javascript">
    //
    $(document).ready(function(){
        //添加按钮的单击事件
        $("#btn_add").click(function(){
            $("#add_form").submit();
        });
        //页面输入内容验证
        $("#add_form").validate({
            errorPlacement: function(error, element){
                var error_td = element.parent('dd').children('span.err');
                error_td.append(error);
            },
            rules : {
                order_sn: {
                    required : true,
                }
            },
            messages : {
                order_sn: {
                    required : '<i class="fa fa-exclamation-circle"></i>手机号必填',
                }
            }
        });
    });
</script>
