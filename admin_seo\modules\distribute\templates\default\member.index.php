<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title">
      <div class="subject">
        <h3>分销商管理</h3>
        <h5>分销会员及认证管理</h5>
      </div>
      <?php echo $output['top_link'];?>
    </div>
  </div>
  <!-- 操作说明 -->
  <div class="explanation" id="explanation">
    <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
      <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
      <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
    <ul>
        <?php echo $output['top_tips'];?>
    </ul>
  </div>
  <div id="flexigrid"></div>
    <div class="ncap-search-ban-s" id="searchBarOpen"><i class="fa fa-search-plus"></i>高级搜索</div>
    <div class="ncap-search-bar">
        <div class="handle-btn" id="searchBarClose"><i class="fa fa-search-minus"></i>收起边栏</div>
        <div class="title">
            <h3>高级搜索</h3>
        </div>
        <form method="get" name="formSearch" id="formSearch">
            <div id="searchCon" class="content">
                <div class="layout-box">
                    <dl>
                        <dt>分销认证来源</dt>
                        <dd>
                            <label>
                                <select name="distribute_state" class="s-select">
                                    <option value="">请选择</option>
                                    <option value="1">内部分销员</option>
                                    <option value="2">外部代理人</option>

                                </select>
                            </label>
                        </dd>
                    </dl>
                    <dl>
                        <dt>是否匹配SHR</dt>
                        <dd>
                            <label>
                                <select name="shr_state" class="s-select">
                                    <option value="">请选择</option>
                                    <option value="1">是</option>
                                    <option value="0">否</option>
                                </select>
                            </label>
                        </dd>
                    </dl>
                </div>
            </div>
            <div class="bottom"> <a href="javascript:void(0);" id="ncsubmit" class="ncap-btn ncap-btn-green mr5">提交查询</a><a href="javascript:void(0);" id="ncreset" class="ncap-btn ncap-btn-orange" title="撤销查询结果，还原列表项所有内容"><i class="fa fa-retweet"></i><?php echo $lang['nc_cancel_search'];?></a></div>
        </form>
    </div>
</div>
<script type="text/javascript">
$(function(){

    // 高级搜索提交
    $('#ncsubmit').click(function(){
        $("#flexigrid").flexOptions({url: 'index.php?act=distri_member&op=get_xml&mem_state=<?php echo intval($output['mem_stat']);?>&'+$("#formSearch").serialize(),query:'',qtype:''}).flexReload();
    });
    // 高级搜索重置
    $('#ncreset').click(function(){
        $("#flexigrid").flexOptions({url: 'index.php?act=distri_member&op=get_xml&mem_state=<?php echo intval($output['mem_stat']);?>'}).flexReload();
        $("#formSearch")[0].reset();
    });
    $("#flexigrid").flexigrid({
        url: 'index.php?act=distri_member&op=get_xml&mem_state=<?php echo intval($output['mem_stat']);?>',
        colModel : [
            {display: '操作', name : 'operation', width : 180, sortable : false, align: 'center'},
            {display: '会员ID', name : 'member_id', width : 60, sortable : true, align: 'center'},
            {display: '会员名称', name : 'member_name', width : 150, sortable : true, align: 'center'},
            {display: '会员手机', name : 'member_mobile', width : 80, sortable : true, align: 'center'},
            {display: '匹配SHR', name : 'shr_state', width : 100, sortable : true, align: 'center'},
            {display: '是否绑定客服', name : 'is_bind', width : 80, sortable : true, align: 'center'},
            <?php if(intval($output['mem_stat']) == 1){?>
            {display: '申请状态', name : 'distri_stat', width : 60, sortable : true, align: 'left'},
             {display: '申请时间', name : 'distri_time', width : 100, sortable : true, align: 'center'},
             {display: '通过时间', name : 'distri_handle_time', width : 100, sortable : true, align: 'center'},
             {display: '绑定分院', name : 'distri_chain_name', width : 200, sortable : true, align: 'left'}
            <?php }else{?>
            {display: '分销单数', name : 'order_count', width : 100, sortable : true, align: 'center'},
            {display: '已结佣金(元)', name : 'had_pay_amount', width : 100, sortable : true, align: 'center'},
            {display: '未结佣金(元)', name : '' +
                    '', width : 100, sortable : true, align: 'center'},
            {display: '提现成功((元)', name : 'success_amount', width : 100, sortable : true, align: 'center'},
            {display: '提现申请(元)', name : 'apply_amount', width : 100, sortable : true, align: 'center'},
            {display: '待提现(元)', name : 'wait_amount', width : 100, sortable : true, align: 'center'},
            {display: '分销佣金总额(元)', name : 'distri_amount', width : 100, sortable : true, align: 'center'},
            {display: '绑定分院', name : 'distri_chain_name', width : 150, sortable : true, align: 'center'},
            {display: '财务编码', name : 'account_id', width : 50, sortable : true, align: 'left'}
            <?php }?>
            ],

        buttons : [
            <?php if(C('is_excelport')){?>
            {display: '<i class="fa fa-file-excel-o"></i>导出数据', name : 'csv', bclass : 'csv', title : '将选定行数据导出CSV文件', onpress : fg_operation }	,
            <?php }?>
            <?php if(intval($output['mem_stat']) == 1){?>
            {display: '<i class="fa fa-check-circle-o"></i>批量审核', name : 'check', bclass : 'check', title : '将选定行数据批量审核', onpress : fg_operation }
            <?php }?>
            ],

        searchitems : [
            {display: '会员ID', name : 'member_id'},
            {display: '会员名称', name : 'member_name'},
            {display: '会员手机号', name : 'member_mobile'},
            {display: '绑定分院', name : 'chain_name'}
            ],

        sortname: "distri_time",
        sortorder: "desc",
        title: '认证分销商列表'
    });
	
});

function fg_operation(name, bDiv) {
    if (name == 'csv') {
        if ($('.trSelected', bDiv).length == 0) {
            if (!confirm('您确定要下载全部数据吗？')) {
                return false;
            }
        }
        var itemids = new Array();
        $('.trSelected', bDiv).each(function(i){
            itemids[i] = $(this).attr('data-id');
        });
        fg_csv(itemids);
    }else if(name == 'check'){
        if ($('.trSelected', bDiv).length == 0) {
            if (!confirm('您确定要审核全部待审核的数据吗？')) {
                return false;
            }
        }
        var itemids = new Array();
        $('.trSelected', bDiv).each(function(i){
            itemids[i] = $(this).attr('data-id');
        });
        var ids = itemids.join(',');
        fg_check(ids);
    }
}

function fg_csv(ids) {
    id = ids.join(',');
    window.location.href = $("#flexigrid").flexSimpleSearchQueryString()+'&op=export_csv&id=' + id;
}

//商品取消分销
function fg_del(id) {
    if(confirm('清退后将不能恢复且该分销员及其的所分销的商品都将失效，针对离职员工处理，确认清退该分销员吗？')){
        $.getJSON('index.php?act=distri_member&op=member_cancle', {member_id:id}, function(data){
            if (data.state) {
                $("#flexigrid").flexReload();
            } else {
                showError(data.msg);
            }
        });
    }
}

//解绑员工与门店关系，不影响分销相关业绩
function fg_unbind(id) {
    if(confirm('解除员工与门店绑定关系，不影响分销相关业绩及粉丝关系，确认解绑吗？')){
        $.getJSON('index.php?act=distri_member&op=member_unbind', {member_id:id}, function(data){
            if (data.state) {
                $("#flexigrid").flexReload();
            } else {
                showError(data.msg);

            }
        });
    }
}

//解绑员工与门店关系，不影响分销相关业绩
function update_staff(mobile) {
    if(confirm('同步员工信息是否匹配SHR员工手机号')){
        $.getJSON('index.php?act=distri_member&op=update_staff', {mobile:mobile}, function(data){
            console.log(data.state)
        if (data.status == 200){
                showSucc(data.msg);
                $("#flexigrid").flexReload();
            }else{
                showError(data.msg);
            }
        });
    }
}

//批量审核
function fg_check(ids) {
    _uri = "index.php?act=distri_member&op=batch_auth&id=" + ids;
    CUR_DIALOG = ajax_form('member_check', '批量审核', _uri, 640);
}

</script> 

