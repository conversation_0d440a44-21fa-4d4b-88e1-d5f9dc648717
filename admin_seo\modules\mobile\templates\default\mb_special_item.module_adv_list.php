<?php defined('InShopNC') or exit('Access Invalid!');?>
<?php if($item_edit_flag) { ?>

<div class="explanation" id="explanation">
  <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
    <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
  </div>
  <ul>
    <li>点击添加新的广告条按钮可以添加新的广告条</li>
    <li>鼠标移动到已有的广告条上点击出现的删除按钮可以删除对应的广告条</li>
    <li>操作完成后点击保存编辑按钮进行保存</li>
  </ul>
</div>
<?php } ?>
<div class="index_block adv_list">
  <?php if($item_edit_flag) { ?>
  <h3>广告条版块</h3>
  <?php } ?>
  <div nctype="item_content" class="content">
    <?php if($item_edit_flag) { ?>
    <h5>内容：</h5>
    <?php } ?>
    <?php if(!empty($item_data['item']) && is_array($item_data['item'])) {?>
    <?php foreach($item_data['item'] as $item_key => $item_value) {?>
    <div nctype="item_image" class="item"> <img nctype="image" src="<?php echo getMbSpecialImageUrl($item_value['image']);?>" alt="">
      <?php if($item_edit_flag) { ?>
      <input nctype="image_name" name="item_data[item][<?php echo $item_key;?>][image]" type="hidden" value="<?php echo $item_value['image'];?>">
      <input nctype="image_type" name="item_data[item][<?php echo $item_key;?>][type]" type="hidden" value="<?php echo $item_value['type'];?>">
      <input nctype="image_data" name="item_data[item][<?php echo $item_key;?>][data]" type="hidden" value="<?php echo $item_value['data'];?>">
        <input nctype="image_sort" name="item_data[item][<?php echo $item_key;?>][sort]" type="hidden"  value="<?php echo $item_value['sort'];?>">
          <input nctype="wap_url" name="item_data[item][<?php echo $item_key;?>][wap_url]" type="hidden"  value="<?php echo $item_value['wap_url'];?>">
        <input nctype="image" name="item_data[sort]" type="hidden"  value="1">
        <a nctype="btn_edit_item_image" data-desc="320*130" href="javascript:;" style="right: 60px;"><i class="fa fa-pencil-square-o"></i>编辑</a>
        <a nctype="btn_del_item_image" href="javascript:;"><i class="fa fa-trash-o"></i>删除</a>
      <?php } ?>
    </div>
    <?php } ?>
    <?php } ?>
  </div>
  <?php if($item_edit_flag) { ?>
  <a nctype="btn_add_item_image" class="ncap-btn" data-desc="640*340" href="javascript:;"><i class="fa fa-plus"></i>添加新的广告条</a>
  <?php } ?>
</div>
