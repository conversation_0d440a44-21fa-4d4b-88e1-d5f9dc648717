<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title">
      <div class="subject">
        <h3>变更管理</h3>
        <h5>更改分销员的订单记录</h5>
      </div>
    </div>
  </div>
  <!-- 操作说明 -->
  <div class="explanation" id="explanation">
    <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
      <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
      <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
    <ul>
      <li>查看变更记录审核情况</li>
    </ul>
  </div>
  <div id="flexigrid"></div>
    <div class="ncap-search-ban-s" id="searchBarOpen"><i class="fa fa-search-plus"></i>高级搜索</div>
    <div class="ncap-search-bar">
      <div class="handle-btn" id="searchBarClose"><i class="fa fa-search-minus"></i>收起边栏</div>
      <div class="title">
        <h3>高级搜索</h3>
      </div>
      <form method="get" name="formSearch" id="formSearch">
        <div id="searchCon" class="content">
          <div class="layout-box">
            <dl>
              <dt>订单编号</dt>
              <dd>
                <input type="text" value="" name="order_sn" id="order_sn" class="s-input-txt">
              </dd>
            </dl>
            <dl>
              <dt>商品名称</dt>
              <dd>
              <label><input type="text" value="" name=goods_name id="goods_name" class="s-input-txt"></label>
              <label><input type="checkbox" value="1" name="jq_query">精确</label>
              </dd>
            </dl>

          </div>
        </div>
        <div class="bottom">
          <a href="javascript:void(0);" id="ncsubmit" class="ncap-btn ncap-btn-green">提交查询</a>
          <a href="javascript:void(0);" id="ncreset" class="ncap-btn ncap-btn-orange" title="撤销查询结果，还原列表项所有内容"><i class="fa fa-retweet"></i><?php echo $lang['nc_cancel_search'];?></a>
        </div>
      </form>
    </div>
</div>
<script type="text/javascript">
$(function(){
    // 高级搜索提交
    $('#ncsubmit').click(function(){
        $("#flexigrid").flexOptions({url: 'index.php?act=distri_modify&op=get_modify_xml&'+$("#formSearch").serialize(),query:'',qtype:''}).flexReload();
    });

    // 高级搜索重置
    $('#ncreset').click(function(){
        $("#flexigrid").flexOptions({url: 'index.php?act=distri_modify&op=get_modify_xml'}).flexReload();
        $("#formSearch")[0].reset();
    });
    $("#flexigrid").flexigrid({
        url: 'index.php?act=distri_modify&op=get_modify_xml',
        colModel : [
            {display: '审批凭证', name : 'operation', width : 60, sortable : false, align: 'center', className: 'handle-s'},
            {display: '变更时间', name : 'op_date', width : 120, sortable : true, align: 'center'},
            {display: '操作人', name : 'op_name', width : 60, sortable : true, align: 'center'},
            {display: '订单编号', name : 'order_sn', width : 130, sortable : true, align: 'left'},
			{display: '商品名称', name : 'goods_name', width : 300, sortable : true, align: 'left'},
			{display: '支付金额', name : 'pay_goods_amount', width: 70, sortable : true, align : 'center'},
			{display: '分销佣金', name : 'dis_pay_amount', width: 70, sortable : true, align : 'center'},
            {display: '原分销员ID', name : 'dis_o_memberid', width : 70, sortable : true, align: 'center'},
            {display: '原所属分院', name : 'store_id', width : 150, sortable : true, align: 'center'},
            {display: '变更后分销员ID', name : 'dis_n_memberid', width : 90, sortable : true, align: 'center'},
            {display: '所属分院', name : 'store_id', width : 150, sortable : true, align: 'center'},

            ],
        searchitems : [
           {display: '订单编号', name : 'order_sn'}, {display: '商品名称', name : 'goods_name'}, {display: '所属分院', name : 'chain_name'}, {display: '分销员ID', name : 'dis_memberid'}
        ],
        sortname: "id",
        sortorder: "desc",
        title: '变更记录列表'
    });
});

</script> 
