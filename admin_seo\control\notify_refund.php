<?php
/**
 * 在线退款异步通知
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

defined('InShopNC') or exit('Access Invalid!');

class notify_refundControl{
    /**
     * 支付宝
     *
     */
    public function alipayOp(){
        $api_file = BASE_PATH.DS.'api'.DS.'refund'.DS.'alipay'.DS.'alipay.class.php';
        include $api_file;
        $result = "fail";
        $condition = array();
        $condition['payment_code'] = 'alipay';
        $model_payment = Model('payment');
        $payment_info = $model_payment->getPaymentInfo($condition);//接口参数
        $payment_config = unserialize($payment_info['payment_config']);
        
        $alipay_config = array();
        $alipay_config['seller_email'] = $payment_config['alipay_account'];
        $alipay_config['partner'] = $payment_config['alipay_partner'];
        $alipay_config['key'] = $payment_config['alipay_key'];
        $alipayNotify = new AlipayNotify($alipay_config);
        $verify_result = $alipayNotify->verifyNotify();
        if($verify_result) {//验证成功
            $batch_no = $_POST['batch_no'];//批次号
            $success_num = $_POST['success_num'];//退交易成功的笔数
            $result_details = $_POST['result_details'];//退款结果明细:交易号^退款金额^处理结果
            $details = explode('^', $result_details);
            if($success_num > 0 && substr($details[2], 0, 7) == 'SUCCESS') {
                $detail_array = array();
                $detail_array['pay_amount'] = ncPriceFormat($details[1]);
                $detail_array['pay_time'] = time();
                
                $model_refund = Model('refund_return');
                $refund = array();
                $detail_info = array();
                $consume_array = array();
                
                if ($_GET['refund'] == "vr") {
                    $model_refund = Model('vr_refund');//虚拟订单退款
                    $detail_info = $model_refund->getDetailInfo(array('batch_no'=> $batch_no));//退款详细
                    $refund_id = $detail_info['refund_id'];
                    $refund = $model_refund->getRefundInfo(array('refund_id'=> $refund_id));
                    $consume_array['consume_remark'] = '支付宝在线退款成功（到账有延迟），虚拟退款单号：'.$refund['refund_sn'];
                } else {
                    $detail_info = $model_refund->getDetailInfo(array('batch_no'=> $batch_no));//退款详细
                    $refund_id = $detail_info['refund_id'];
                    $refund = $model_refund->getRefundReturnInfo(array('refund_id'=> $refund_id));
                    $consume_array['consume_remark'] = '支付宝在线退款成功（到账有延迟），退款退货单号：'.$refund['refund_sn'];
                }
                $model_refund->editDetail(array('batch_no'=> $batch_no), $detail_array);
                $result = "success";
                
                $consume_array['member_id'] = $refund['buyer_id'];
                $consume_array['member_name'] = $refund['buyer_name'];
                $consume_array['consume_amount'] = $detail_array['pay_amount'];
                $consume_array['consume_time'] = time();
                
                if ($detail_info['pay_time'] == 0) {
                    QueueClient::push('addConsume', $consume_array);
                }

                $refund['admin_state_balance'] = 3;
                $user = unserialize(decrypt(cookie('sys_key'),MD5_KEY));
                if ($_GET['refund'] == "vr") {
                    $refund['admin_time'] = time();
                    $refund['admin_state'] = '2';
                    $refund['admin_message'] = "管理员处理支付宝在线退款";
                }
                $state = $model_refund->editOrderRefund($refund,$user['name']);
                if ($state) {
                    if ($_GET['refund'] == "vr") {

                    } else {
                        $refund_array = array();
                        $refund_array['admin_time'] = time();
                        $refund_array['refund_state'] = '3';//状态:1为处理中,2为待管理员处理,3为已完成
                        $refund_array['admin_message'] = "管理员处理支付宝在线退款";
                        $model_refund->editRefundReturn(array('refund_id' => $refund_id), $refund_array);
                    }

                    // 发送买家消息
                    $param = array();
                    $param['code'] = 'refund_return_notice';
                    $param['member_id'] = $refund['buyer_id'];
                    $param['param'] = array(
                        'refund_url' => urlShop('member_refund', 'view', array('refund_id' => $refund['refund_id'])),
                        'refund_sn' => $refund['refund_sn']
                    );
                    $refund['msg'] = '管理员已处理退款，请查收';//状态描述
                    $param['param']['mp_array'] = Logic('wx_api')->getTemplateData($param['code'],$refund);
                    RealTimePush('sendMemberMsg', $param);

                    //退款ERP订单
                    define('SCRIPT_ROOT',  BASE_DATA_PATH.'/api/ERP');
                    require_once SCRIPT_ROOT.'/order/'.'order.php';
                    require_once SCRIPT_ROOT.'/base/'.'member.php';
                    if ($_GET['refund'] == "vr") {
                        $vr_logic=Logic('erp_order');
                        $vr_logic->syncOrderRefund($refund);
                    } else {
                        $vr_logic=Logic('erp_realorder');
                        $vr_logic->syncOrderRefund($refund);
                        //更新管家婆中订单退款状态
                        Logic('gjp_qqd')->updateOrderStatus($refund['order_sn'],4,3);
                        //生成管家婆售后订单
                        Logic('gjp_qqd')->afterorderToGjp($refund_id);
                    }

                    //$this->log('退款确认，退款编号'.$refund['refund_sn']);
                    $detail_array['pd_amount'] = 0;
                    $detail_array['refund_state'] = 2;
                    $model_refund->editDetail(array('refund_id'=> $refund_id), $detail_array);
                }
            }
        }
        echo $result;exit;
    }
}
