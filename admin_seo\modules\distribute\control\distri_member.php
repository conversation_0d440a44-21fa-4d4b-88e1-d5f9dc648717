<?php
/**
 * 分销-分销商管理
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Box\Spout\Common\Type;
use Box\Spout\Writer\Style\StyleBuilder;
use Box\Spout\Writer\WriterFactory;
use Shopnc\Tpl;
use Upet\Models\Shr\ShrTStaffInfo;

defined('InShopNC') or exit('Access Invalid!');

class distri_memberControl extends SystemControl
{
    private $_links = array(
        array('url' => 'act=distri_member&op=member', 'text' => '管理'),
        array('url' => 'act=distri_member&op=auth_up', 'text' => '认证申请'),
        array('url' => 'act=distri_member&op=member_setting', 'text' => '分销员规则'),
        array('url' => 'act=distri_member&op=serviceApply', 'text' => '门店前台报备申请'),
    );
    const EXPORT_SIZE = 10000;

    function __construct()
    {
        parent::__construct();
    }

    public function indexOp()
    {
        $this->memberOp();
    }

    public function memberOp()
    {
        //输出子菜单
        Tpl::output('top_link', $this->sublink($this->_links, 'member'));
        Tpl::output('mem_stat', 2);
        $this->showTips();
        Tpl::showpage('member.index');
    }

    /**
     * Notes:操作提示
     * User: rocky
     * DateTime: 2021/12/22 9:39
     */
    public function showTips(){
        $tips = '<li>清退：会导致粉丝清空，适用于离职且不继续做分销工作的人，清退后是普通会员</li>';
        $tips .= '<li>解绑：会导致内部分销员解除和门店的绑定关系，适用于内部分销员所在旧门店的主体注销，或门店更改财务编码</li>';
        $tips .= '<li>解绑后的内部分销员需要在阿闻爱省钱选择新门店进行重新认证，经审核通过，方可完成和新门店的绑定</li>';
        $tips .= '<li>未结佣金：指已申请提现，但还未支付的金额</li>';
        if ($_GET['op'] == 'member' || $_GET['op'] == 'auth_up' || $_GET['op'] == 'index') {
            $tips .= '<li style="color: red">匹配SHR:指手机号是否和SHR员工系统在职员工手机号一致，注意，这里只匹配了手机号，没有匹配姓名</li>';
        }
        Tpl::output('top_tips', $tips);
    }

    /**
     * 认证申请
     */
    public function auth_upOp()
    {
        $this->showTips();
        //输出子菜单
        Tpl::output('top_link', $this->sublink($this->_links, 'auth_up'));
        Tpl::output('mem_stat', 1);
        Tpl::showpage('member.index');
    }

    /**
     * 保存分销设置
     */
    public function manage_saveOp(){
        $model_setting = Model('setting');
        $update_array = array();
        if($_POST['distri_user']==0){
            $update_array['distri_user_time'] = 0;
        }else{
            $update_array['distri_user_time'] = intval($_POST['distri_user_time']);
        }
        $result = $model_setting->updateSetting($update_array);
        if ($result === true){
            showMessage(Language::get('nc_common_save_succ'));
        }else {
            showMessage(Language::get('nc_common_save_fail'));
        }
    }

    /**
     * 认证申请
     */
    public function member_settingOp()
    {
        $model_setting = Model('setting');
        $setting_info = $model_setting->getRowSetting('distri_user_time');

        Tpl::output('setting_info',$setting_info);
        //输出子菜单
        Tpl::output('top_link', $this->sublink($this->_links, 'member_setting'));
        Tpl::output('mem_stat', 1);
        $this->showTips();
        Tpl::showpage('distri_member.setting');
    }

    /**
     * 认证详情
     */
    public function member_infoOp()
    {
        $member_id = intval($_REQUEST['member_id']);
        if ($member_id <= 0) {
            showMessage('会员不存在', 'index.php?act=distri_member');
        }
        $member_info = Model()->table('member,chain_brand')->field('member.*,chain_brand.brand_name')
            ->join('left')->on('member.distri_brandid=chain_brand.chain_brand_id')->where(['member.member_id'=>$member_id])->find();

        //可提现金额
        $available_trad = $member_info['trad_amount'];

        //冻结金额
        $freeze_trad = floatval($member_info['freeze_trad']);
        if($member_info['distri_state'] == 2){
            if($member_info['trad_amount'] >= C('distribute_bill_limit')){
                $freeze_trad += C('distribute_bill_limit');
                $available_trad -= C('distribute_bill_limit');
            }else{
                $freeze_trad += $member_info['trad_amount'];
                $available_trad = 0;
            }
        }

        $member_info['available_distri_trad'] = $available_trad;
        $member_info['freeze_distri_trad'] = $freeze_trad;
        $chain_info = Model('chain')->getChainInfo(array('chain_id'=>$member_info['distri_chainid']),"chain_name");
        $member_info['chain_name'] = $chain_info['chain_name'];

        Tpl::output('member_info', $member_info);
        $sex_array = $this->get_sex();
        Tpl::output('sex_array', $sex_array);
        Tpl::showpage('member.info');
    }

    /**
     * 会员认证
     */
    public function authOp()
    {
        if (isset($_GET['type'])){
            //门店报备申请跳转
            $url = 'index.php?act=distri_member&op=serviceApply';
            $url2 = 'index.php?act=distri_member&op=member_service_info';
        }else{
            //分销商管理认证跳转
            $url = 'index.php?act=distri_member';
            $url2 = 'index.php?act=distri_member&op=member_info';
        }
        if (!empty($_POST)) {
            $param = array();
            $member_model = Model('member');
            $param['auth_message'] = trim($_POST['joinin_message']);
            $param['distri_state'] = $this->_get_stat($_POST['verify_type']);
            $param['distri_code'] = getUniqueCode(uniqid());
            $member_id = intval($_POST['member_id']);
            $member_info = $member_model->getMemberInfo(['member_id'=>$member_id],'member_mobile,distri_formId,weixin_mini_openidasq,distri_chainid,distri_state');
            if (!$member_info){
                showMessage('用户信息不存在', 'index.php?act=distri_member');
            }
            if ($member_info['distri_state'] != 1){
                showMessage('审核状态异常，请刷新页面', 'index.php?act=distri_member');
            }
            if($_POST['verify_type'] == 'pass'){
                $param['distri_handle_time'] = time();
                $param['distri_show'] = 1;
            }
            //如果申请的是外部分销员
            $member_model->beginTransaction();
            $dis_member_model = Model('dis_member_fans');
            $stat = $member_model->editMember(array('member_id' => $member_id), $param);
            if ($stat) {
                //更新门店人员审核状态
                $user = [];
                $user['auth_desc'] = isset($_POST['auth_desc'])?trim($_POST['auth_desc']):'';
                if($param['distri_show'] == 1){
                    //门店报备申请审核
                    if($member_info['distri_chainid'] > 0) {
                        $user['isjudge'] = $_POST['verify_type'] == 'pass' ? 1 : 2;
                    }else{
                        $dis_member_model->addDisMemberOutside($member_id);
                    }
                }
                if ($user){
                    $res = Model('chain_user')->updateChainUser(['user_mobile' => $member_info['member_mobile']], $user);
                    if (!$res){
                        showMessage('认证处理人员信息失败', $url2.'&member_id=' . $member_id);
                        $member_model->rollback();
                    }
                }
                $member_model->commit();
                showMessage('认证处理成功', $url);
            } else {
                $member_model->rollback();
                showMessage('认证处理失败', $url2.'&member_id=' . $member_id);
            }
        } else {
            showMessage('非法请求', $url);
        }
    }

    /**
     * 会员批量认证
     */
    public function batch_authOp(){
        if (chksubmit()) {
            $member_model = Model('member');
            $dis_member_model = Model('dis_member_fans');
            $condition = array();
            $condition['distri_state'] = 1;
            if ($_POST['member_id'] != '') {
                $id_array = explode(',', $_POST['member_id']);
                $condition['member_id'] = array('in', $id_array);
            }
            $member_list =  $member_model->getMemberList($condition,'distri_chainid,member_id,member_mobile,distri_formId,weixin_mini_openidasq');
            if(!empty($member_list)){
                $param = array();
                $param['auth_message'] = trim($_POST['joinin_message']);
                $param['distri_state'] = $_POST['verify_type'] == 1 ? 2 : 3;
                foreach ($member_list as $member_info){
                    $param['distri_code'] = getUniqueCode(uniqid());
                    if($_POST['verify_type'] == 1){
                        $param['distri_handle_time'] = time();
                        $param['distri_show'] = 1;
                        //线上无短信发送机制，暂时屏蔽
//                        if (intval($param['distri_chainid']) > 0) {
//                            $content = '【阿闻爱省钱】您已通过审核，请至分店后台绑定对应注册手机号，绑定成功后即可使用。';
//                        } else {
//                            $content = '【阿闻爱省钱】您的代理人资质已审核通过，来阿闻爱省钱小程序开启您的“爱宠健康大使”生涯。稍后将通过客服电话为您提供咨询指引服务，请注意接听。';
//                        }
//                       $member_model->sendSmsMobile($member_info['member_mobile'], $content);

                    }
                    $member_model->editMember(array('member_id' => $member_info['member_id']), $param);

                    //如果申请的是外部分销员 lihaobin
                    if(!$member_info['distri_chainid']){
                        $dis_member_model->addDisMemberOutside($member_info['member_id']);
                    }else{
                        //更新门店人员审核状态
                        $type = 2;
                        if ($param['distri_state']==2) {
                            $type = 1;
                        }
                      Model('chain_user')->updateChainUser(['user_mobile' => $member_info['member_mobile']], ['isjudge' => $type]);
                    }
                }
            }else {
                showDialog(Language::get('nc_common_save_fail'),'','error','$("#flexigrid").flexReload();CUR_DIALOG.close();');
            }
            showDialog(L('nc_common_op_succ'), '', 'succ', '$("#flexigrid").flexReload();CUR_DIALOG.close()');
        }
        Tpl::output('member_id', $_GET['id']);
        Tpl::showpage('distri_member.batch_auth', 'null_layout');
    }

    /**
     * 清退分销商
     */
    public function member_cancleOp()
    {
        $member_id = intval($_GET['member_id']);
        $data = array();
        $data['state'] = false;
        if ($member_id <= 0) {
            $data['msg'] = '参数错误';
            exit(json_encode($data));
        }
        $member_model = Model('member');
        $param = array();
        $param['distri_state'] = 4;
        $param['distri_code'] = '';
        $param['quit_time'] = time();
        $param['distri_chainid'] = 0;
        $param['distri_quit_times'] = array('exp','distri_quit_times+1');
        $condition = array();
        $condition['member_id'] = $member_id;
        $member_info = $member_model->getMemberInfo($condition);
        $stat = $member_model->editMember($condition, $param);
        if ($stat) {
            $data['state'] = true;

            $dis_goods_model = Model('dis_goods');
            $dis_goods_model->delDistriGoods($condition);

            //判断是否有绑定客服关系
            verifyBindService($member_info,2);

            $distribue_logic = Logic('distribute');
            //分销人清退操作
            $distribue_logic->disMemberClear($member_info);

            exit(json_encode($data));
        } else {
            $data['msg'] = '清退失败';
            exit(json_encode($data));
        }
    }

    /**
     * 解绑分销商
     */
    public function member_unbindOp()
    {
        $member_id = intval($_GET['member_id']);
        $data = array();
        $data['state'] = false;
        if ($member_id <= 0) {
            $data['msg'] = '参数错误';
            exit(json_encode($data));
        }
        $member_model = Model('member');
        $param = array();
        $param['distri_state'] = 0;
        $param['distri_code'] = '';
        $param['quit_time'] = time();
        $param['distri_chainid'] = 0;
        $param['distri_quit_times'] = array('exp','distri_quit_times+1');
        $condition = array();
        $condition['member_id'] = $member_id;
        $member_info = $member_model->getMemberInfo($condition);
        $stat = $member_model->editMember($condition, $param);
        if ($stat) {
            $data['state'] = true;
            //删除企业微信门店登录手机记录
            Model('chain_member')->where(['mobile'=>$member_info['member_mobile']])->delete();
            //删除员工记录
            Model('chain_user')->where(['user_mobile'=>$member_info['member_mobile']])->delete();
            //日志
            $admin = unserialize(decrypt(cookie('sys_key'),MD5_KEY));
            $adddata['content']    = '分销员解绑:'.$member_info['member_mobile'];
            $adddata['admin_name'] = $admin['name'];
            $adddata['createtime'] = TIMESTAMP;
            $adddata['admin_id']   = $admin['id'];
            $adddata['ip']         = getIp();
            $adddata['url']        = $_REQUEST['act'].'&'.$_REQUEST['op'];
            Model('admin_log')->insert($adddata);
            //判断是否有绑定客服关系
            verifyBindService($member_info);

            exit(json_encode($data));
        } else {
            $data['msg'] = '解绑失败';
            exit(json_encode($data));
        }
    }
    /**
     * 分销员查看
     */
    public function show_memberOp()
    {
        $member_id = intval($_GET['member_id']);
        if($member_id <= 0){
            showMessage('参数错误','index.php?act=distri_member&op=show_member','html','error');
        }
        $this->_links[] = array('url' => 'act=distri_member&op=show_member', 'text' => '分销订单');
        Tpl::output('top_link', $this->sublink($this->_links, 'show_member'));
        Tpl::output('member_id',$member_id);
        Tpl::showpage('member.show');
    }

    /**
     * 性别
     * @return multitype:string
     */
    private function get_sex() {
        $array = array();
        $array[1] = '男';
        $array[2] = '女';
        $array[3] = '保密';
        return $array;
    }

    /**
     * 获取审核状态值
     * @param $param
     * @return int
     */
    private function _get_stat($param)
    {
        $stat = 1;
        if ($param == 'pass') {
            $stat = 2;
        } elseif ($param == 'fail') {
            $stat = 3;
        }
        return $stat;
    }

    /**
     * 输出XML数据
     */
    public function get_xmlOp()
    {
        $model_member = Model('member');
        $condition = array();
        if ($_POST['query'] != '') {
            if ($_POST['qtype'] =='chain_name'){
                $chain_arr = Model('chain')->getChainList(['chain_name'=>['like',"%{$_POST['query']}%"]],'chain_id');
                $chain_ids = array_column($chain_arr,'chain_id');
                $condition['member.distri_chainid'] = array('in',$chain_ids);
            }else{
                $condition['member.'.$_POST['qtype']] = $_POST['query'];
            }
        }
        $order = '';
        $param = array('member_id', 'member_name', 'member_avatar', 'member_email', 'member_mobile', 'member_sex', 'member_truename', 'member_time', 'member_login_time', 'member_login_ip', 'trad_amount', 'distri_state', 'freeze_trad','distri_time');
        if (in_array($_POST['sortname'], $param) && in_array($_POST['sortorder'], array('asc', 'desc'))) {
            $order = 'member.'.$_POST['sortname'] . ' ' . $_POST['sortorder'];
        }
        $page = $_POST['rp'];
        $distri_stat = 1;
        if ($_REQUEST['mem_state'] == 2) {
            $condition['member.distri_state'] = array('in',array(2,4,5));
            $distri_stat = $_REQUEST['mem_state'];
        } else {
            $condition['member.distri_state'] = array('gt', 0);
        }
        if(isset($_REQUEST['distribute_state'])){
            if($_REQUEST['distribute_state'] == 1){
                $condition['member.distri_chainid'] = array('gt',0);
            }elseif($_REQUEST['distribute_state'] == 2){
                $condition['member.distri_chainid'] = array('eq',0);
            }
        }
        if(isset($_REQUEST['shr_state'])){
            if($_REQUEST['shr_state'] == 0){
                $condition['chain_user.shr_state'] = 0;
            }elseif($_REQUEST['shr_state'] == 1){
                $condition['chain_user.shr_state'] = 1;
            }
        }
        $on = 'member.member_mobile = chain_user.user_mobile and chain_user.user_mobile !="",member.distri_chainid = chain.chain_id,
        member.member_id = chain_bind.member_id';
        $field = 'member.*,chain.chain_name,chain.account_id,chain_bind.id as bid,chain_user.store_phone_state,
        chain_user.shr_state,chain_user.shr_staff_no';

        $member_list_tmp = Model()->table('member,chain_user,chain,chain_bind')->field($field)->join('left,left,left')
            ->on($on)->where($condition)->page($page)->group('member.member_id')->order($order)->select();
        $member_list = array();
        foreach ($member_list_tmp as $value) {
            $member_list[$value['member_id']] = $value;
        }

        $this->getMemberExtension($member_list);

        $data = array();
        $data['now_page'] = $model_member->shownowpage();
        $data['total_num'] = $model_member->gettotalnum();
        foreach ($member_list as $value) {
            $param = array();
            $operation = '';
            switch ($distri_stat) {
                case 1:
                    if ($value['distri_state'] == '1') {
                        $operation .= "<a class='btn orange' href=\"index.php?act=distri_member&op=member_info&member_id=" . $value['member_id'] . "\"><i class=\"fa fa-check-circle-o\"></i>审核</a>";
                    }
                    $operation .= "<a class='btn green' href='index.php?act=distri_member&op=member_info&member_id=" . $value['member_id'] . "'><i class='fa fa-list-alt'></i>查看</a>";
                    break;
                case 2:
                    if ($value['distri_state'] == 2) {
                        $operation .= "<a class='btn red' href='javascript:void(0);' onclick=\"fg_unbind('" . $value['member_id'] . "')\"><i class='fa fa-ban'></i>解绑</a>";
                        $operation .= "<a class='btn red' href='javascript:void(0);' onclick=\"fg_del('" . $value['member_id'] . "')\"><i class='fa fa-ban'></i>清退</a>";
                    }
                    $operation .= "<a class='btn green' href='index.php?act=distri_member&op=show_member&member_id=" . $value['member_id'] . "'><i class='fa fa-list-alt'></i>查看</a>";
                    break;
            }

            $param['operation'] = $operation;
            $param['member_id'] = $value['member_id'];
            $param['member_name'] = "<img src=" . getMemberAvatarForID($value['member_id']) . " class='user-avatar' onMouseOut='toolTip()' onMouseOver='toolTip(\"<img src=" . getMemberAvatarForID($value['member_id']) . ">\")'>" . $value['member_name'];
            $param['member_mobile'] = hideStr($value['member_mobile']);
            $param['shr_state'] = $value['shr_state']?'是':'否';
            if ($value['shr_state'] == 0 && $value['store_phone_state'] == 0){
                $param['shr_state'] .= " <a style='background:#3b639f;color:white;' class='btn blue' href='javascript:void(0);' onclick=\"update_staff('" . $value['member_mobile'] . "')\"><i class='fa'></i>同步SHR</a>";
            }
            $param['is_bind'] = '否';
            if ($value['bid'] > 0){
                $param['is_bind'] = '是';
            }
            if ($distri_stat == 1) {
                $param['distri_stat'] = str_replace(array('1', '2', '3', '4', '5'), array('待审核', '已通过', '未通过', '清退', '退出'), $value['distri_state']);
                $param['distri_time'] = $value['distri_time'] ? date('Y-m-d', $value['distri_time']) : '';
                $param['distri_handle_time'] = ($value['distri_handle_time'] && $value['distri_state'] == 2) ? date('Y-m-d', $value['distri_handle_time']) : '';
            } else {
                $param['order_count'] = $value['order_count'] ? : 0;
                $param['had_pay_amount'] = ncPriceFormat($value['had_pay_amount']);
                $param['unpay_amount'] = ncPriceFormat($value['unpay_amount']);

                $param['success_amount'] = ncPriceFormat($value['success_amount']);//提现成功
                $param['apply_amount'] = ncPriceFormat($value['un_amount']);//提现申请 未打款的总金额
                $param['wait_amount'] = ncPriceFormat($value['trad_amount']);//待提现 即 可提现的金额
                $param['distri_amount'] = ncPriceFormat($value['had_pay_amount'] + $value['unpay_amount']);
            }
            $param['distri_chain_name'] = $value['chain_name']?:'外部代理人或其他';
            if ($value['distri_chainid'] > 1){
                $param['account_id'] = $value['account_id'];
            }
            $data['list'][$value['member_id']] = $param;
        }
        echo Tpl::flexigridXML($data);
        exit();
    }

    public function get_member_xmlOp(){
        $member_id = intval($_GET['member_id']);
        $condition = array();
        $condition['dis_member_id'] = $member_id;
        $model = Model('dis_order');
        $order_model = Model('order');
        $vr_order_model = Model('vr_order');
        $page = $_POST['rp'];
        $member_list = $model->getDisPayOrderList($condition, 'order_sn,order_id,dis_pay_amount,dis_pay_time,log_state,is_virtual', $page);
        $data = array();
        $data['now_page'] = $model->shownowpage();
        $data['total_num'] = $model->gettotalnum();
        foreach ($member_list as $value) {
            $param = array();
            if($value['is_virtual']==1){
                $vr_order_info = $vr_order_model->getOrderInfo(['order_id'=>$value['order_id']],'order_sn,order_amount,order_state,add_time');
                $param['order_sn'] = $vr_order_info['order_sn'];
                $param['add_time'] = date('Y-m-d', (int)$vr_order_info['add_time']);
                $param['order_amount'] = ncPriceFormat($vr_order_info['order_amount']);
                $param['order_state'] = str_replace(array(10,20,40,0),array('待付款','待使用','已完成','已取消'),$vr_order_info['order_state']);
            }else{
                $order_info = $order_model->getOrderInfo(['order_id'=>$value['order_id']],['order_goods'],'order_sn,order_amount,order_state,add_time');
                $param['order_sn'] = $order_info['order_sn'];
                $param['add_time'] = date('Y-m-d', (int)$order_info['add_time']);
                $param['order_amount'] = ncPriceFormat($order_info['order_amount']);
                $param['order_state'] = str_replace(array(10,20,30,40,0),array('待付款','待发货','已发货','已完成','已取消'),$order_info['order_state']);
            }
            $param['dis_pay_amount'] = ncPriceFormat($value['dis_pay_amount']);
            $param['dis_pay_time'] = $value['dis_pay_time']?date('Y-m-d', $value['dis_pay_time']):'';
            $param['log_state'] = str_replace(array(1,0),array('已结算','未结算'),intval($value['log_state']));
            $data['list'][$value['order_id']] = $param;

        }

        echo Tpl::flexigridXML($data);
        exit();

    }

    private function getMemberExtension(& $member_list)
    {
        if (empty($member_list)) return;
        $member_ids = array_keys($member_list);

        $model_dis_pay = Model('dis_bill');
        // 订单总数
        $condition = array();
        $condition['dis_member_id'] = array('in', $member_ids);
        $order_count = $model_dis_pay->getDistriBillList($condition, 'count(*) as order_count,dis_member_id', '', 'log_id desc', false, 'dis_member_id');
        foreach ($order_count as $value) {
            $member_list[$value['dis_member_id']]['order_count'] = $value['order_count'];
        }
        //已提现金额
        $trad_list = Model('dis_trad')->getDistriTradCashSum(['tradc_member_id'=>['in',$member_ids],'tradc_payment_state'=>['in',[0,1]]]);
        // $trad_list tradc_member_id时，生成的是二维数组，所以需要循环
        $trad_list_arr = [];
        foreach ($trad_list as $value) {
            $trad_list_arr[$value['tradc_member_id']][] = $value;
        }


        //已结算跟未结算
        $field = 'sum(dis_pay_amount) as dis_pay_amount,dis_member_id,log_state';
        $condition['log_state'] = array('in',array(0,1));
        $order_amount = $model_dis_pay->getDistriBillList($condition, $field, '', 'log_id desc', false, 'dis_member_id,log_state');
        foreach ($order_amount as $value) {
            if($value['log_state'] == 1){
                $member_list[$value['dis_member_id']]['had_pay_amount'] = $value['dis_pay_amount'];
            }else{
                $member_list[$value['dis_member_id']]['unpay_amount'] = $value['dis_pay_amount'];
            }
            if (isset($trad_list_arr[$value['dis_member_id']])){
                foreach ($trad_list_arr[$value['dis_member_id']] as $trad){
                    if($trad['tradc_payment_state'] == 0){
                        $member_list[$value['dis_member_id']]['un_amount'] = $trad['total_amount'];
                    }elseif($trad['tradc_payment_state'] == 1){
                        $member_list[$value['dis_member_id']]['success_amount'] = $trad['total_amount'];
                    }
                }
            }
        }
    }

    /**
     * csv导出
     */
    public function export_csvOp()
    {
        $model_member = Model('member');
        $condition = array();
        $where = [];
        $limit = false;
        if ($_GET['id'] != '') {
            $id_array = explode(',', $_GET['id']);
            $where['member.member_id'] = $condition['member_id'] = array('in', $id_array);
        }
        if ($_GET['query'] != '') {
            if ($_GET['qtype'] =='chain_name'){
                $chain_arr = Model('chain')->getChainList(['chain_name'=>['like',"%{$_GET['query']}%"]],'chain_id');
                $chain_ids = array_column($chain_arr,'chain_id');
                $where['member.distri_chainid'] = array('in',$chain_ids);
            }else{
                $where['member.'.$_GET['qtype']] = $_GET['query'];
            }
        }
        $order = '';
        $param = array('member_id', 'member_name', 'member_avatar', 'member_email', 'member_mobile', 'member_sex', 'member_truename', 'member_time', 'member_login_time', 'member_login_ip', 'trad_amount', 'distri_state', 'freeze_trad','distri_time');
        if (in_array($_GET['sortname'], $param) && in_array($_GET['sortorder'], array('asc', 'desc'))) {
            $order = 'member.'.$_GET['sortname'] . ' ' . $_GET['sortorder'];
        }
        $distri_stat = 1;
        if ($_REQUEST['mem_state'] == 2) {
            $where['member.distri_state'] =$condition['distri_state'] = array('in',array(2,4,5));
            $distri_stat = $_REQUEST['mem_state'];
        } else {
            $where['member.distri_state'] =$condition['distri_state'] = array('gt', 0);
        }

        if (!is_numeric($_GET['curpage'])) {
            $count = $model_member->getMemberCount($condition);
            if ($count > self::EXPORT_SIZE) {   //显示下载链接
                $array = array();
                $page = ceil($count / self::EXPORT_SIZE);
                for ($i = 1; $i <= $page; $i++) {
                    $limit1 = ($i - 1) * self::EXPORT_SIZE + 1;
                    $limit2 = $i * self::EXPORT_SIZE > $count ? $count : $i * self::EXPORT_SIZE;
                    $array[$i] = $limit1 . ' ~ ' . $limit2;
                }
                Tpl::output('list', $array);
                Tpl::output('murl', 'index.php?act=member&op=index');
                Tpl::showpage('export.excel');
                exit();
            }
        } else {
            $limit1 = ($_GET['curpage'] - 1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $limit = $limit1 . ',' . $limit2;
        }

        $field = 'member.member_id,member.member_name,member.member_mobile,member.distri_state,member.member_email,member.distri_time,member.distri_handle_time,
        member.dis_trad_money,member.freeze_trad,member.trad_amount,
        member.bill_user_name,member.distri_chainid,chain.chain_name,chain.account_id,chain_region.region_name,chain_bind.id as bid,chain_user.shr_state';
        $on = 'member.distri_chainid = chain.chain_id,chain.region_id = chain_region.region_id,member.member_id=chain_bind.member_id,member.member_mobile = chain_user.user_mobile and chain_user.user_mobile !=""';

        $member_list_tmp = Model()->table('member,chain,chain_region,chain_bind,chain_user')->field($field)->join('left,left,left,left')->on($on)->where($where)->order($order)->limit($limit)->select();

        $member_list = array();
        foreach ($member_list_tmp as $value) {
            if($value['distri_chainid']>0){
                $value['distri_type'] = '内部员工';
            }else{
                $value['distri_type'] = '外部代理';
            }
            $member_list[$value['member_id']] = $value;
        }

        $this->getMemberExtension($member_list);
        $this->createCsv($member_list,$distri_stat);
    }

    /**
     * 生成csv文件
     */
    private function createCsv($member_list,$distri_stat)
    {
        $data = array();
        foreach ($member_list as $value) {
            $param = array();
            $param['member_id'] = $value['member_id'];
            $param['member_name'] = $value['member_name'];
            if ($distri_stat == 1) {
                $param['distri_stat'] = str_replace(array('1', '2', '3', '4', '5'), array('待审核', '已通过', '未通过', '清退', '退出'), $value['distri_state']);
                $param['member_email'] = $value['member_email'];
                $param['member_mobile'] = hideStr($value['member_mobile']);
                $param['shr_state'] = $value['shr_state']?'是':'否';
                $param['is_bind'] = $value['bid'] > 0?'是':'否';
                $param['distri_time'] = $value['distri_time'] ? date('Y-m-d', $value['distri_time']) : '';
                $param['distri_handle_time'] = ($value['distri_handle_time'] && $value['distri_state'] == 2) ? date('Y-m-d', $value['distri_handle_time']) : '';
                $param['distri_type'] = $value['distri_type'];
                $param['chain_name'] = $value['chain_name']?:$this->initShopCode[$_SESSION['store_id']]['shop_name'];
                $param['region_name'] = $value['region_name'];
                $param['bill_user_name'] = $value['bill_user_name']?:'';
            } else {
                $param['member_email'] = $value['member_email'];
                $param['member_mobile'] = hideStr($value['member_mobile']);
                $param['shr_state'] = $value['shr_state']?'是':'否';
                $param['is_bind'] = $value['bid'] > 0?'是':'否';
                $param['order_count'] = $value['order_count'] ? $value['order_count'] : 0;
                $param['had_pay_amount'] = ncPriceFormat($value['had_pay_amount']);
                $param['unpay_amount'] = ncPriceFormat($value['unpay_amount']);
                $param['success_amount'] = ncPriceFormat($value['success_amount']);//提现成功
                $param['apply_amount'] = ncPriceFormat($value['un_amount']);//提现申请即未打款的总金额
                $param['wait_amount'] = ncPriceFormat($value['trad_amount']);//待提现 即 可提现的金额
                $param['distri_amount'] = ncPriceFormat($value['had_pay_amount'] + $value['unpay_amount']);
                $param['distri_type'] = $value['distri_type'];
                $param['chain_name'] = $value['chain_name']?:$this->initShopCode[$_SESSION['store_id']]['shop_name'];
                $param['account_id'] = $value['distri_chainid'] > 1?$value['account_id']:$this->initShopCode[$_SESSION['store_id']]['shop_code'];
                $param['region_name'] = $value['region_name'];
                $param['bill_user_name'] = $value['bill_user_name']?:'';
            }
            $data[$value['member_id']] = $param;
        }
        if($distri_stat == 1){
            $header = array('member_id' => '会员ID', 'member_name' => '会员名称', 'distri_stat' => '申请状态', 'member_email' => '会员邮箱',
                'member_mobile' => '会员手机','shr_state' => '匹配SHR','is_bind' => '是否绑定客服', 'distri_time' => '申请时间',
                'distri_handle_time' => '通过时间','distri_type'=>'人员归属','chain_name'=>'归属门店','region_name'=>'归属大区','bill_user_name'=>'持卡人');
        }else{
            $header = array('member_id' => '会员ID', 'member_name' => '会员名称', 'member_email' => '会员邮箱', 'member_mobile' => '会员手机',
                'shr_state' => '匹配SHR', 'is_bind' => '是否绑定客服','order_count' => '分销单数', 'had_pay_amount' => '已结佣金(元)', 'unpay_amount' => '未结佣金(元)',
                'success_amount' => '提现成功(元)', 'apply_amount' => '提现申请(元)', 'wait_amount' => '待提现(元)', 'distri_amount' => '分销佣金总额(元)',
                'distri_type'=>'人员归属','chain_name'=>'归属门店','account_id'=>'财务编码','region_name'=>'归属大区','bill_user_name'=>'持卡人');
        }

        \Shopnc\Lib::exporter()->output('member_list' . $_GET['curpage'] . '-' . date('Y-m-d'), $data, $header);
    }

    /**
     * Notes:门店前台报备申请模板
     * User: rocky
     * DateTime: 2023/3/28 11:42
     */
    public function serviceApplyOp()
    {
        Tpl::output('top_link', $this->sublink($this->_links, 'serviceApply'));
        $tips = '<li>门店公用手机号，不在shr员工系统中，但需要统计业绩，因此这部分手机号认证内部分销员之前，需要报备。</li>';
        $tips .= '<li>报备的手机号认证内部分销员，系统可自动审核通过，业绩归属取决于报备时提交的门店。</li>';
        Tpl::output('top_tips', $tips);
        Tpl::showpage('member.service_apply');
    }

    /**
     * Notes:门店前台报备申请列表
     * User: rocky
     * DateTime: 2023/3/29 19:58
     */
    public function get_service_listOp(){
        $page = $_POST['rp'];
        $order = '';
        $param = array('member_id', 'member_name', 'member_mobile', 'member_truename', 'member_time', 'distri_state','distri_time');
        if (in_array($_POST['sortname'], $param) && in_array($_POST['sortorder'], array('asc', 'desc'))) {
            $order = 'member.'.$_POST['sortname'] . ' ' . $_POST['sortorder'];
        }
        //查询条件
        $condition['chain_user.store_phone_state'] =1;
        if ($_POST['query'] != '') {
            if ($_POST['qtype'] =='member_id' || $_POST['qtype'] =='member_name' || $_POST['qtype'] =='member_mobile' || $_POST['qtype'] =='id_card'){
                $condition['member.'.$_POST['qtype']] = $_POST['query'];
            }else if ($_POST['qtype'] =='chain_name' || $_POST['qtype'] =='account_id'){
                $condition['chain.'.$_POST['qtype']] = $_POST['query'];
            }
        }
        $field ='member.member_id,member.member_identity,member.member_name,member.member_mobile,member.distri_brandid,member.distri_state,
        chain.chain_name,chain.account_id,chain_brand.brand_name';
        $on = 'member.member_mobile = chain_user.user_mobile and chain_user.user_mobile !="",member.distri_chainid = chain.chain_id,member.distri_brandid = chain_brand.chain_brand_id';
        if ($_GET['export_id']){
            if ($_GET['id'] != '') {
                $id_array = explode(',', $_GET['id']);
                $condition['member.member_id'] = $condition['member_id'] = array('in', $id_array);
            }
            $list = Model()->table("member,chain_user,chain,chain_brand")->join("left,left")
                ->on($on)->field($field)->where($condition)->order($order)->select();
        }else{
            $list = Model()->table("member,chain_user,chain,chain_brand")->join("left,left")
                ->on($on)->field($field)->where($condition)->order($order)
                ->page($page)->select();
        }
        $data = array();
        $data['now_page'] = Model()->shownowpage();
        $data['total_num'] = Model()->gettotalnum();
        $param = $xls =[];
        foreach ($list as $k =>$v){
            $operation = "<a class='btn green' href='index.php?act=distri_member&op=member_service_info&member_id=" . $v['member_id'] . "'><i class='fa fa-list-alt'></i>查看</a>";
            $param[$k]['operation'] = $operation;
            $xls[$k]['member_id']= $param[$k]['member_id'] = $v['member_id'];
            $xls[$k]['member_mobile']= $param[$k]['member_mobile'] = hideStr($v['member_mobile']);
            $xls[$k]['member_name']=$param[$k]['member_name'] = $v['member_name'];
            $xls[$k]['chain_name']=$param[$k]['chain_name'] = $v['chain_name'];
            $xls[$k]['brand_name']=$param[$k]['brand_name'] = $v['brand_name']?:'';
            $xls[$k]['account_id']=$param[$k]['account_id'] = $v['account_id'];
            //0未申请 1待审核 2已通过 3未通过 4清退 5退出 6 已撤销
            switch ($v['distri_state']){
                case 2:
                    $distri_state = '审核通过';
                    break;
                case 3:
                    $distri_state = '审核不通过';
                    break;
                case 6:
                    $distri_state = '已撤销';
                    break;
                default:
                    $distri_state = '待审核';
                    break;
            }
            $xls[$k]['distri_state'] = $param[$k]['distri_state'] = $distri_state;
            $xls[$k]['member_identity'] = $v['member_identity']?hideStr($v['member_identity'],3):'';
        }
        //导出数据
        if ($_GET['export_id']){
            $writer = WriterFactory::create(Type::XLSX);
            $writer->setDefaultRowStyle((new StyleBuilder())->setShouldWrapText(false)->build());
            $writer->openToBrowser('门店前台报备导出列表-'.$_GET['curpage'].'-'.date('Y-m-d-H',time()).'.xlsx');
            $writer->addRow(['会员id','手机号','会员名称','门店','品牌','财务编码','状态','身份证']);
            $writer->addRows($xls);
            $writer->close();
            $this->log('门店前台报备申请列表');
            exit();
        }
        $data['list'] = $param;
        echo Tpl::flexigridXML($data);
        exit();
    }

    /**
     * Notes:特殊分销员详情
     * User: rocky
     * DateTime: 2023/3/30 8:56
     */
    public function member_service_infoOp()
    {
        $member_id = intval($_REQUEST['member_id']);
        if ($member_id <= 0) {
            showMessage('会员不存在', 'index.php?act=distri_member');
        }
        $condition['chain_user.store_phone_state'] =1;
        $condition['member.member_id'] =$member_id;

        $field ='member.member_id,member.member_truename,member.member_time,member.member_sex,member.id_card_code,member.member_name,
        member.member_mobile_bind,member.member_mobile,member.distri_brandid,member.distri_state,member.member_email_bind,member.member_identity,
        member.member_login_time,member.auth_message,chain.chain_name,chain.account_id,chain_brand.brand_name,
        chain_user.auth_desc,chain_user.store_phone_state,chain_user.shr_state,chain_user.shr_staff_no';
        $on = 'member.member_mobile = chain_user.user_mobile and chain_user.user_mobile !="",member.distri_chainid = chain.chain_id,member.distri_brandid = chain_brand.chain_brand_id';

        $member_info = Model()->table("member,chain_user,chain,chain_brand")->join("left,left,left")->on($on)->field($field)->where($condition)->find();

        if ($member_info['distri_state'] != 1){
            $member_info['member_identity'] = hideStr($member_info['member_identity'],3);
        }

        Tpl::output('member_info', $member_info);
        $sex_array = $this->get_sex();
        Tpl::output('sex_array', $sex_array);
        Tpl::showpage('member.service_info');
    }

    /**
     * Notes:修改身份证
     * User: rocky
     * DateTime: 2023/3/30 10:05
     */
    public function edit_cardOp(){
        $id = $_POST['id'];
        $id_card = $_POST['id_card'];
        $res['status'] = 400;
        if (!$id  || !$id_card){
            $res['msg'] = '请求输入参数有误';
            echo json_encode($res);exit;
        }
        $match = "/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/";
        if (!preg_match($match, $id_card)) {
            $res['msg'] = '身份证格式错误!';
            echo json_encode($res);exit;
        }
        $result = Model("member")->where(['member_id'=>$id])->update(['member_identity'=>$id_card]);
        if (!$result){
            $res['msg'] = '修改失败!';
            echo json_encode($res);exit;
        }
        $res['status'] = 200;
        $res['msg'] = "修改成功";
        $res['id_card'] = hideStr($id_card,3);
        echo json_encode($res);exit;
    }

    /**
     * Notes:修改备注
     * User: rocky
     * DateTime: 2023/3/30 10:05
     */
    public function edit_descOp(){
        $mobile = $_POST['mobile'];
        $auth_desc = $_POST['auth_desc'];
        $res['status'] = 400;
        if (!$mobile  || !$auth_desc){
            $res['msg'] = '请求输入参数有误';
            echo json_encode($res);exit;
        }
        $result = Model("chain_user")->where(['user_mobile'=>$mobile])->update(['auth_desc'=>$auth_desc]);
        if (!$result){
            $res['msg'] = '修改失败!';
            echo json_encode($res);exit;
        }
        $res['status'] = 200;
        $res['msg'] = "修改成功";
        $res['auth_desc'] = $auth_desc;
        echo json_encode($res);exit;
    }

    public function update_staffOp(){
        $mobile = $_GET['mobile'];
        $res['status'] = 400;
        if (!$mobile){
            $res['msg'] = '请求输入参数有误';
            echo json_encode($res);exit;
        }
        $model_member = Model('member');
        $staff_info = ShrTStaffInfo::getInfo($mobile);
        if ($staff_info){
            if ($staff_info['status'] == 2) {
                $update['shr_state'] = 1;
                $update['shr_staff_no'] = $staff_info['shr_staff_no'];
                $result = Model("chain_user")->where(['user_mobile' => $mobile])->update($update);
                if (!$result) {
                    $res['msg'] = '修改失败!';
                    echo json_encode($res);
                    exit;
                }
                $res['status'] = 200;
                $res['msg'] = "更新成功";
                echo json_encode($res);
                exit;
            }else{
                $res['msg'] = '员工状态异常';
                echo json_encode($res);exit;
            }
        }else{
            $res['msg'] = '查询到大数据的更新的数据库没此员工记录';
            echo json_encode($res);exit;
        }

    }
}