<?php
/**
 * 虚拟订单退款
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;
use Upet\Integrates\Redis\RedisManager as Redis;
use Box\Spout\Common\Type;
use Box\Spout\Writer\Style\StyleBuilder;
use Box\Spout\Writer\WriterFactory;

defined('InShopNC') or exit('Access Invalid!');
class vr_refundControl extends SystemControl{
    const EXPORT_SIZE = 1000;
    private $links = array(
            array('url'=>'act=vr_refund&op=refund_manage','text'=>'待处理'),
            array('url'=>'act=vr_refund&op=refund_all','text'=>'所有记录'),
    );
    public function __construct(){
        parent::__construct();
        Language::read('refund');
        $model_vr_refund = Model('vr_refund');
        $model_vr_refund->getRefundStateArray();
    }

    public function indexOp() {
        $this->refund_manageOp();
    }

    /**
     * 待处理列表
     */
    public function refund_manageOp() {
        $_GET['waiting'] = 1;
        $this->refund_allOp();
    }

    /**
     * 所有记录
     */
    public function refund_allOp() {
        Tpl::output('top_link',$this->sublink($this->links,$_GET['waiting'] ? 'refund_manage' : 'refund_all'));
        Tpl::showpage('vr_refund_all.list');
    }

    /**
     * 所有记录
     */
    public function get_all_xmlOp() {
        /**
         * @var $model_vr_refund vr_refundModel
         */
        $model_vr_refund = Model('vr_refund');
        $condition = array();

        list($condition,$order) = $this->_get_condition($condition);

        $refund_list = $model_vr_refund->getRefundJoinOrdersList($condition, $_POST['rp'] > 0 ? $_POST['rp'] : 15, $order);

        $data = array();
        $data['now_page'] = $model_vr_refund->shownowpage();
        $data['total_num'] = $model_vr_refund->gettotalnum();
        $admin_array = $model_vr_refund->getRefundStateArray('admin');
        foreach ($refund_list as $k => $refund_info) {
            $list = array();
            if ($_GET['waiting'] == 1 && $refund_info['admin_state'] == 1 && $refund_info['order_type'] != 17) {
                $list['operation'] = "<a class=\"btn orange\" href=\"index.php?act=vr_refund&op=edit&refund_id={$refund_info['refund_id']}\"><i class=\"fa fa-gavel\"></i>处理</a>";
            } else {
                $list['operation'] = "<a class=\"btn green\" href=\"index.php?act=vr_refund&op=view&refund_id={$refund_info['refund_id']}\"><i class=\"fa fa-list-alt\"></i>查看</a>";
            }
            $mark_label = $refund_info['mark_refunded'] == 1 ? "取消标记" : "标记";
            $list['operation'] .= "<a class=\"btn orange mark_refunded\" data-mark_refunded='{$refund_info['mark_refunded']}' data-id='{$refund_info['refund_id']}' href=\"javascript:;\"><i class=\"fa fa-tag\"></i>{$mark_label}</a>";

            $list['refund_sn'] = $refund_info['refund_sn'];
            $list['refund_amount'] = ncPriceFormat($refund_info['refund_amount']);
            $list['buyer_message'] = "<span title='{$refund_info['buyer_message']}'>{$refund_info['buyer_message']}</span>";
            $list['add_times'] = date('Y-m-d H:i:s',$refund_info['add_time']);
            $list['goods_name'] = "<a class='open' title='{$refund_info['goods_name']}' href='". urlShop('goods', 'index', array('goods_id' => $refund_info['goods_id'])) ."' target='blank'>{$refund_info['goods_name']}</a>";
            $list['admin_state'] = $admin_array[$refund_info['admin_state']];
            $list['admin_message'] = $refund_info['admin_message'];
            $list['admin_message'] = "<span title='{$refund_info['admin_message']}'>{$refund_info['admin_message']}</span>";
            $list['goods_id'] = !empty($refund_info['goods_id']) ? $refund_info['goods_id'] : '';
            $list['order_sn'] = $refund_info['order_sn'];
            $list['erp_order_sn'] = $refund_info['erp_order_sn'];
            $list['buyer_name'] = $refund_info['buyer_name'];
            $list['buyer_id'] = $refund_info['buyer_id'];
            $list['store_name'] = $refund_info['store_name'];
            $list['store_id'] = $refund_info['store_id'];
//            $list['code_sn'] = $refund_info['code_sn'];
            $data['list'][$refund_info['refund_id']] = $list;
        }
        exit(Tpl::flexigridXML($data));
    }

    /**
     * 业务标记退款状态
     */
    public function mark_refundedOp()
    {
        $refund_id = $_POST['refund_id'];
        $mark_refunded = intval($_POST['mark_refunded']);
        if (!in_array($mark_refunded, [0, 1])) {
            showMessage("参数错误");
        }
        /**
         * @var $model_vr_refund vr_refundModel
         */
        $model_vr_refund = Model('vr_refund');

        $condition = array('refund_id' => array("in", $refund_id));
        $updateData =array(
            'mark_refunded' => $mark_refunded,
        );
        $rs = $model_vr_refund->editRefund($condition, $updateData);
        $code = 200;
        $message = "成功";
        if ($rs === false) {
            $code = 400;
            $message = "失败";
        }
        echo json_encode(array("code" => $code, "message" => $message));
    }


    /**
     * 虚拟订单售后审核页 vip-2.0.1
     */
    public function editOp(){
        $model_vr_refund = Model('vr_refund');
        $condition = array();
        $condition['refund_id'] = intval($_GET['refund_id']);
        $refund = $model_vr_refund->getRefundInfo($condition);
        $memberInfo = Model('member')->getMemberInfo(array('member_id' => $refund['buyer_id']),'member_mobile');
        $member_mobile = $memberInfo['member_mobile'];
        $vr_order_model = Model('vr_order');
        $order_info = $vr_order_model->getOrderInfo(array('order_id' => $refund['order_id']),"payment_time");
        $payment_time=1546963200; //1月8号之前无需积分
        $erpoints = Logic('erp_points')->getUserPoints($member_mobile);
        $refund['erpoints'] = $erpoints;
        $refund['needpoints'] = $order_info['payment_time'] > $payment_time?floor($refund['refund_amount']):0;
        $order_id = $refund['order_id'];
        $model_vr_order = Model('vr_order');
        $order = $model_vr_order->getOrderInfo(array('order_id'=> $order_id));
        $order['pay_amount'] = $order['order_amount']-$order['rcb_amount']-$order['pd_amount'];//在线支付金额=订单总价格-充值卡支付金额-预存款支付金额
        Tpl::output('order',$order);
        $detail_array = $model_vr_refund->getDetailInfo($condition);
        if(empty($detail_array)) {
            $model_vr_refund->addDetail($refund,$order);
            $detail_array = $model_vr_refund->getDetailInfo($condition,"*",true);
        }
        Tpl::output('detail_array',$detail_array);

        if (chksubmit()) {
            $lock = Redis::lock('edit_shop_vr_refund:'.$refund['refund_id'])->setAutoRelease();
            if (!$lock->get()) {
                showMessage('处理中，请勿频繁操作...');
            }
            if ($refund['admin_state'] != '1') {//检查状态,防止页面刷新不及时造成数据错误
                showMessage(Language::get('nc_common_save_fail'));
            }
            if ($detail_array['pay_time'] > 0) {
                $refund['pay_amount'] = $detail_array['pay_amount'];//已完成在线退款金额
            }
            $refund['admin_time'] = time();
            $refund['admin_state'] = '2';
            if ($_POST['admin_state'] == '3') {
                $refund['admin_state'] = '3';
            }
            $refund['admin_message'] = $_POST['admin_message'];
            $refund['admin_state_virbalance'] = $_POST['admin_state_virbalance'];
            $refund['operationer'] = $this->admin_info['name'];
            $refund['mobile'] = $order['buyer_phone'];

            //退款ERP虚拟订单
            /** @var refundLogic $refundLogic*/
            $refundLogic = Logic('refund');
            $result = $refundLogic->syncBatchVrOrderRefund($refund, $order);
            if (!$result) {
                showMessage("操作申请失败，您稍后再申请~~");
            }

            unset($refund['operationer']);

            $state = $model_vr_refund->editOrderRefund($refund);

            if ($state) {
                // 发送买家消息
                $param = array();
                $param['code'] = 'refund_return_notice';
                $param['member_id'] = $refund['buyer_id'];
                $param['param'] = array(
                    'refund_url' => urlShop('member_vr_refund', 'view', array('refund_id' => $refund['refund_id'])),
                    'refund_sn' => $refund['refund_sn']
                );
                $refund['msg'] = '管理员已处理退款，请查收';//状态描述
                $param['param']['mp_array'] = Logic('wx_api')->getTemplateData($param['code'],$refund);
                RealTimePush('sendMemberMsg', $param);

                $this->log('虚拟订单退款审核，退款编号'.$refund['refund_sn']);
                showMessage(Language::get('nc_common_save_succ'),'index.php?act=vr_refund&op=refund_manage');
            } else {
                showMessage(Language::get('nc_common_save_fail'));
            }
        }
        //有赠品订单
        $model_gift_order = Model('gift_order');
        $gift_order_info = $model_gift_order->getOrderInfo(['order_id_from'=>$order_id],'order_sn,order_id,buyer_name,buyer_phone,order_amount,goods_id,goods_name,goods_price,goods_image,goods_num,order_state,safe_app_no,safe_no,goods_spec');
        if (is_array($gift_order_info) && !empty($gift_order_info)) {
            $refund['gift_order'] = $gift_order_info;
            $refund['gift_order']['safe_time'] = $gift_order_info['safe_time'] ? date('Y-m-d H:i:s',$gift_order_info['safe_time']) : "";
        }
        Tpl::output('refund',$refund);
        $code_array = explode(',', $refund['code_sn']);
        Tpl::output('code_array',$code_array);
        Tpl::showpage('vr_refund.edit');
    }

    /**
     * 查看页
     *
     */
    public function viewOp() {
        $model_vr_refund = Model('vr_refund');
        $condition = array();
        $condition['refund_id'] = intval($_GET['refund_id']);
        $refund = $model_vr_refund->getRefundInfo($condition);
        Tpl::output('refund',$refund);
        $code_array = explode(',', $refund['code_sn']);
        Tpl::output('code_array',$code_array);
        $detail_array = $model_vr_refund->getDetailInfo($condition);
        Tpl::output('detail_array',$detail_array);
        Tpl::showpage('vr_refund.view');
    }

    /**
     * 封装共有查询代码
     */
    private function _get_condition($condition) {
        if ($_REQUEST['query'] != '' && in_array($_REQUEST['qtype'],array('order_sn','erp_order_sn','store_name','buyer_name','goods_name','refund_sn'))) {
            if ($_REQUEST['qtype'] =='refund_sn'){
                $condition['vr_refund.'.$_REQUEST['qtype']] = array('like',"%{$_REQUEST['query']}%");
            }else{
                $condition['vr_order.'.$_REQUEST['qtype']] = array('like',"%{$_REQUEST['query']}%");
            }
        }
        if ($_GET['keyword'] != '' && in_array($_GET['keyword_type'],array('order_sn','erp_order_sn','store_name','buyer_name','goods_name','refund_sn'))) {
            if ($_GET['jq_query']) {
                if ($_REQUEST['keyword_type'] =='refund_sn'){
                    $condition['vr_refund.'.$_GET['keyword_type']] = $_GET['keyword'];
                }else{
                    $condition['vr_order.'.$_REQUEST['keyword_type']] = $_GET['keyword'];
                }
            } else {
                if ($_REQUEST['keyword_type'] =='refund_sn'){
                    $condition['vr_refund.'.$_REQUEST['keyword_type']] = array('like',"%{$_REQUEST['keyword']}%");
                }else{
                    $condition['vr_order.'.$_REQUEST['keyword_type']] = array('like',"%{$_REQUEST['keyword']}%");
                }
            }
        }
        if (!in_array($_GET['qtype_time'], array('add_time', 'admin_time', "payment_time"))) {
            $_GET['qtype_time'] = null;
        }
        $if_start_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_start_date']);
        $if_end_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_end_date']);
        $start_unixtime = $if_start_time ? strtotime($_GET['query_start_date']) : null;
        $end_unixtime = $if_end_time ? strtotime($_GET['query_end_date']): null;
        if ($_GET['qtype_time'] && ($start_unixtime || $end_unixtime)) {
            $condition['vr_refund.'.$_GET['qtype_time']] = array('time',array($start_unixtime,$end_unixtime));
        }
        if (floatval($_GET['query_start_amount']) > 0 && floatval($_GET['query_end_amount']) > 0) {
            $condition['vr_refund.'.'refund_amount'] = array('between',floatval($_GET['query_start_amount']).','.floatval($_GET['query_end_amount']));
        }
        if ($_GET['waiting'] == 1) {
            $condition['vr_refund.'.'admin_state'] = 1;
        }
        if (isset($_REQUEST['hide-mark-refunded']) && $_REQUEST['hide-mark-refunded']) {
            $condition['vr_refund.'.'mark_refunded'] = 0;
        }
        $sort_fields = array('buyer_name','store_name','goods_id','refund_id','seller_time','refund_amount','buyer_id','store_id');
        if ($_REQUEST['sortorder'] != '' && in_array($_REQUEST['sortname'],$sort_fields)) {
            $order = $_REQUEST['sortname'].' '.$_REQUEST['sortorder'];
        }
        if ((isset($_GET['order_from']) && $_GET['order_from'] > 0) || (isset($_GET['qtype_time']) && $_GET['qtype_time'] == 'payment_time')) {
            foreach ($condition as $k => $v) {
                unset($condition[$k]);
                if ($k == 'payment_time') { //买家支付时间
                    $condition['vr_order.' . $k] = $v;
                } else {
                    $condition['vr_refund.' . $k] = $v;
                }
            }
            // 销售渠道
            if ($_GET['order_from'] > 0) {
                if ($_GET['order_from'] == 99) { // 视频号
                    $condition['vr_order.is_live&orders.payment_code'] = [2, 'wx_jsapi', '_multi' => false];
                } else {
                    $condition['vr_order.order_from'] = $_GET['order_from'];
                }
            }
            if (!empty($order)) {
                $order = "vr_refund." . $order;
            }
        }
        $condition['vr_refund.store_id'] = $_SESSION['store_id'];
        return array($condition,$order);
    }

    /**
     * csv导出
     */
    public function export_step1Op() {
        /**
         * @var $model_refund vr_refundModel
         */
        $model_refund = Model('vr_refund');
        $condition = array();
        if (preg_match('/^[\d,]+$/', $_GET['refund_id'])) {
            $_GET['refund_id'] = explode(',',trim($_GET['refund_id'],','));
            $condition['refund_id'] = array('in',$_GET['refund_id']);
        }
        list($condition,$order) = $this->_get_condition($condition);

        if (!is_numeric($_GET['curpage'])){
            $count = $model_refund->getRefundCount($condition);
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $array = array();
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','javascript:history.back(-1)');
                Tpl::showpage('export.excel');
                exit();
            }
            $limit = false;
        } else {
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $limit = $limit1 .','. $limit2;
        }

        if ($model_refund->checkParamsUseVrOrderField()) {
            $refund_list = $model_refund->getRefundListAndCodeAndOrder($condition, '', $limit, 'vr_refund.*,vr_order_code.erp_order_id,vr_order_code.vr_code,vr_order_code.vr_usetime,vr_order_code.chain_name', $order);
        } else {
            $refund_list = $model_refund->getRefundListAndCode($condition, '', $limit, 'vr_refund.*,vr_order_code.erp_order_id,vr_order_code.vr_code,vr_order_code.vr_usetime,vr_order_code.chain_name', $order);
        }

        $this->createXls($refund_list);
    }

    /**
     * 生成csv文件
     */
    private function createXls($refund_list) {
        $writer = WriterFactory::create(Type::XLSX);
        //不自动换行
        $writer->setDefaultRowStyle((new StyleBuilder())->setShouldWrapText(false)->build());
        $writer->openToBrowser('vr_refund-'.$_GET['curpage'].'-'.date('Y-m-d-H',time()).'.xlsx');
        $writer->addRow(['退单编号','退款金额','申请原因','申请时间','涉及商品','平台处理','平台处理备注','商品ID','订单编号','买家',
            '买家ID','商家名称','商家ID','处理时间',  'ERP订单号','核销兑换码','核销时间','退款平台','退款门店']);
        $list = array();
        $admin_array =  Model('vr_refund')->getRefundStateArray('admin');
        foreach ((array)$refund_list as $k=>$refund_info){
            $list[$k]['refund_sn'] = $refund_info['refund_sn'];
            $list[$k]['refund_amount'] = ncPriceFormat($refund_info['refund_amount']);
            $list[$k]['buyer_message'] = $refund_info['buyer_message'];
            $list[$k]['add_times'] = date('Y-m-d H:i:s',$refund_info['add_time']);
            $list[$k]['goods_name'] = $refund_info['goods_name'];
            $list[$k]['admin_state'] = $admin_array[$refund_info['admin_state']];
            $list[$k]['admin_message'] = $refund_info['admin_message'];
            $list[$k]['goods_id'] = !empty($refund_info['goods_id']) ? $refund_info['goods_id'] : '';
            $list[$k]['order_sn'] = $refund_info['order_sn'];
            $list[$k]['buyer_name'] = $refund_info['buyer_name'];
            $list[$k]['buyer_id'] = $refund_info['buyer_id'];
            $list[$k]['store_name'] = $refund_info['store_name'];
            $list[$k]['store_id'] = $refund_info['store_id'];
            $list[$k]['erp_order_id'] = $refund_info['erp_order_id'];
            $list[$k]['vr_code'] = $refund_info['vr_code'];
            $list[$k]['admin_time'] = $refund_info['admin_time']?date('Y-m-d H:i:s',$refund_info['admin_time']):"-";
            $list[$k]['vr_usetime'] = $refund_info['vr_usetime']?date('Y-m-d H:i:s',$refund_info['vr_usetime']):"-";
            $list[$k]['refund_flag'] =$refund_info['erp_order_id']?"ERP退款":"电商退款";
            $list[$k]['refund_chain_name'] = $refund_info['chain_name']?$refund_info['chain_name']:"-";
        }
        $writer->addRows($list);
        $writer->close();
        $this->log('导出虚拟订单退款数据');
    }
}
