<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title"><a class="back" href="index.php?act=goods_tag&op=tags_list" title="返回商品标签列表"><i class="fa fa-arrow-circle-o-left"></i></a>
      <div class="subject">
        <h3><?php echo $lang['goods_tag_index_tag'];?> - <?php echo $lang['nc_new'];?></h3>
        <h5><?php echo $lang['goods_tag_index_tag_subhead'];?></h5>
      </div>
    </div>
  </div>
  <form id="goods_class_form" enctype="multipart/form-data" method="post">
    <input type="hidden" name="form_submit" value="ok" />
    <div class="ncap-form-default">
      <dl class="row">
        <dt class="tit">
          <label for="tag_name"><em>*</em><?php echo $lang['goods_tag_index_name'];?></label>
        </dt>
        <dd class="opt">
          <input type="text" value="" name="tag_name" id="tag_name" maxlength="20" class="input-txt">
          <span class="err"></span>
          <p class="notic"></p>
        </dd>
      </dl>

      <dl class="row">
        <dt class="tit">
          <label for="gc_parent_id"><?php echo $lang['goods_class_add_sup_class'];?></label>
        </dt>
        <dd class="opt">
            <input type="hidden" id="mansong_rule_count" name="rule_count">
            <ul id="mansong_rule_list" class="ncsc-mansong-rule-list">

            </ul>
            <a href="javascript:void(0);" id="btn_add_rule" class="ncap-btn ncbtn-aqua"><i class="icon-plus-sign"></i>添加分类</a>
            <div id="div_add_rule" style="display:none;">
                <div class="ncsc-mansong-error"><span id="no_select_error" style="display:none;font-size: 12px;color: #F00;margin-bottom: 5px;"><i class="icon-exclamation-sign"></i>请选择分类！</span></div>
                <div class="ncsc-mansong-rule">
                    <div id="gcategory">
                        <input type="hidden" value="" class="mls_id" name="cate_id[]" />
                        <input type="hidden" value="" class="mls_name" name="cate_name[]" />
                        <input type="hidden" value="" class="mls_names" name="cate_name" />
                        <select class="class-select">
                            <option value="0"><?php echo $lang['nc_please_choose'];?></option>
                            <?php if(!empty($output['gc_list'])){ ?>
                                <?php foreach($output['gc_list'] as $k => $v){ ?>
                                    <?php if ($v['gc_parent_id'] == 0) {?>
                                        <option value="<?php echo $v['gc_id'];?>"><?php echo $v['gc_name'];?></option>
                                    <?php } ?>
                                <?php } ?>
                            <?php } ?>
                        </select>
                    </div>
                </div>
                <div class="mt10">
                    <a href="javascript:void(0);" id="btn_save_rule" class="ncap-btn ncbtn-aqua"><i class="icon-ok-circle"></i>确定所选分类</a>
                    <a href="javascript:void(0);" id="btn_cancel_add_rule" class="ncap-btn ncbtn-bittersweet"><i class="icon-ban-circle"></i>取消</a></div>
            </div>

          <span class="err"></span>
          <p class="notic"><?php echo $lang['goods_tag_add_sup_tag_notice'];?></p>

        </dd>
      </dl>
      <dl class="row">
        <dt class="tit">
          <label><?php echo $lang['nc_sort'];?></label>
        </dt>
        <dd class="opt">
          <input type="text" value="0" name="tag_sort" id="tag_sort" class="input-txt">
          <span class="err"></span>
          <p class="notic"><?php echo $lang['goods_class_add_update_sort'];?></p>
        </dd>
      </dl>
    <dl class="row">
        <dt class="tit">类型</dt>
        <dd class="opt">
            <input id="show_type_0" type="radio" checked="checked" value="0" style="margin-bottom:6px;" name="tag_type_id" />
            <label for="show_type_0">默认</label>
            <input id="show_type_1" type="radio" value="1" style="margin-bottom:6px;" name="tag_type_id" />
            <label for="show_type_1">活动标签</label>
            <span class="err"></span>
        </dd>
    </dl>
    <dl class="row">
        <dt class="tit">是否显示</dt>
        <dd class="opt">
            <div class="onoff">
                <label for="tag_state1" class="cb-enable selected"><?php echo $lang['nc_yes'];?></label>
                <label for="tag_state0" class="cb-disable"><?php echo $lang['nc_no'];?></label>
                <input id="tag_state1" name="tag_state" checked="checked" value="1" type="radio">
                <input id="tag_state0" name="tag_state" value="0" type="radio">
            </div>
            <p class="notic"><?php echo $lang['brand_index_recommend_tips'];?></p>
        </dd>
    </dl>
      <div class="bot"><a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="submitBtn"><?php echo $lang['nc_submit'];?></a></div>
    </div>
  </form>
</div>
<script id="mansong_rule_template" type="text/html">
    <li nctype="mansong_rule_item">
        <span><%=cate_names%>　</span>
        <input type="hidden" name="cate_ids[]" value="<%=cate_id%>">
        <a nctype="btn_del_mansong_rule" href="javascript:void(0);" class="ncbtn-mini ncbtn-grapefruit"><i class="icon-trash"></i>删除</a>
    </li>
</script>
<script type="text/javascript" src="<?php echo RESOURCE_SITE_URL;?>/js/template.min.js"></script>
<script src="<?php echo RESOURCE_SITE_URL;?>/js/common_select.js" charset="utf-8"></script>
<script>
$(function(){
//自动加载滚动条
    $('#type_div').perfectScrollbar();
//按钮先执行验证再提交表单    
	$("#submitBtn").click(function(){
		if($("#goods_class_form").valid()){
			$("#goods_class_form").submit();
		}
	});
	
	$('input[type="radio"][name="t_id"]').click(function(){
		if($(this).val() == '0'){
			$('#t_name').val('');
		}else{
			$('#t_name').val($(this).next('span').html());
		}
	});

    $('#btn_add_rule').on('click', function() {
        $('#mansong_price').val('');
        $('#mansong_discount').val('');
        $('#mansong_goods_item').html('');
        $('#no_select_error').hide();
        $('#div_add_rule').show();
        $('#btn_add_rule').hide();
    });
    // 规则保存
    $('#btn_save_rule').on('click', function() {
        var cate_list = {};
        cate_list.cate_id = Number($('.mls_id').val());
        if(isNaN(cate_list.cate_id) || cate_list.cate_id <= 0) {
            $('#no_select_error').show();
            return false;
        } else {
            $('#no_select_error').hide();
        }
        cate_list.cate_name = $('.mls_name').val();
        cate_list.cate_names = $('.mls_names').val();
        var cate_list_rule_item = template.render('mansong_rule_template', cate_list);
        $('#mansong_rule_list').append(cate_list_rule_item);
        close_div_add_rule();
    });

    // 删除已添加的分类
    $('#mansong_rule_list').on('click', '[nctype="btn_del_mansong_rule"]', function() {
        $(this).parents('[nctype="mansong_rule_item"]').remove();
        close_div_add_rule();
    });

    // 取消添加分类
    $('#btn_cancel_add_rule').on('click', function() {
        close_div_add_rule();
    });


//表单验证	
	$('#goods_class_form').validate({
        errorPlacement: function(error, element){
			var error_td = element.parent('dd').children('span.err');
            error_td.append(error);
        },
        rules : {
            tag_name : {
                required : true,
                /*remote   : {
                url :'index.php?act=goods_tag&op=ajax&branch=check_tag_name',
                type:'get',
                data:{
                    tag_name : function(){
                        return $('#tag_name').val();
                    },
                    gc_parent_id : function() {
                        return $('#gc_parent_id').val();
                    },
                    gc_id : ''
                  }
                }*/
            },
            tag_sort : {
                number   : true
            }
        },
        messages : {
            tag_name : {
                required : '<i class="fa fa-exclamation-circle"></i><?php echo $lang['goods_tag_add_name_null'];?>',
                //remote   : '<i class="fa fa-exclamation-circle"></i><?php echo $lang['goods_tag_add_name_exists'];?>'
            },
            tag_sort  : {
                number   : '<i class="fa fa-exclamation-circle"></i><?php echo $lang['goods_tag_add_sort_int'];?>'
            }
        }
    });

	// 所属分类
    $("#gc_parent_id").live('change',function(){
    	type_scroll($(this));
    });
    // 类型搜索
    $("#gcategory > select").live('change',function(){
    	type_scroll($(this));
    });

});
// 关闭分类添加窗口
function close_div_add_rule() {
    var rule_count = $('#mansong_rule_list').find('[nctype="mansong_rule_item"]').length;
    if( rule_count >= 20) {
        $('#btn_add_rule').hide();
    } else {
        $('#btn_add_rule').show();
    }
    $('#div_add_rule').hide();
    $('#mansong_rule_count').val(rule_count);
}

var typeScroll = 0;
function type_scroll(o){
	var id = o.val();
	if(!$('#type_dt_'+id).is('dt')){
		return false;
	}
	$('#type_div').scrollTop(-typeScroll);
	var sp_top = $('#type_dt_'+id).offset().top;
	var div_top = $('#type_div').offset().top;
	$('#type_div').scrollTop(sp_top-div_top);
	typeScroll = sp_top-div_top;
}
gcategoryInit('gcategory');
function remove_attr(o){
    o.parents('div:first').remove();
}

</script>
