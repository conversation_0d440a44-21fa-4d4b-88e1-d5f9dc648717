<?php
/**
 * 订单物流导入
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');
class order_expressControl extends SystemControl{


    public function __construct(){
        parent::__construct();
        Language::read('trade');
    }

    public function indexOp(){
        $newdata = [];
        if (chksubmit()){
            require_once  BASE_ROOT_PATH.'/vendor/phpoffice/phpexcel/Classes/PHPExcel/IOFactory.php';
            if (!empty($_FILES['files']['name'])){
                $tmp = $_FILES['files']['tmp_name'];
                $imageName = "25220_".date("His",time())."_".rand(1111,9999).'.xlsx';
                $path= BASE_UPLOAD_PATH.DS.ATTACH_CHAIN;
                if(move_uploaded_file($tmp,$path.DS.$imageName)){
                    $fileSrc= $path."/". $imageName;
                }else{
                    echo 'error';exit;
                }
            }else{
                showDialog('请上传导入文件', 'reload');
            }
            $extension = strtolower( pathinfo($_FILES['files']['name'], PATHINFO_EXTENSION) );
            if ($extension =='xlsx') {
                $objReader = new PHPExcel_Reader_Excel2007();
                $objPHPExcel = $objReader ->load($fileSrc);
            } else if ($extension =='xls') {
                $objReader = PHPExcel_IOFactory::createReader('Excel5');
                $objPHPExcel = $objReader->load($fileSrc);
            } else if ($extension=='csv') {//没测
                $PHPReader = new PHPExcel_Reader_CSV();
                //默认输入字符集
                $PHPReader->setInputEncoding('GBK');
                //默认的分隔符
                $PHPReader->setDelimiter(',');
                //载入文件
                $objExcel = $PHPReader->load($fileSrc);
            }
            $sheet = $objPHPExcel->getSheet(0);
            $highestRow = $sheet->getHighestRow(); // 取得总行数
            //$highestColumn = $sheet->getHighestColumn(); // 取得总列数
            for($i=2;$i<=$highestRow;$i++)
            {
                $data['order_sn']= $objPHPExcel->getActiveSheet()->getCell("A".$i)->getValue();//订单编号
                $data['shipping_name'] = $objPHPExcel->getActiveSheet()->getCell("C".$i)->getValue();//物流公司
                $data['shipping_code']= $objPHPExcel->getActiveSheet()->getCell("D".$i)->getValue();//物流单号
                $result= $this->send($data['order_sn'],$data['shipping_code'],$data['shipping_name']);
                if (!$result && $data['shipping_code']&& $data['order_sn']) {
                    array_push($newdata,$data);
                }
            }
            $orderList = "";
            if (is_array($newdata) && !empty($newdata)) {
                $orderList .= '更新物流单失败订单<br/>';
                foreach ($newdata as $key => $val) {
                    $orderList .= $val['order_sn']. ' '.$val['shipping_name'].' '.$val['shipping_code'].'<br/>';
                }
            }
            if (!empty($fileSrc)){
                @unlink($fileSrc);
            }
            if (!empty($orderList)) {
                echo '<pre>';print_r($orderList);
            }
            echo "<br/>程序执行完毕！";
            die;
        }else {
            Tpl::output('express_list',$newdata);
            Tpl::showpage('order_express.index');
        }
    }

    private function send($order_sn,$shipping_code,$e_name){
        $model_order = Model('order');
        $condition = array();
        $condition['order_sn'] = $order_sn;
        $order_info = $model_order->getOrderInfo($condition,array('order_common','order_goods'));
        $order_id = $order_info['order_id'];
        $if_allow_send = intval($order_info['lock_state']) || !in_array($order_info['order_state'],array(ORDER_STATE_PAY));
        if ($if_allow_send || intval($order_info['refund_state']) > 0) {
            return false;
        }
        //取发货地址
        $model_daddress = Model('daddress');
        if ($order_info['extend_order_common']['daddress_id'] > 0 ){
            $daddress_info = $model_daddress->getAddressInfo(array('address_id'=>$order_info['extend_order_common']['daddress_id']));
        }else{
            //取默认地址
            $daddress_info = $model_daddress->getAddressList(array('store_id'=>1),'*','is_default desc',1);
            $daddress_info = $daddress_info[0];

            //写入发货地址编号
            $this->_edit_order_daddress($daddress_info['address_id'], $order_id);
        }
        $logic_order = Logic('order');
        $reciver_name = $order_info['extend_order_common']['reciver_name'];
        $reciver_area = $order_info['extend_order_common']['reciver_info']['area'];
        $reciver_street = $order_info['extend_order_common']['reciver_info']['street'];
        $reciver_mob_phone = $order_info['extend_order_common']['reciver_info']['mob_phone'];
        $reciver_tel_phone = $order_info['extend_order_common']['reciver_info']['tel_phone'];
        $reciver_dlyp = $order_info['extend_order_common']['reciver_info']['dlyp'];
        $chain_price = $order_info['extend_order_common']['reciver_info']['chain_price'];
        $deliver_explain= $order_info['extend_order_common']['deliver_explain'];
        $reciver_info = array(
            'address' => $reciver_area . ' ' . $reciver_street,
            'phone' => trim($reciver_mob_phone . ',' . $reciver_tel_phone,','),
            'area' => $reciver_area,
            'street' => $reciver_street,
            'mob_phone' => $reciver_mob_phone,
            'tel_phone' => $reciver_tel_phone,
            'dlyp' => $reciver_dlyp,
            'chain_price' => ncPriceFormat(floatval($chain_price))
        );
        $shipping_express_id = str_replace(array("顺丰","顺丰物流","顺丰快递","圆通快递","圆通","韵达快递","韵达","中通快递","中通","德邦物流","德邦","汇通快递","汇通","优速快递","优速","EMS","ems","申通快递","申通","天天快递","天天"),
            array('29','29','29',"40","40","41","41","44","44","7","7","16","16","43","43","8","8","28","28","32","32"),$e_name);
        $new_reciver_info = serialize($reciver_info);
        $order_data['reciver_info'] = $new_reciver_info;
        $order_data['reciver_name'] = $reciver_name;
        $order_data['deliver_explain'] = $deliver_explain;
        $order_data['daddress_id'] = $daddress_info['address_id'];
        $order_data['shipping_express_id'] = $shipping_express_id;//$order_info['shipping_express_id'];
        $order_data['shipping_code'] = $shipping_code;
        $result = $logic_order->changeOrderSend($order_info, 'admin', $this->admin_info['name'], $order_data);
        if (!$result['state']) {
            return false;
        } else {
            //ERP发送物流
            define('SCRIPT_ROOT',  BASE_DATA_PATH.'/api/ERP');
            require_once SCRIPT_ROOT.'/order/'.'order.php';
            require_once SCRIPT_ROOT.'/base/'.'member.php';
            $vr_reallogic=Logic('erp_realorder');
            $vr_reallogic->updateExpressState($order_info['order_id']);
            return true;
        }
    }

    /**
     * 批量发货
     */
    private function batch_send()
    {
        $order_id = explode(',', trim($_GET['order_id']));

        $model_order = Model('order');
        $condition = array();
        $condition['order_id'] = array('in', $order_id);
        $condition['order_type'] = array('in', array(1, 2, 4, 20));
        $condition['store_id'] = $_SESSION['store_id'];
        $condition['order_state'] = ORDER_STATE_PAY;
        $order_list = $model_order->getOrderList($condition, '', '*', 'order_id desc', '', array('order_common', 'order_goods'));
        if (empty($order_list)) {
            showMessage('没有可批量发货的订单', '', 'html', 'error');
        }

        if (chksubmit()) {
            $logic_order = Logic('order');
            foreach ($order_list as $k => $v) {
                //需要物流
                if (intval($_POST['shipping_type']) == 1) {
                    $deliver_explain_name = 'deliver_explain_' . $k;
                    $shipping_code_name = 'shipping_code_' . $k;
                    $_POST['deliver_explain'] = $_POST[$deliver_explain_name];
                    $_POST['shipping_code'] = $_POST[$shipping_code_name];
                    $result = $logic_order->batchChangeOrderSend($v, 'seller', $_SESSION['seller_name'], $_POST);
                } else {
                    $_POST['shipping_express_id'] = 0;
                    $_POST['shipping_code'] = NULL;
                    $result = $logic_order->batchChangeOrderSend($v, 'seller', $_SESSION['seller_name'], $_POST);
                }
                if (!$result['state']) {
                    showDialog($result['msg'], '', 'error');
                }
            }
            showDialog($result['msg'], $_POST['ref_url'], 'succ');
        }
    }

    /**
     * 修改发货地址
     */
    private function _edit_order_daddress($daddress_id, $order_id) {
        $model_order = Model('order');
        $data = array();
        $data['daddress_id'] = intval($daddress_id);
        $condition = array();
        $condition['order_id'] = $order_id;
        //$condition['store_id'] = 1;
        return $model_order->editOrderCommon($data, $condition);
    }
    /**
     * 组合reciver_info
     */
    private function _get_reciver_info() {
        $reciver_info = array(
            'address' => $_POST['reciver_area'] . ' ' . $_POST['reciver_street'],
            'phone' => trim($_POST['reciver_mob_phone'] . ',' . $_POST['reciver_tel_phone'],','),
            'area' => $_POST['reciver_area'],
            'street' => $_POST['reciver_street'],
            'mob_phone' => $_POST['reciver_mob_phone'],
            'tel_phone' => $_POST['reciver_tel_phone'],
            'dlyp' => $_POST['reciver_dlyp'],
            'chain_price' => ncPriceFormat(floatval($_POST['chain_price']))
        );
        return serialize($reciver_info);
    }

}
