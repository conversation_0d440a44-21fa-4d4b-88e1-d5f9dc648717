<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title">
      <div class="subject">
        <h3>已结算业绩导入入</h3>
        <h5>批量修改已结算业绩记录</h5>
      </div>
        <?php echo $output['top_link'];?>
    </div>
  </div>
    <!-- 操作说明 -->
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
            <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
            <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
        <ul>
            <li>批量打款操作excel字段业绩id和订单号</li>
            <li>请先下载导入模版，填充对应需批量业绩id和订单号</li>
            <li>此操作不可逆，请确认清楚。才执行导入操作</li>
            <li>导入执行时间，根据数据量大小，执行时间不等，请不要连续操作，等待弹出批量导出完成提示</li>
            <li>
                <div class="bot" id="submit-holder"><a class="ncap-btn-big ncap-btn-green" href="index.php?act=distri_outside_order&op=cash_tem_export">下载导入模版</a></div>

            </li>
        </ul>
    </div>
  <form id="cash_form" method="post" action='index.php?act=distri_outside_order&op=cash_import' enctype="multipart/form-data">
    <input type="hidden" name="form_submit" value="ok" />
    <div class="ncap-form-default" id="explanation">
      <dl class="row">
        <dt class="tit">
          <label><em>*</em>导入表格</label>
        </dt>
        <dd class="opt">
          <ul class="ncsc-form-radio-list">
              <li><label><input name="dis_outside_export" type="file"></label></li>
             <!-- <li><label><input name="distribution_binding" value="0" checked="checked" type="radio">点击推广链接</label></li>
                <li><label><input name="distribution_binding" value="1" checked="checked" type="radio">下单后绑定</label></li>
                <li><label><input name="distribution_binding" value="3" checked="checked" type="radio">交易完成后绑定</label></li>
          --></ul>
        </dd>
      </dl>

      <div class="bot">
          <input type="submit" id="submitBtn" value="确认提交">
         </div>
    </div>
  </form>
</div>
<script src="<?php echo RESOURCE_SITE_URL;?>/js/common_select.js" charset="utf-8"></script> 
<script src="<?php echo RESOURCE_SITE_URL;?>/js/jquery.ajaxContent.pack.js" type="text/javascript"></script> 
<script>
$(function(){
    $('#submitBtn').on('click',function(){


    })

});

</script> 
