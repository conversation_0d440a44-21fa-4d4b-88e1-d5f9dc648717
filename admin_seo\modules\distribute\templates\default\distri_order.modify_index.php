<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title">
      <div class="subject">
        <h3>订单管理</h3>
        <h5>分销订单管理</h5>

      </div>
        <?php echo $output['top_link'];?>
    </div>
  </div>
  <div id="flexigrid"></div>


<script type="text/javascript">
$(function(){

    $("#flexigrid").flexigrid({
        url: 'index.php?act=distri_order&op=get_modify_xml',
        colModel : [
            {display: '订单编号', name : 'order_sn', width : 150, sortable : false, align: 'left'}, 
            {display: '下单时间', name : 'add_time', width : 140, sortable : false, align: 'left'},
            {display: '订单金额(元)', name : 'order_amount', width : 140, sortable : false, align: 'left'},
            {display: '结算状态', name : 'dis_state', width: 60, sortable : false, align : 'center'},
            {display: '结算时间', name : 'dis_pay_time', width : 80, sortable : false, align: 'center'},
            {display: '分销员', name : 'dis_member_id', width: 120, sortable : false, align : 'left'},
            {display: '分销门店', name : 'dis_chain', width : 200, sortable : false, align: 'left'},
            {display: '操作', name : 'operation', width : 100, sortable : false, align: 'center'},
          ],
        searchitems : [
            {display: '实物订单编号', name : 'order_sn', isdefault: true},
            {display: '实物订单手机号码', name : 'buyer_phone'},
            {display: '虚拟订单编号', name : 'order_v_sn'},
            {display: '虚拟订单手机号码', name : 'buyer_v_phone'}
            ],
        sortname: "order_id",
        sortorder: "desc",
        title: '分销订单明细'
    });
});

</script> 
