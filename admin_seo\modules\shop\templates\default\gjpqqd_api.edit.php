<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title">
      <div class="subject">
        <h3>管家婆设置</h3>
        <h5>管家婆对接基础信息设置</h5>
      </div>
    </div>
  </div>
  <form method="post" name="settingForm">
    <input type="hidden" name="form_submit" value="ok" />
    <div class="ncap-form-default">

    <div class="title">
      <h3>管家婆接口设置</h3>
    </div>
      <dl class="row">
        <dt class="tit">
          <label for="gjp_appkey">appkey</label>
        </dt>
        <dd class="opt">
          <input id="gjp_appkey" name="gjp_appkey" value="<?php echo $output['list_setting']['gjp_appkey'];?>" class="input-txt" type="text">
        </dd>
      </dl>
      <dl class="row">
            <dt class="tit">
                <label for="gjp_appsecret">appsecret</label>
            </dt>
            <dd class="opt">
                <input id="gjp_appsecret" name="gjp_appsecret" value="<?php echo $output['list_setting']['gjp_appsecret'];?>" class="input-txt" type="text">
                <p class="notic">&nbsp;</p>
            </dd>
      </dl>
      <dl class="row">
            <dt class="tit">
                <label for="gjp_serverurl">管家婆授权地址</label>
            </dt>
            <dd class="opt">
                <input id="gjp_serverurl" name="gjp_serverurl" value="<?php echo $output['list_setting']['gjp_serverurl'];?>" class="input-txt" type="text">
                <p class="notic">&nbsp;</p>
            </dd>
      </dl>
        <dl class="row">
            <dt class="tit">
                <label for="gjp_apisurl">管家婆接口地址</label>
            </dt>
            <dd class="opt">
                <input id="gjp_apisurl" name="gjp_apisurl" value="<?php echo $output['list_setting']['gjp_apisurl'];?>" class="input-txt" type="text">
                <p class="notic">&nbsp;</p>
            </dd>
        </dl>
        <dl class="row">
            <dt class="tit">
                <label for="gjp_callbackurl">授权回调地址</label>
            </dt>
            <dd class="opt">
                <input id="gjp_callbackurl" name="gjp_callbackurl" value="<?php echo $output['list_setting']['gjp_callbackurl'];?>" class="input-txt" type="text">
                <a href="javascript:void(0);" id="goAuthorize" class="ncap-btn ncap-btn-blue mr10" style="margin-left: 10px;">去授权</a>
                <p class="notic">&nbsp;</p>
            </dd>
        </dl>
      <div class="bot"><a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" onclick="document.settingForm.submit()"><?php echo $lang['nc_submit'];?></a></div>
    </div>
  </form>
</div>
<script type="text/javascript">
    $(function () {
       $("#goAuthorize").on('click',function () {
           var appkey = $("#gjp_appkey").val();
           var appsecret = $("#gjp_appsecret").val();
           var serverurl = $("#gjp_serverurl").val();
           var callbackurl = $("#gjp_callbackurl").val();
           if (appkey == "" || appsecret == "" || serverurl == "" || callbackurl == "") {
               alert("信息填写不完整！");
               return;
           }
           var url = serverurl+"?appkey=" + appkey + "&appsecret=" + appsecret + "&redirect_url=" + callbackurl + "&state=ycsc123";
           window.open(url);
       });
    });
</script>
