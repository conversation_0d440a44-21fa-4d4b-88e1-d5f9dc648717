<?php
defined('InShopNC') or exit('Access Invalid!');
/**
 * index
 */
$lang['brand_index_brand']		= '品牌管理';
$lang['brand_index_brand_subhead']= '商品品牌管理及店铺申请品牌审核';
$lang['brand_index_to_audit']	= '待审核';
$lang['brand_index_name']		= '品牌名称';
$lang['brand_index_class']		= '所属分类';
$lang['brand_index_class_tips']	= '选择分类，可关联大分类或更具体的下级分类。';
$lang['brand_index_upload_tips']= '品牌LOGO尺寸要求宽度为150像素，高度为50像素、比例为3:1的图片；';
$lang['brand_index_recommend_tips']	= '选择被推荐的图片将在所有品牌列表页“推荐品牌”位置展现。';
$lang['brand_index_pic_sign']	= '品牌图片标识';
$lang['brand_index_help1']		= '当店主添加商品时可选择商品品牌，用户可根据品牌查询商品列表';
$lang['brand_index_help2']		= '被推荐品牌，将在前台品牌推荐模块处显示';
$lang['brand_index_help3']		= '在品牌列表页面，品牌将按类别分组，即具有相同类别的品牌为一组，品牌类别与品牌分类无联系';
/**
 * 新增品牌
 */
$lang['brand_add_name_null']	= '品牌名称不能为空';
$lang['brand_add_sort_int']		= '排序仅可以为数字';
$lang['brand_add_back_to_list']	= '返回品牌列表';
$lang['brand_add_again']		= '继续新增品牌';
$lang['brand_add_support_type']	= '支持格式';
$lang['brand_add_if_recommend']	= '是否推荐';
$lang['brand_add_update_sort']	= '数字范围为0~255，数字越小越靠前';
$lang['brand_add_name_exists']	= '该品牌名称已经存在了，请您换一个';
/**
 * 编辑品牌
 */
$lang['brand_edit_again']	= '重新编辑该品牌';
/**
 * 品牌申请
 */
$lang['brand_apply_pass']	= '品牌审核通过。';
$lang['brand_apply_passed']	= '申请的品牌已通过审核。';
$lang['brand_apply_wrong']	= '品牌不符合要求';
$lang['brand_apply_invalid_argument']	= '状态参数非法!';
$lang['brand_apply_handle_ensure']		= '您确定进行这操作吗';
$lang['brand_apply_fail']		= '申请的品牌审核失败。';
$lang['brand_apply_paramerror']		= '状态参数非法!';
$lang['brand_apply_brandparamerror']		= '品牌参数非法!';