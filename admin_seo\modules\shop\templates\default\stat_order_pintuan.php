<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title">
      <div class="subject">
        <h3><?php echo $lang['stat_order_pintuan'];?></h3>
        <h5><?php echo $lang['stat_order_pintuan_subhead'];?></h5>
      </div>
    </div>
  </div>
  <!-- 操作说明 -->
  <div class="explanation" id="explanation">
    <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
      <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
      <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
    <ul>
      <li><?php echo $lang['stat_order_pintuan_help1'];?></li>
      <li><?php echo $lang['stat_order_pintuan_help2'];?></li>
      <li><?php echo $lang['stat_order_pintuan_help3'];?></li>
    </ul>
  </div> 
  <div id="flexigrid"></div>
<div class="ncap-search-ban-s" id="searchBarOpen"><i class="fa fa-search-plus"></i>高级搜索</div>
  <div class="ncap-search-bar">
    <div class="handle-btn" id="searchBarClose"><i class="fa fa-search-minus"></i>收起边栏</div>
    <div class="title">
      <h3>高级搜索</h3>
    </div>
    <form method="get" name="formSearch" id="formSearch">
      <div id="searchCon" class="content">
        <div class="layout-box">
          <dl>
            <dt>日期筛选</dt>
            <dd>
              <label>
                <select class="s-select" name="qtype_time">
                  <option selected="selected" value="">-请选择-</option>
                  <option value="add_time">下单时间</option>
                </select>
              </label>
              <label>
                <input readonly id="query_start_date" placeholder="请选择起始时间" name=query_start_date value="" type="text" class="s-input-txt" />
              </label>
              <label>
                <input readonly id="query_end_date" placeholder="请选择结束时间" name="query_end_date" value="" type="text" class="s-input-txt" />
              </label>
            </dd>
          </dl>
        </div>
      </div>
      <div class="bottom"> <a href="javascript:void(0);" id="ncsubmit" class="ncap-btn ncap-btn-green mr5">提交查询</a><a href="javascript:void(0);" id="ncreset" class="ncap-btn ncap-btn-orange" title="撤销查询结果，还原列表项所有内容"><i class="fa fa-retweet"></i><?php /*echo $lang['nc_cancel_search'];*/?></a></div>
    </form>
  </div>
</div>
<script type="text/javascript">
$(function(){
	$('#query_start_date').datepicker();
    $('#query_end_date').datepicker();
    // 高级搜索提交
    $('#ncsubmit').click(function(){
        $("#flexigrid").flexOptions({url: 'index.php?act=stat_order_pintuan&op=get_xml&'+$("#formSearch").serialize(),query:'',qtype:''}).flexReload();
    });
    // 高级搜索重置
    $('#ncreset').click(function(){
        $("#flexigrid").flexOptions({url: 'index.php?act=stat_order_pintuan&op=get_xml'}).flexReload();
        $("#formSearch")[0].reset();
    });
    $("#flexigrid").flexigrid({
        url: 'index.php?act=stat_order_pintuan&op=get_xml',
        colModel : [
            {display: '订单ID', name : 'ID', width : 50, sortable : false, align: 'left'},
            {display: '拼团状态', name : 'status', width : 100, sortable : false, align: 'left'},
            {display: '生成订单时间', name : 'create_time', width : 150, sortable : false, align: 'left'},
            {display: '团长订单', name : 'parent_pin_order_sn', width : 150, sortable : false, align: 'left'},
            {display: '主订单号', name : 'pin_order_sn', width : 150, sortable : false, align: 'left'},
            {display: '支付单号', name : 'pay_sn', width : 150, sortable : false, align: 'left'},
            {display: '虚拟订单', name : 'is_virtual', width : 100, sortable : false, align: 'left'},
            {display: '子订单号', name : 'order_sn', width : 150, sortable : false, align: 'left'},
            {display: '主单金额', name : 'pay_price', width : 50, sortable : false, align: 'left'},
            {display: '运费', name : 'shipping_fee', width : 80, sortable : false, align: 'left'},
            {display: '子订单状态', name : 'c_status', width : 100, sortable : false, align: 'left'},
            {display: '支付时间', name : 'payment_time', width : 150, sortable : false, align: 'left'},
            {display: '支付方式', name : 'payment_code', width : 150, sortable : false, align: 'left'},
            {display: '退款单号', name : 'refund_sn', width : 250, sortable : false, align: 'left'},
            {display: '退款时间', name : 'refund_time', width : 150, sortable : false, align: 'left'},
            {display: '退款金额', name : 'refund_amount', width : 50, sortable : false, align: 'left'},
            ],
        buttons : [
			{display: '<i class="fa fa-file-excel-o"></i>导出订单数据', name : 'csv', bclass : 'csv', title : '将选定行数据导出excel文件,如果不选中行，将导出列表所有数据', onpress : fg_operate_order },
        ],

        searchitems : [
            {display: '支付单号', name : 'pay_sn'},
            {display: '主订单号', name : 'pay_sn'},
            ],
        // sortname: "orders.order_id",
        // sortorder: "desc",
        title: '财务对账明细-实物',
    });
});

function fg_operate_order(name, grid) {
    if (name == 'csv') {
    	var itemlist = new Array();
        if($('.trSelected',grid).length>0){
            $('.trSelected',grid).each(function(){
            	itemlist.push($(this).attr('data-id'));
            });
        }
        fg_csv2(itemlist);
    }
   
}

function fg_csv2(ids) {
    id = ids.join(',');
    window.location.href = $("#flexigrid").flexSimpleSearchQueryString()+'&op=get_xml&export_type=1&id=' + id;
}
</script> 
