<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title">
      <div class="subject">
        <h3>分销商合并管理</h3>

      </div>
      <?php echo $output['top_link'];?>
    </div>
  </div>
  <!-- 操作说明 -->
  <div class="explanation" id="explanation">
    <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
      <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
      <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
    <ul>

    </ul>
  </div>
  <div id="flexigrid"></div>

</div>
<script type="text/javascript">
$(function(){


    $("#flexigrid").flexigrid({
        url: 'index.php?act=distri_member_merge&op=get_xml&mem_state=<?php echo intval($output['mem_stat']);?>',
        colModel : [
            {display: '会员ID', name : 'new_member_id', width : 40, sortable : true, align: 'center'},
            {display: '会员新手机号', name : 'new_member_mobile', width : 150, sortable : true, align: 'left'},
            {display: '会员旧手机号', name : 'old_member_mobile', width : 150, sortable : true, align: 'left'},
            {display: '合并时间', name : 'create_time_text', width : 150, sortable : true, align: 'left'},

        ],

        buttons : [

            ],

        searchitems : [

            {display: '会员新手机号', name : 'new_member_mobile'},
            {display: '会员旧手机号', name : 'old_member_mobile'}
            ],

        sortname: "distri_time",
        sortorder: "desc",
        title: '认证分销商列表'
    });
	
});

function fg_operation(name, bDiv) {
    if (name == 'csv') {
        if ($('.trSelected', bDiv).length == 0) {
            if (!confirm('您确定要下载全部数据吗？')) {
                return false;
            }
        }
        var itemids = new Array();
        $('.trSelected', bDiv).each(function(i){
            itemids[i] = $(this).attr('data-id');
        });
        fg_csv(itemids);
    }else if(name == 'check'){
        if ($('.trSelected', bDiv).length == 0) {
            if (!confirm('您确定要审核全部待审核的数据吗？')) {
                return false;
            }
        }
        var itemids = new Array();
        $('.trSelected', bDiv).each(function(i){
            itemids[i] = $(this).attr('data-id');
        });
        var ids = itemids.join(',');
        fg_check(ids);
    }
}

function fg_csv(ids) {
    id = ids.join(',');
    window.location.href = $("#flexigrid").flexSimpleSearchQueryString()+'&op=export_csv&id=' + id;
}

//商品取消分销
function fg_del(id) {
    if(confirm('清退后将不能恢复且该分销员及其的所分销的商品都将失效，确认清退该分销员吗？')){
        $.getJSON('index.php?act=distri_member_merge&op=member_cancle', {member_id:id}, function(data){
            if (data.state) {
                $("#flexigrid").flexReload();
            } else {
                showError(data.msg);
            }
        });
    }
}
//批量审核
function fg_check(ids) {
    _uri = "index.php?act=distri_member_merge&op=batch_auth&id=" + ids;
    CUR_DIALOG = ajax_form('member_check', '批量审核', _uri, 640);
}

</script> 

