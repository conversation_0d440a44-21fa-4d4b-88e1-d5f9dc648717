<?php defined('InShopNC') or exit('Access Invalid!');?>

<?php if($output['erp_order']):?>
<div class="ncap-form-default">
  <dl class="row">
    <dt class="tit">
      <label>ERP订单号</label>
    </dt>
    <dd class="opt">
      <?php echo $output['erp_order']['orderid'];?>
    </dd>
    <dt class="tit">
      <label>创建时间</label>
    </dt>
    <dd class="opt">
      <?php echo $output['erp_order']['createtime'];?>
    </dd>
    <dt class="tit">
      <label>订单状态</label>
    </dt>
    <dd class="opt">
    <?php if($output['erp_order']['orderstate'] ==1):?>未支付<?php elseif($output['erp_order']['orderstate'] ==2):?>
    <?php if($output['erp_order']['expressstate'] ==2):?>待收货<?php elseif($output['erp_order']['expressstate'] ==3):?>交易完成<?php else:?>待发货<?php endif;?>
    <?php elseif($output['erp_order']['orderstate'] ==3):?>已退款<?php elseif($output['erp_order']['orderstate'] ==4):?>已取消
    <?php endif;?>
    </dd>
  </dl>
  <dl class="row">
	  <?php foreach ($output['erp_order']['goods'] as $v):?>
	    <dt class="tit">
	      <label>商品名称</label>
	    </dt>
	    <dd class="opt">
	      <?php echo trim($v['name']); ?>
	    </dd>
	    <dt class="tit">
	      <label>核销状态</label>
	    </dt>
	    <dd class="opt">
	      <?php if($v['chargeoff'] ==1):?>不用核销<?php elseif($v['chargeoff'] ==2):?>需要核销
		  <?php elseif($v['chargeoff'] ==3):?>已核销<?php else:?>-
		  
		  <?php endif;?>
	      
	    </dd>
	    <dt class="tit">
	      <label>核销时间</label>
	    </dt>
	    <dd class="opt">
	      <?php echo $v['chargeofftime']?trim($v['chargeofftime']):'-'; ?>
	    </dd>
	    
	    
      <?php endforeach;?>
    
    
  </dl>
</div>
<?php else:?>
<div class="ncap-form-default"><dl class="row"><dd class="opt">&nbsp;&nbsp;暂无信息</dd></dl>
</div>
<?php endif;?>
