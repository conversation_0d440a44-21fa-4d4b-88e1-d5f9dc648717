<?php
/**
 * 虚拟订单管理
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');
class stat_vr_order_account_checkControl extends SystemControl{
    /**
     * 每次导出订单数量
     * @var int
     */
    const EXPORT_SIZE = 10000;

    public function __construct(){
        parent::__construct();
        Language::read('trade');
        $this->order_type_arr = array( //订单类型
            0=>"普通",
            4=>"拼团",
            10=>"新人专享",
            99=>"助力",
        );
    }

    public function indexOp(){

        //显示支付接口列表(搜索)
        $payment_list = Model('payment')->getPaymentOpenList();
        $payment_list['wxpay'] = array(
                'payment_code' => 'wxpay',
                'payment_name' => '微信支付'
        );

        $payment_list['card'] = [
            'payment_code' => 'card',
            'payment_name' => '储值卡'
        ];

        foreach ($payment_list as $key => $value){
            if ($value['payment_code'] == 'offline') {
                unset($payment_list[$key]);
            }
        }
        Tpl::output('admin_id',$this->admin_info['id']);
        Tpl::output('payment_list',$payment_list);
        Tpl::showpage('stat_vr_order_account_check.index');
    }

    public function get_xmlOp() {
        $model_vr_order = Model('vr_order');
        $model_stat = Model('stat');
        $condition  = ['vr_order.erp_status' => ['lt',2]];
        $this->_get_condition($condition);

        $orderby = 'vr_order.order_id desc';
        $order_allnum =$model_stat->statByFlowstat('vr_order', $condition, 'sum(order_amount) as allnum');

        $field = 'vr_order.order_id,vr_order.chain_id, vr_order.order_type,vr_order.order_sn,vr_order.chain_id,vr_order.order_from,vr_order.add_time,vr_order.order_amount,vr_order.order_state,vr_order.payment_code,vr_order.payment_time,vr_order.buyer_name,vr_order.buyer_phone,vr_order.buyer_id,vr_order.store_name,vr_order.store_id,vr_order.goods_name,vr_order.vr_indate,vr_order.rcb_amount,vr_order.pd_amount,vr_order.finnshed_time,vr_order.evaluation_state,vr_order.refund_amount, vr_refund.refund_sn, vr_refund.add_time as refund_time, vr_order.is_dis,vr_order.dis_member_id';
        $order_list = $model_vr_order->getOrdersListAndCode($condition, $field,!empty($_POST['rp']) ? intval($_POST['rp']) : 15, $orderby,'');
        $data = array();
        $data['now_page'] = $model_vr_order->shownowpage();
        $data['total_num'] = $model_vr_order->gettotalnum();
        //$_SESSION['vr_order_sellnum'] = $order_allnum[0]['allnum'];

        foreach ($order_list as $k => $order_info) {
            $list = array();
            $list = $this->formatOrder($order_info);
            $data['list'][$order_info['order_id']] = $list;
        }

        exit(Tpl::flexigridXML($data));
    }
//    public function get_order_sellnumOp(){
//        echo json_encode(array('vr_order_all_num'=>$_SESSION['vr_order_sellnum']));
//    }

    /**
     * 导出订单数据
     *
     */
    public function export_step2Op(){
        set_time_limit(0);
        $lang   = Language::getLangContent();

        $model_vr_order = Model('vr_order');
        $condition  = ['vr_order.erp_status' => ['lt',2]];

        if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
            $_GET['order_id'] = explode(',',trim($_GET['order_id'],','));
            $condition['vr_order.order_id'] = array('in',$_GET['order_id']);
        }

        $this->_get_condition($condition);
        $field = 'vr_order.order_id,vr_order.chain_id, vr_order.order_type,vr_order.order_sn,vr_order.chain_id,vr_order.order_from,vr_order.add_time,vr_order.order_amount,vr_order.order_state,vr_order.payment_code,vr_order.payment_time,vr_order.buyer_name,vr_order.buyer_phone,vr_order.buyer_id,vr_order.store_name,vr_order.store_id,vr_order.goods_name,vr_order.vr_indate,vr_order.rcb_amount,vr_order.pd_amount,vr_order.finnshed_time,vr_order.evaluation_state,vr_order.refund_amount, vr_refund.refund_sn, vr_refund.add_time as refund_time, vr_order.is_dis,vr_order.dis_member_id';
        $order ='vr_order.order_id desc';

        if (!is_numeric($_GET['curpage'])){
            $count = $model_vr_order->getVrOrdersCodeCount($condition);
            $array = array();
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=stat_vr_order_account_check&op=index');
                Tpl::showpage('export.excel');
            }else{  //如果数量小，直接下载
                $data = $model_vr_order->getVrOrderList($condition,'',$field,$order,self::EXPORT_SIZE,array('chain','tags'));
                $this->createExcel2($data);
            }
        }else{  //下载
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $data = $model_vr_order->getVrOrderList($condition,'',$field, $order,"{$limit1},{$limit2}",array('chain','tags'));
            $this->createExcel2($data);
        }

    }

    /**
     * 生成excel
     *
     * @param array $data
     */
    private function createExcel2($data = array()){
        Language::read('export');
        import('libraries.excel');
        $excel_obj = new Excel();
        $excel_data = array();
        //设置样式
        $excel_obj->setStyle(array('id'=>'s_title','Font'=>array('FontName'=>'宋体','Size'=>'12','Bold'=>'1')));
        //header
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'生成订单时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'支付时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单名称');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'支付方式');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'主单金额');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'主订单号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'退款单号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'退款时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'退款金额');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'主订单状态');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'子订单状态');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'是否分销');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'门店');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'分销员ID');
        //data
        foreach ((array)$data as $k=>$order_info){
            $list = $this->formatOrder($order_info);

            $tmp = array();
            $tmp[] = array('data'=>$list['add_time']);
            $tmp[] = array('data'=>$list['payment_time']);
            $tmp[] = array('data'=>$list['order_name']);
            $tmp[] = array('data'=>$list['payment_code']);
            $tmp[] = array('data'=>$list['order_amount']);
            $tmp[] = array('data'=>$list['main_order_sn']);
            $tmp[] = array('data'=>$list['refund_order_sn']);
            $tmp[] = array('data'=>$list['refund_time']);
            $tmp[] = array('data'=>$list['refund_amount']);
            $tmp[] = array('data'=>$list['order_state']);
            $tmp[] = array('data'=>$list['child_order_state']);
            $tmp[] = array('data'=>$list['is_distribute']);
            $tmp[] = array('data'=>$list['store_name']);
            $tmp[] = array('data'=>$list['dis_member_id']);
            $excel_data[] = $tmp;
        }
        $excel_data = $excel_obj->charset($excel_data,CHARSET);
        $excel_obj->addArray($excel_data);
        $excel_obj->addWorksheet($excel_obj->charset(L('exp_od_order'),CHARSET));
        $excel_obj->generateXML('order-'.$_GET['curpage'].'-'.date('Y-m-d-H',time()));
    }

    /**
     * 处理搜索条件
     */
    private function _get_condition(& $condition) {
        if ($_REQUEST['query'] != '' && in_array($_REQUEST['qtype'],array('main_order_sn','sub_order_sn','vr_refund.refund_sn'))) {
            if($_REQUEST['qtype'] == 'main_order_sn') {
                $condition['vr_order.order_sn'] = array('like',"%{$_REQUEST['query']}%");
            } else if($_REQUEST['qtype'] == 'sub_order_sn') {
                $condition['vr_order.order_sn'] = array('eq',0);
            } else {
                $condition[$_REQUEST['qtype']] = array('like',"{$_REQUEST['query']}");
            }

        }
        if (!in_array($_GET['qtype_time'],array('vr_order.add_time','vr_order.payment_time','vr_order.finnshed_time','vr_refund.refund_time'))) {
            $_GET['qtype_time'] = null;
        }
        $if_start_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_start_date']);
        $if_end_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_end_date']);
        $start_unixtime = $if_start_time ? strtotime($_GET['query_start_date']) : null;
        $end_unixtime = $if_end_time ? strtotime($_GET['query_end_date']): null;
        if ($_GET['qtype_time'] && ($start_unixtime || $end_unixtime)) {
            $condition[$_GET['qtype_time']] = array('time',array($start_unixtime,$end_unixtime));
        }
        if ($_GET['order_from'] > 0) {
            if ($_GET['order_from'] == 99) { // 视频号
                $condition['vr_order.is_live'] = 2;
                $condition['vr_order.payment_code'] = 'wx_jsapi';
            } else {
                $condition['vr_order.order_from'] = $_GET['order_from'];
            }
        }
        $condition['vr_order.store_id'] = $_SESSION['store_id'];
    }

    /** 格式化订单信息
     * @param $order_info
     * @return array
     */
    private function formatOrder($order_info) {
        $list = array();
        $model_chain = Model('chain');
        //获取门店信息
        $chainInfo = array();
        if($order_info['chain_id'] > 0) {
            $chainInfo = $model_chain->getChainInfo(['chain_id'=>$order_info['chain_id']],'chain_name');
        }

        $list['add_time'] = date('Y-m-d H:i:s', $order_info['add_time']);
        $list['payment_time'] = !empty($order_info['payment_time']) ? (intval(date('His',$order_info['payment_time'])) ? date('Y-m-d H:i:s',$order_info['payment_time']) : date('Y-m-d',$order_info['payment_time'])) : '';

        $order_type = '';
        if(isset($this->order_type_arr[$order_info['order_type']])){
            $order_type = $this->order_type_arr[$order_info['order_type']];
        }

        $list['order_name'] = $order_type;
        $list['payment_code'] = orderPaymentName($order_info['payment_code']);
        $list['order_amount'] = ncPriceFormat($order_info['order_amount']);
        $list['main_order_sn'] = $order_info['order_sn'];
        $list['refund_order_sn'] = $order_info['refund_sn'];
        $list['refund_time'] = isset($order_info['refund_time']) ?  date('Y-m-d H:i:s',$order_info['refund_time']): '';;
        $list['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
        $list['order_state'] = $order_info['state_desc'];
        $list['is_distribute'] = $order_info['is_dis'] == 1 ? '是': '否';
        $list['store_name'] = isset($chainInfo['chain_name']) ? $chainInfo['chain_name'] : '';
        $list['dis_member_id'] = $order_info['dis_member_id'];
        return $list;
    }
}
