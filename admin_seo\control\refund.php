<?php
/**
 * 在线退款
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Upet\Integrates\Redis\RedisManager as Redis;
use Upet\Models\Order as OrderAlias;
use Upet\Models\OrderPresale;
use Upet\Models\RefundPreOrder;
use Upet\Modules\Order\Queues\SyncInvoiceInfoQueue;

defined('InShopNC') or exit('Access Invalid!');
class refundControl extends SystemControl{
    public function __construct(){
        parent::__construct();
    }

    public function indexOp() {
        $this->get_detailOp();
    }

    /**
     * 微信退款
     *
     */
    public function wxpayOp() {
        $result = array('state'=>'false','msg'=>'参数错误，微信退款失败');
        $refund_id = intval($_GET['refund_id']);
        $lock = Redis::lock('wxpay_refund:'.$refund_id)->setAutoRelease();
        if (!$lock->get()) {
            showMessage('处理中，请勿频繁操作...');
        }
        $model_refund = Model('refund_return');
        $condition = array();
        $condition['refund_id'] = $refund_id;
        $condition['refund_state'] = '1';
        $detail_array = $model_refund->getDetailInfo($condition);//退款详细
        if(!empty($detail_array) && in_array($detail_array['refund_code'],OrderAlias::PAY_CENTER_TYPES)) {
            $order = $model_refund->getPayDetailInfo($detail_array);//退款订单详细
            $refund_amount = $order['pay_refund_amount'];//本次在线退款总金额

            if ($refund_amount > 0) {
                if((C('dianyin_pay') && $order['payment_from'] > 0) || $order['payment_from'] > 0) {
                    $use_refund_time = $order['payment_time'] + C('dianyin_allow_time');
                    if ($order['payment_code'] <> 'card' && $use_refund_time > time()) {
                        $result['msg'] = '操作太快了，5分钟后再来吧~';
                    }else{
                        $refund = $model_refund->getRefundReturnInfo(array('refund_id'=> $refund_id));
                        $refund_data = array();
                        $refund_data['trade_no'] = $order['trade_no'];
                        $refund_data['refund_amount'] = $refund_amount;
                        $refund_data['order_type'] = $order['order_type'];
                        $refund_data['payment_code'] = $order['payment_code'];
                        $extendInfo = ['order_type'=>'r'];
                        $refund_data['extendInfo'] = json_encode($extendInfo);
                        $refund_data['appId'] = getShopAppId($order['store_id']);
                        $refund_data['refund_sn'] = $refund['refund_sn'];
                        // 视频号支付的不用调电银行
                        if(is_wx_live($order)){
                            $dianyin_result['state'] = true;
                            $data['rspCode'] = 1;
                            $data['refundAmt'] = $refund_amount*100;
                        }else{
                            /**@var dianyin_payLogic $dianyinLogic*/
                            $dianyinLogic = Logic('dianyin_pay');
                            $dianyin_result = $dianyinLogic->orderRefund($refund_data);
                            $data = $dianyin_result['data'];
                        }
                        if ($dianyin_result['state'] && in_array($data['rspCode'],['1','2','0'])) {
                            $detail_array = array();
                            $detail_array['pay_amount'] = ncPriceFormat($data['refundAmt']/100);
                            $detail_array['pay_time'] = time();
                            $result['state'] = 'true';
                            $result['msg'] = '已提交申请退款:'.$detail_array['pay_amount']."元";

                            $consume_array = array();
                            $consume_array['member_id'] = $refund['buyer_id'];
                            $consume_array['member_name'] = $refund['buyer_name'];
                            $consume_array['consume_amount'] = $detail_array['pay_amount'];
                            $consume_array['consume_time'] = time();
                            $consume_array['consume_remark'] = '在线退款成功（到账有延迟），退款退货单号：'.$refund['refund_sn'];
                            QueueClient::push('addConsume', $consume_array);//old end

                            if ($order['order_type'] == 9 && $order['is_head'] == 1) {
                                $push_param = array(
                                    'pushType'=>1,
                                    'refundId'=>$refund['refund_id'],
                                    'refundType'=>$refund['refund_type'],
                                    'refundAmount'=>$refund['refund_amount'],
                                    'refundTime'=>date('Y-m-d H:i:s', $refund['add_time']),
                                );
                                Model('cycle_push_info')->pushSubscribeMsg($order['order_sn'],$push_param);
                            }

                            $refund['admin_state_balance'] = 3;
                            $state = $model_refund->editOrderRefund($refund,$this->admin_info['name']);

                            if ($state) {
                                $refund_array = array();
                                $refund_array['admin_time'] = time();
                                $refund_array['refund_state'] = '3';//状态:1为处理中,2为待管理员处理,3为已完成
                                $refund_array['admin_message'] = "管理员处理在线退款";
                                $refund_array['dy_refund_id'] = $data['refundId'];
                                $refund_array['dy_transaction_no'] = $data['transactionNo'];

                                if($data['rspCode'] === '1'){
                                    $refund_array['dy_state'] = 2;
                                    $refund_array['dy_dealtime'] = time();
                                }

                                $model_refund->editRefundReturn(array('refund_id' => $refund_id), $refund_array);

                                //退款ERP订单
                                $refund['admin_state'] = 2;
                                $refund['mobile'] = $order['buyer_phone'];
                                // 视频号 13:退款完成
                                $refund['return_type'] = 13;
                                $erp_result = Logic('refund')->refundOrder($refund, $order);
                                if (!$erp_result) {
                                    showMessage("驳回申请失败，您稍后再申请~~");
                                }

                                if ($order['order_type'] == 11) {
                                    $insert_data = [
                                        'order_sn' =>  $order['erp_order_sn'],
                                        'pre_status' => 1,
                                        'refund_amount' => ncPriceFormat($data['refundAmt']/100),
                                        'dy_refund_id' => $data['refundId'],
                                        'dy_transaction_no' => $data['transactionNo'],
                                        'rsp_code' => $data['rspCode'],
                                        'refund_state' => 2,
                                        'msg' => $data['msg'],
                                    ];
                                    //添加预售订单定金退款记录
                                    RefundPreOrder::insert($insert_data);

                                    //尾款退款
                                    if($order['last_refund_amount'] > 0){
                                        OrderPresale::dopreRefund($order, $order['last_refund_amount']);
                                    }

                                    $result['msg'] = "已提交申请退款，（含定金：{$detail_array['pay_amount']}元,尾款：{$order['last_refund_amount']}元）";
                                }
                                $this->log('退款确认，退款编号'.$refund['refund_sn']);
                                $detail_array['pd_amount'] = 0;
                                $detail_array['refund_state'] = 2;
                                $model_refund->editDetail(array('refund_id'=> $refund_id), $detail_array);
                            }
                        }else{
                            if ($data['rspCode'] == "3" || $data['rspCode'] == "0" || $data['rspCode'] == "-1") {
                                $result['msg'] = '退款返回信息,'.$data['msg'];//错误描述
                            }else{
                                $result['msg'] = '退款错误,'.$dianyin_result['msg'];//错误描述
                            }
                        }
                    }
                }else{
                    $wxpay = $order['payment_config'];
                    define('WXPAY_APPID', $wxpay['appid']);
                    define('WXPAY_MCHID', $wxpay['mchid']);
                    define('WXPAY_KEY', $wxpay['key']);
                    if ($order['order_father'] > 0 && $order['order_type'] != 11) {
                        $parent_order = Model('order')->getOrderInfo(['order_id'=>$order['order_father']],[],"order_amount");//订单详细
                        $order['pay_amount'] = ncPriceFormat($parent_order['order_amount']);
                    }
                    $total_fee = $order['pay_amount']*100;//微信订单实际支付总金额(在线支付金额,单位为分)
                    $refund_fee = $refund_amount*100;//本次微信退款总金额(单位为分)
                    $api_file = BASE_PATH.DS.'api'.DS.'refund'.DS.'wxpay'.DS.'WxPay.Api.php';
                    include $api_file;
                    $input = new WxPayRefund();
                    $input->SetTransaction_id($order['trade_no']);//微信订单号
                    $input->SetTotal_fee($total_fee);
                    $input->SetRefund_fee($refund_fee);
                    $input->SetOut_refund_no($detail_array['batch_no']);//退款批次号
                    $input->SetOp_user_id(WxPayConfig::MCHID);
                    $data = WxPayApi::refund($input);
                    if(!empty($data) && $data['return_code'] == 'SUCCESS') {//请求结果
                        if($data['result_code'] == 'SUCCESS') {//业务结果
                            $detail_array = array();
                            $detail_array['pay_amount'] = ncPriceFormat($data['refund_fee']/100);
                            $detail_array['pay_time'] = time();
                            $result['state'] = 'true';
                            $result['msg'] = '微信成功退款:'.$detail_array['pay_amount'];

                            $refund = $model_refund->getRefundReturnInfo(array('refund_id'=> $refund_id));
                            $consume_array = array();
                            $consume_array['member_id'] = $refund['buyer_id'];
                            $consume_array['member_name'] = $refund['buyer_name'];
                            $consume_array['consume_amount'] = $detail_array['pay_amount'];
                            $consume_array['consume_time'] = time();
                            $consume_array['consume_remark'] = '微信在线退款成功（到账有延迟），退款退货单号：'.$refund['refund_sn'];
                            QueueClient::push('addConsume', $consume_array);//old end
                            //周期购主订单订阅通知
                            if ($order['order_type'] == 9 && $order['is_head'] == 1) {
                                $push_param = array(
                                    'pushType'=>1,
                                    'refundId'=>$refund['refund_id'],
                                    'refundType'=>$refund['refund_type'],
                                    'refundAmount'=>$refund['refund_amount'],
                                    'refundTime'=>date('Y-m-d H:i:s', $refund['add_time']),
                                );
                                Model('cycle_push_info')->pushSubscribeMsg($order['order_sn'],$push_param);
                            }

                            $refund['admin_state_balance'] = 3;
                            $state = $model_refund->editOrderRefund($refund,$this->admin_info['name']);
                            if ($state) {
                                $refund_array = array();
                                $refund_array['admin_time'] = time();
                                $refund_array['refund_state'] = '3';//状态:1为处理中,2为待管理员处理,3为已完成
                                $refund_array['admin_message'] = "管理员处理微信支付在线退款";
                                $model_refund->editRefundReturn(array('refund_id' => $refund_id), $refund_array);

                                //退款ERP订单
                                $refund['admin_state'] = 2;
                                $refund['mobile'] = $order['buyer_phone'];
                                // 视频号 13:退款完成
                                $refund['return_type'] = 13;
                                $erp_result = Logic('refund')->refundOrder($refund,$order);
                                if (!$erp_result) {
                                    showMessage("驳回申请失败，您稍后再申请~~");
                                }

                                //预售定金退款处理
                                if ($order['order_type'] ==11) {
                                    $insert_data = [
                                        'order_sn' =>  $order['erp_order_sn'],
                                        'pre_status' => 1,
                                        'refund_amount' => ncPriceFormat($data['refund_fee']/100),
                                        'dy_refund_id' => $order['trade_no'],
                                        'dy_transaction_no' => $detail_array['batch_no'],
                                        'rsp_code' => 1,
                                        'refund_state' => 2,
                                        'msg' => $data['msg'],
                                    ];
                                    //添加预售订单退款记录
                                    RefundPreOrder::insert($insert_data);
                                    //尾款退款
                                    if($order['last_refund_amount'] > 0){
                                        OrderPresale::dopreRefund($order,$order['last_refund_amount']);
                                    }
                                    $result['msg'] = "已提交申请退款，（含定金：{$detail_array['pay_amount']}元,尾款：{$order['last_refund_amount']}元）";
                                }

                                $this->log('退款确认，退款编号'.$refund['refund_sn']);
                                $detail_array['pd_amount'] = 0;
                                $detail_array['refund_state'] = 2;
                                $model_refund->editDetail(array('refund_id'=> $refund_id), $detail_array);
                            }
                        } else {
                            $result['msg'] = '微信退款错误,'.$data['err_code_des'];//错误描述
                        }
                    } else {
                        $result['msg'] = '微信接口错误,'.$data['return_msg'];//返回信息
                    }
                }
            }elseif ($order['last_refund_amount'] > 0){
                OrderPresale::dopreRefund($order, $order['last_refund_amount']);
                $result['state'] = 'true';
                $result['msg'] = '已提交申请退款:'.$order['last_refund_amount']."元";

                $refund = $model_refund->getRefundReturnInfo(array('refund_id'=> $refund_id));
                $refund['admin_state_balance'] = 3;
                $state = $model_refund->editOrderRefund($refund,$this->admin_info['name']);

                if ($state) {
                    $refund_array = array();
                    $refund_array['admin_time'] = time();
                    $refund_array['refund_state'] = '3';//状态:1为处理中,2为待管理员处理,3为已完成
                    $refund_array['admin_message'] = "管理员处理在线退款";
                    $model_refund->editRefundReturn(array('refund_id' => $refund_id), $refund_array);
                }
            }
        }

        // 发票红冲操作
        if ($result['state'] == 'true') {
            SyncInvoiceInfoQueue::dispatch($refund_id);
        }

        exit(json_encode($result));
    }

    /**
     * 微信/储值卡 退款查询
     */
    public function queryWxpayOp() {
        $result = array('state'=>'false','msg'=>'参数错误，订单不存在');
        $refund_id = intval($_GET['refund_id']);
        $model_refund = Model('refund_return');
        $condition = array();
        $condition['refund_id'] = $refund_id;
        $detail_array = $model_refund->getDetailInfo($condition);//退款详细
        if(!empty($detail_array) && in_array($detail_array['refund_code'],OrderAlias::PAY_CENTER_TYPES)) {
            $order = $model_refund->getPayDetailInfo($detail_array);//退款订单详细
            if (is_array($order) && !empty($order)) {
                if((C('dianyin_pay') && $order['payment_from'] > 0) || $order['payment_from'] > 0) {
                    $refund = $model_refund->getRefundReturnInfo($condition,"refund_amount,dy_refund_id,dy_transaction_no");
                    $refund_data = array();
                    $refund_data['dy_transaction_no'] = $refund['dy_transaction_no'];
                    $refund_data['dy_refund_id'] = $refund['dy_refund_id'];
                    $refund_data['refund_amount'] = $refund['refund_amount'];
                    $refund_data['payment_code'] = $order['payment_code'];
                    $refund_data['trade_no'] = $order['trade_no'];
                    $dianyin_result = Logic('dianyin_pay')->queryOrder($refund_data);
                    if ($dianyin_result['state']) {
                        $data = $dianyin_result['data'];
                        $pay_amount = ncPriceFormat($data['refundAmt'] / 100);
                        $result['state'] = 'true';
                        $result['msg'] = '查询成功退款:' . $pay_amount;
                    }else{
                        $result['msg'] = '查询退款:' . $dianyin_result['msg'];
                    }
                }else{
                    $wxpay = $order['payment_config'];
                    define('WXPAY_APPID', $wxpay['appid']);
                    define('WXPAY_MCHID', $wxpay['mchid']);
                    define('WXPAY_KEY', $wxpay['key']);
                    $api_file = BASE_PATH.DS.'api'.DS.'refund'.DS.'wxpay'.DS.'WxPay.Api.php';
                    include $api_file;
                    $input = new WxPayRefund();
                    $input->SetTransaction_id($order['trade_no']);//微信订单号
                    $data = WxPayApi::refundQuery($input);
                    if(!empty($data) && $data['return_code'] == 'SUCCESS') {//请求结果
                        if ($data['result_code'] == 'SUCCESS') {//业务结果
                            $pay_amount = ncPriceFormat($data['refund_fee'] / 100);
                            $result['state'] = 'true';
                            $result['msg'] = '查询微信成功退款:' . $pay_amount;
                        }
                    }
                }
            }
        }
        exit(json_encode($result));
    }

    /**
     * 支付宝退款
     *
     */
    public function alipayOp() {
        $refund_id = intval($_GET['refund_id']);
        $lock = Redis::lock('alipay_refund:'.$refund_id)->setAutoRelease();
        if (!$lock->get()) {
            showMessage('处理中，请勿频繁操作...');
        }
        $model_refund = Model('refund_return');
        $condition = array();
        $condition['refund_id'] = $refund_id;
        $condition['refund_state'] = '1';
        $detail_array = $model_refund->getDetailInfo($condition);//退款详细
        if(!empty($detail_array) && $detail_array['refund_code'] == 'alipay') {
            $order = $model_refund->getPayDetailInfo($detail_array);//退款订单详细
            $refund_amount = $order['pay_refund_amount'];//本次在线退款总金额
            if ($refund_amount > 0) {
                $payment_config = $order['payment_config'];
                $alipay_config = array();
                $alipay_config['seller_email'] = $payment_config['alipay_account'];
                $alipay_config['partner'] = $payment_config['alipay_partner'];
                $alipay_config['key'] = $payment_config['alipay_key'];
                $api_file = BASE_PATH.DS.'api'.DS.'refund'.DS.'alipay'.DS.'alipay.class.php';
                include $api_file;
                $alipaySubmit = new AlipaySubmit($alipay_config);
                $parameter = getPara($alipay_config);
                $batch_no = $detail_array['batch_no'];
                $b_date = substr($batch_no,0,8);
                if($b_date != date('Ymd')) {
                    $batch_no = date('Ymd').substr($batch_no, 8);//批次号。支付宝要求格式为：当天退款日期+流水号。
                    $model_refund->editDetail(array('refund_id'=> $refund_id), array('batch_no'=> $batch_no));
                }
                $parameter['batch_no'] = $batch_no;
                $parameter['detail_data'] = $order['trade_no'].'^'.$refund_amount.'^协商退款';//数据格式为：原交易号^退款金额^理由
                $pay_url = $alipaySubmit->buildRequestParaToString($parameter);
                @header("Location: ".$pay_url);
            }
        }
    }

    /**
     * 支付退款查询
     */
    public function queryAlipayOp() {

    }

    /**
     * 在线退款查询
     *
     */
    public function get_detailOp() {
        $result = array('state'=>'false','msg'=>'退款正在处理中或已失败，稍后查询');
        $refund_id = intval($_GET['refund_id']);
        $model_refund = Model('refund_return');
        $condition = array();
        $condition['refund_id'] = $refund_id;
        $detail_array = $model_refund->getDetailInfo($condition);//退款详细
        if($detail_array['pay_time'] > 0) {
            $result = array('state'=>'true','msg'=>'成功退款:'.ncPriceFormat($detail_array['pay_amount']));
        }
        exit(json_encode($result));
    }
}
