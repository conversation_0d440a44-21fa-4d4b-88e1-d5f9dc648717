<?php
defined('InShopNC') or exit('Access Invalid!');
/**
 * 店铺分类 语言包
 */

$lang['store_class_name_no_null']     = '分类名称不能为空';
$lang['store_class_sort_only_number'] = '分类排序仅能为数字!';
$lang['back_store_class_list']        = '返回分类列表';
$lang['continue_add_store_class']     = '继续新增分类';
$lang['re_edit_store_class']          = '重新编辑该分类!';
$lang['import_ok']                    = '导入成功';
$lang['import_csv_no_null']           = '导入的csv文件不能为空!';
$lang['illegal_parameter']            = '参数非法';

$lang['store_class']      = '店铺分类';
$lang['store_class_subhead'] = '商城店铺分类管理设置';
$lang['manage']           = '管理';
$lang['store_class_name'] = '分类名称';
$lang['store_class_bail'] = '保证金数额';
$lang['can_edit']         = '可编辑';
$lang['del_store_class']  = '删除后将不能恢复，确认删除吗';
$lang['store_class']      = '店铺分类';
$lang['manage']           = '管理';
$lang['store_class_name'] = '分类名称';
$lang['store_class_help1'] = '商家入驻时可指定此处设置店铺分类';
$lang['store_class_help2'] = '<a>对分类作任何更改后，都需要到 设置 -> 清理缓存 清理店铺分类，新的设置才会生效</a>';
$lang['can_edit']         = '可编辑';
$lang['back']             = '返回';
$lang['update_sort']      = '数字范围为0~255，数字越小越靠前';
$lang['reset']            = '重置';
$lang['store_class_name_no_null']     = '分类名称不能为空';
$lang['store_class_name_is_there']    = '该分类名称已经存在了，请您换一个';
$lang['store_class_sort_only_number'] = '分类排序仅能为数字';
