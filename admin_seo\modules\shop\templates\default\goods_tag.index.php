<?php defined('InShopNC') or exit('Access Invalid!');?>
<style type="text/css">
.flexigrid .bDiv tr:nth-last-child(2) span.btn ul { bottom: 0; top: auto}
</style>


<div class="page">
  <div class="fixed-bar">
    <div class="item-title">
      <div class="subject">
        <h3><?php echo $lang['goods_tag_index_tag'];?></h3>
        <h5><?php echo $lang['goods_tag_index_tag_subhead_list'];?></h5>
      </div>
      </div>
  </div>
  <div class="explanation" id="explanation">
    <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
      <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
      <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span>
    </div>
    <ul>
      <li><?php echo $lang['goods_tag_index_help1'];?></li>
    </ul>
  </div>
  <div id="flexigrid"></div>
</div>
<script type="text/javascript" src="<?php echo ADMIN_RESOURCE_URL;?>/js/jquery.edit.js" charset="utf-8"></script>
<script type="text/javascript">
$(function(){
    $('#flexigrid').flexigrid({
        url: 'index.php?act=goods_tag&op=get_xml',
        title: '商品标签',// 表格标题
        sortname: "tag_id",
        sortorder: "desc",
        colModel: [
                    {display: '操作', name : 'operation', width : 150, sortable : false, align: 'center', className: 'handle'},
                    {display: '索引ID', name : 'tag_id', width : 80, sortable : true, align: 'center'},
                    {display: '排序', name : 'tag_sort', width : 80, sortable : true, align: 'center'},
                    {display: '标签名称', name : 'tag_name', width : 310, sortable : true, align: 'left'},
                    {display: '标签类型', name : 'tag_type_id', width : 80, sortable : true, align: 'left'},
                    {display: '所属分类', name : 'gc_name', width : 200, sortable : true, align: 'center'},
                    {display: '是否显示', name : 'tag_state', width : 60, sortable : true, align: 'center'},
            /*{display: '状态', name : 'goods_verify', width : 60, sortable : false, align: 'center'}*/
                ],// 不使用列控制
        buttons : [
                   {display: '<i class="fa fa-plus"></i>新增标签', name : 'add', bclass : 'add', onpress : fg_operation },
                   {display: '<i class="fa fa-trash"></i>批量删除', name : 'del', bclass : 'del', title : '将选定行数据批量删除', onpress : fg_operation },
            /*{display: '<i class="fa fa-file-excel-o"></i>导出数据', name : 'csv', bclass : 'csv', title : '导出全部分类数据', onpress : fg_operation }*/
               ],
        searchitems : [
            {display: '标签名称', name : 'tag_name'},
            {display: '索引ID', name : 'tag_id'},
            {display: '所属分类', name : 'gc_name'},
        ],
    });

    $('span[nc_type="inline_edit"]').inline_edit({act: 'goods_tag',op: 'ajax'});
});

function fg_operation(name, bDiv) {
    if (name == 'add') {
        window.location.href = 'index.php?act=goods_tag&op=goods_tag_add';
    } else if (name == 'del') {
        if ($('.trSelected', bDiv).length == 0) {
            showError('请选择要操作的数据项！');return;
        }
        var itemids = new Array();
        $('.trSelected', bDiv).each(function(i){
            itemids[i] = $(this).attr('data-id');
        });
        fg_del(itemids);
    } else if (name = 'csv') {
        window.location.href = 'index.php?act=goods_tag&op=goods_tag_export';
    }
}
function fg_del(ids) {
    if (typeof ids == 'number') {
        var ids = new Array(ids.toString());
    };
    id = ids.join(',');
    if(confirm('删除后将不能恢复，确认删除这项吗？')){
        $.getJSON('index.php?act=goods_tag&op=goods_tag_del', {id:id}, function(data){
            if (data.state) {
                location.reload();
            } else {
                showError(data.msg)
            }
        });
    }
}
</script>