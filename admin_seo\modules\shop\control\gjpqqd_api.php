<?php
/**
 * 管家婆对接设置
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');
class gjpqqd_apiControl extends SystemControl{
    public function __construct(){
        parent::__construct();
    }

    public function indexOp(){
        $model_setting = Model('setting');
        if (chksubmit()){
            $update_array = array();
            $update_array['gjp_appkey']   = $_POST['gjp_appkey'];
            $update_array['gjp_appsecret']  = $_POST['gjp_appsecret'];
            $update_array['gjp_serverurl']  = $_POST['gjp_serverurl'];
            $update_array['gjp_apisurl']  = $_POST['gjp_apisurl'];
            $update_array['gjp_callbackurl']   = $_POST['gjp_callbackurl'];
            $result = $model_setting->updateSetting($update_array);
            if ($result){
                $this->log('管家婆接口设置');
                showMessage(Language::get('nc_common_save_succ'));
            } else {
                showMessage(Language::get('nc_common_save_fail'));
            }
        }
        $list_setting = $model_setting->getListSetting();
        Tpl::output('list_setting',$list_setting);
        Tpl::showpage('gjpqqd_api.edit');
    }

}
