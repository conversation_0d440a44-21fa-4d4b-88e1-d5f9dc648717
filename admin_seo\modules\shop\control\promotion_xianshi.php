<?php
/**
 * 限时折扣管理
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Box\Spout\Common\Type;
use Box\Spout\Writer\WriterFactory;
use Shopnc\Tpl;
use Upet\Models\PXianshiGoods;
use Upet\Queues\SendWebHookNotifyQueue;

defined('InShopNC') or exit('Access Invalid!');
class promotion_xianshiControl extends SystemControl{

    public function __construct(){
        parent::__construct();

        //读取语言包
        Language::read('promotion_xianshi');

        //检查审核功能是否开启
        if (intval($_GET['promotion_allow']) !== 1 && intval(C('promotion_allow')) !== 1){
            $url = array(
                array(
                    'url'=>'index.php?act=promotion_xianshi&promotion_allow=1',
                    'msg'=>Language::get('open'),
                ),
                array(
                    'url'=>'index.php?act=setting',
                    'msg'=>Language::get('close'),
                ),
            );
            showMessage("商品促销功能尚未开启",$url,'html','succ',1,6000);
        }

        //自动开启限时折扣
        if (intval($_GET['promotion_allow']) === 1){
            $model_setting = Model('setting');
            $update_array = array();
            $update_array['promotion_allow'] = 1;
            $model_setting->updateSetting($update_array);
        }
    }

    /**
     * 默认Op
     */
    public function indexOp() {

        $this->xianshi_listOp();

    }

    /**
     * 活动列表
     */
    public function xianshi_listOp()
    {
        $model_xianshi = Model('p_xianshi');
        Tpl::output('xianshi_state_array', $model_xianshi->getXianshiStateArray());

        $this->show_menu('xianshi_list');
        Tpl::showpage('promotion_xianshi.list');
    }

    /**
     * 活动列表
     */
    public function xianshi_list_xmlOp()
    {
        $condition = array();

        if ($_REQUEST['advanced']) {
            if (strlen($q = trim((string) $_REQUEST['xianshi_name']))) {
                $condition['xianshi_name'] = array('like', '%' . $q . '%');
            }
            if (strlen($q = trim((string) $_REQUEST['store_name']))) {
                $condition['store_name'] = array('like', '%' . $q . '%');
            }
            if (($q = (int) $_REQUEST['state']) !=0) {
                $condition['state'] = $q;
            }

            $pdates = array();
            if (strlen($q = trim((string) $_REQUEST['pdate1'])) && ($q = strtotime($q . ' 00:00:00'))) {
                $pdates[] = "end_time >= {$q}";
            }
            if (strlen($q = trim((string) $_REQUEST['pdate2'])) && ($q = strtotime($q . ' 00:00:00'))) {
                $pdates[] = "start_time <= {$q}";
            }
            if ($pdates) {
                $condition['pdates'] = array(
                    'exp',
                    implode(' or ', $pdates),
                );
            }

        } else {
            if (strlen($q = trim($_REQUEST['query']))) {
                switch ($_REQUEST['qtype']) {
                    case 'xianshi_name':
                        $condition['xianshi_name'] = array('like', '%'.$q.'%');
                        break;
                    case 'store_name':
                        $condition['store_name'] = array('like', '%'.$q.'%');
                        break;
                }
            }
        }

        $model_xianshi = Model('p_xianshi');
        $condition['store_id'] = $_SESSION['store_id'];
        $xianshi_list = (array) $model_xianshi->getXianshiList($condition, $_REQUEST['rp'], 'xianshi_id desc');

        $flippedOwnShopIds = array_flip(Model('store')->getOwnShopIds());

        $data = array();
        $data['now_page'] = $model_xianshi->shownowpage();
        $data['total_num'] = $model_xianshi->gettotalnum();

        foreach ($xianshi_list as $val) {
            // 待审核
            if($val['state'] == -2) {
                $o = '<a class="btn blue" href="' . urlAdminShop('promotion_xianshi', 'xianshi_detail', array(
                    'xianshi_id' => $val['xianshi_id'],
                )) . '"><i class="fa fa-check"></i>审核</a>';
            }else{
                $o  = '<a class="btn red confirm-on-click" href="javascript:;" data-href="' . urlAdminShop('promotion_xianshi', 'xianshi_del', array(
                        'xianshi_id' => $val['xianshi_id'],
                    )) . '"><i class="fa fa-trash-o"></i>删除</a>';

                $o .= '<span class="btn"><em><i class="fa fa-cog"></i>设置<i class="arrow"></i></em><ul>';

                if ($val['editable']) {
                    $o .= '<li><a class="confirm-on-click" href="javascript:;" data-href="' . urlAdminShop('promotion_xianshi', 'xianshi_cancel', array(
                            'xianshi_id' => $val['xianshi_id'],
                        )) . '">取消活动</a></li>';
                }

                $o .= '<li><a class="confirm-on-click" href="' . urlAdminShop('promotion_xianshi', 'xianshi_detail', array(
                        'xianshi_id' => $val['xianshi_id'],
                    )) . '">活动详细</a></li>';

                $o .= '</ul></span>';
            }
            $i = array();
            $i['operation'] = $o;
            $i['xianshi_name'] = $val['xianshi_name'];
            $i['store_name'] = '<a target="_blank" href="' . urlShop('show_store', 'index', array(
                'store_id'=>$val['store_id'],
            )) . '">' . $val['store_name'] . '</a>';

            if (isset($flippedOwnShopIds[$val['store_id']])) {
                $i['store_name'] .= '<span class="ownshop">[自营]</span>';
            }

            $i['start_time_text'] = date('Y-m-d H:i', $val['start_time']);
            $i['end_time_text'] = date('Y-m-d H:i', $val['end_time']);

            $i['lower_limit'] = $val['lower_limit'];
            $i['xianshi_state_text'] = $val['xianshi_state_text'];

            $data['list'][$val['xianshi_id']] = $i;
        }

        echo Tpl::flexigridXML($data);
        exit;
    }

    /**
     * 限时折扣活动取消
     **/
    public function xianshi_cancelOp() {
        $xianshi_id = intval($_REQUEST['xianshi_id']);
        $model_xianshi = Model('p_xianshi');
        $result = $model_xianshi->cancelXianshi(array('xianshi_id' => $xianshi_id));
        if($result) {
            $this->log('取消限时折扣活动，活动编号'.$xianshi_id);
            Model('p_time')->delXianshi($xianshi_id);
            $this->jsonOutput();
        } else {
            $this->jsonOutput('操作失败');
        }
    }

    /**
     * 限时折扣活动删除
     **/
    public function xianshi_delOp() {
        $xianshi_id = intval($_REQUEST['xianshi_id']);
        $model_xianshi = Model('p_xianshi');
        $result = $model_xianshi->delXianshi(array('xianshi_id' => $xianshi_id));
        if($result) {
            $this->log('删除限时折扣活动，活动编号'.$xianshi_id);
            Model('p_time')->delXianshi($xianshi_id);
            $this->jsonOutput();
        } else {
            $this->jsonOutput('操作失败');
        }
    }

    /**
     * 活动详细信息
     **/
    public function xianshi_detailOp() {
        $xianshi_id = intval($_GET['xianshi_id']);

        $model_xianshi = Model('p_xianshi');
        $model_xianshi_goods = Model('p_xianshi_goods');

        $xianshi_info = $model_xianshi->getXianshiInfoByID($xianshi_id);
        if(empty($xianshi_info)) {
            showMessage(L('param_error'));
        }
        Tpl::output('xianshi_info',$xianshi_info);

        //获取限时折扣商品列表
        $condition = array();
        $condition['xianshi_id'] = $xianshi_id;
        $condition['store_id'] = $_SESSION['store_id'];
        $xianshi_goods_list = $model_xianshi_goods->getXianshiGoodsExtendList($condition);
        Tpl::output('list',$xianshi_goods_list);

        $this->show_menu('xianshi_detail');
        Tpl::showpage('promotion_xianshi.detail');
    }

    /**
     * 套餐管理
     */
    public function xianshi_quotaOp()
    {
        $this->show_menu('xianshi_quota');
        Tpl::showpage('promotion_xianshi_quota.list');
    }

    /**
     * 套餐管理XML
     */
    public function xianshi_quota_xmlOp()
    {
        $condition = array();

        if (strlen($q = trim($_REQUEST['query']))) {
            switch ($_REQUEST['qtype']) {
                case 'store_name':
                    $condition['store_name'] = array('like', '%'.$q.'%');
                    break;
            }
        }

        $model_xianshi_quota = Model('p_xianshi_quota');
        $condition['store_id'] = $_SESSION['store_id'];
        $list = (array) $model_xianshi_quota->getXianshiQuotaList($condition, $_REQUEST['rp'], 'end_time desc');

        $data = array();
        $data['now_page'] = $model_xianshi_quota->shownowpage();
        $data['total_num'] = $model_xianshi_quota->gettotalnum();

        foreach ($list as $val) {
            $i = array();
            $i['operation'] = '<span>--</span>';

            $i['store_name'] = '<a target="_blank" href="' . urlShop('show_store', 'index', array(
                'store_id' => $val['store_id'],
            )) . '">' . $val['store_name'] . '</a>';

            $i['start_time_text'] = date("Y-m-d", $val['start_time']);
            $i['end_time_text'] = date("Y-m-d", $val['end_time']);

            $data['list'][$val['quota_id']] = $i;
        }

        echo Tpl::flexigridXML($data);
        exit;
    }

    /**
     * 设置
     **/
    public function xianshi_settingOp() {

        $model_setting = Model('setting');
        $setting = $model_setting->GetListSetting();
        Tpl::output('setting',$setting);

        $this->show_menu('xianshi_setting');
        Tpl::showpage('promotion_xianshi.setting');
    }

    public function xianshi_setting_saveOp() {

        $promotion_xianshi_price = intval($_POST['promotion_xianshi_price']);
        if($promotion_xianshi_price < 0) {
            $promotion_xianshi_price = 20;
        }

        $model_setting = Model('setting');
        $update_array = array();
        $update_array['promotion_xianshi_price'] = $promotion_xianshi_price;

        $result = $model_setting->updateSetting($update_array);
        if ($result){
            $this->log('修改限时折扣价格为'.$promotion_xianshi_price.'元');
            showMessage(Language::get('setting_save_success'),'');
        }else {
            showMessage(Language::get('setting_save_fail'),'');
        }
    }

    /**
     * ajax修改团购信息
     */
    public function ajaxOp(){
        $result = true;
        $update_array = array();
        $where_array = array();

        switch ($_GET['branch']){
         case 'recommend':
            $model= Model('p_xianshi_goods');
            $update_array['xianshi_recommend'] = $_GET['value'];
            $where_array['xianshi_goods_id'] = $_GET['id'];
            $result = $model->editXianshiGoods($update_array, $where_array);
            break;
        }

        if($result) {
            echo 'true';exit;
        } else {
            echo 'false';exit;
        }

    }


    /*
     * 发送消息
     */
    private function send_message($member_id,$member_name,$message) {
        $param = array();
        $param['from_member_id'] = 0;
        $param['member_id'] = $member_id;
        $param['to_member_name'] = $member_name;
        $param['message_type'] = '1';//表示为系统消息
        $param['msg_content'] = $message;
        $model_message = Model('message');
        return $model_message->saveMessage($param);
    }

    /**
     * 页面内导航菜单
     *
     * @param string    $menu_key   当前导航的menu_key
     * @param array     $array      附加菜单
     * @return
     */
    private function show_menu($menu_key) {
        $menu_array = array(
            'xianshi_list'=>array('menu_type'=>'link','menu_name'=>Language::get('xianshi_list'),'menu_url'=>'index.php?act=promotion_xianshi&op=xianshi_list'),
            'xianshi_detail'=>array('menu_type'=>'link','menu_name'=>Language::get('xianshi_detail'),'menu_url'=>'index.php?act=promotion_xianshi&op=xianshi_detail'),
            'xianshi_quota'=>array('menu_type'=>'link','menu_name'=>Language::get('xianshi_quota'),'menu_url'=>'index.php?act=promotion_xianshi&op=xianshi_quota'),
            'xianshi_setting'=>array('menu_type'=>'link','menu_name'=>Language::get('xianshi_setting'),'menu_url'=>'index.php?act=promotion_xianshi&op=xianshi_setting'),
        );
        if($menu_key != 'xianshi_detail') unset($menu_array['xianshi_detail']);
        $menu_array[$menu_key]['menu_type'] = 'text';
        Tpl::output('menu',$menu_array);
    }

    /**
     * 商品导出
     */
    public function export_goodsOp()
    {
        $xianshi_id = intval($_GET['xianshi_id']);

        $goods = PXianshiGoods::alias("xg")
            ->leftJoin("dc_product.sku s", "s.id=xg.goods_id")
            ->field("xg.*,s.r1_purchase_price as r1_price")
            ->where("xg.xianshi_id", $xianshi_id)
            ->select();

        $writer = WriterFactory::create(Type::XLSX);
        $writer->openToBrowser(urlencode('限时折扣' . $xianshi_id . '活动商品') . time() . '.xlsx');
        $writer->addRow(['sku', '商品名称', '售价', '活动价', '采购价', '折扣', '限购件数', '是否负毛利', '是否异常折扣']);

        $ed = C('xianshi_exception_discount') ?: 0;

        if (!$goods->isEmpty()) {
            foreach ($goods as $sku) {
                $row = [
                    'sku' => $sku['goods_id'],
                    'name' => $sku['goods_name'],
                    'goods_price' => round($sku['goods_price'], 2),
                    'xianshi_price' => round($sku['xianshi_price'], 2),
                    'r1_price' => round($sku['r1_price'] / 100, 2),
                    'discount' => number_format($sku['xianshi_price'] / $sku['goods_price'] * 10, 1) . '折',
                    'max_limit' => $sku['max_limit'],
                    'purchase_exception' => '否',
                    'price_exception' => '否'
                ];
                if ($sku['r1_price'] > 0 && round($sku['xianshi_price'] * 100) < $sku['r1_price']) {
                    $row['purchase_exception'] = '是';
                }
                if (($sku['xianshi_price'] / $sku['goods_price'] * 10 - $ed) < 0.001) {
                    $row['price_exception'] = '是';
                }
                $writer->addRow($row);
            }
        }
        $writer->close();
    }

    /**
     * 审核
     *
     * @return void
     */
    public function checkOp()
    {
        if (chksubmit()) {
            $xianshi_id = intval($_POST['xianshi_id']);
            if ($xianshi_id <= 0) {
                showDialog('参数错误');
            }

            $xianshi_info = Model('p_xianshi')->getXianshiInfoByID($xianshi_id);
            if (empty($xianshi_info) || $xianshi_info['state'] != -2) {
                showDialog('参数错误');
            }

            if (!$_POST['pass'] && empty(trim($_POST['check_reason']))) {
                showDialog('审核不通过时 审核理由 不能为空');
            }

            if(mb_strlen(trim($_POST['check_reason'])) > 100){
                showDialog('审核理由最多100个字，当前字数为'.mb_strlen(trim($_POST['check_reason'])));
            }

            Model('p_xianshi')->editXianshi([
                'state' => $_POST['pass'] ? 1 : -3,
                'check_reason' => trim($_POST['check_reason'])
            ], ['xianshi_id' => $xianshi_id]);

            if ($_POST['pass']) {
                Model('p_time')->edit([
                    'promotion_type' => 2,
                    'promotion_id' => $xianshi_id
                ],[
                    'state' => 1
                ]);
                $checkMsg = ">审核结果：<font color=\"green\">通过</font>";
            }else{
                $checkMsg = ">审核结果：<font color=\"warning\">不通过</font>
>审核理由：".trim($_POST['check_reason']);
            }

            $this->log('审核限时折扣[ID:'.$xianshi_id.']');

            SendWebHookNotifyQueue::dispatch(
                '5b8561cd-e9f8-450a-84c0-16463a07b08d',
                "## 审核结果提醒 \n> **限时折扣** \n> 活动ID：$xianshi_id\n".$checkMsg
            );

            showDialog(L('nc_common_op_succ'), 'reload', 'succ');
        }

        Tpl::showpage('promotion_xianshi.check', 'null_layout');
    }
}
