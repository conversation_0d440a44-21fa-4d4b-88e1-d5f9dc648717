<link rel="stylesheet" type="text/css" href="<?php echo RESOURCE_SITE_URL;?>/js/jquery-ui/themes/ui-lightness/jquery.ui.css"  />
<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title"><a class="back" href="index.php?act=distri_member&op=serviceApply" title="返回列表"><i class="fa fa-arrow-circle-o-left"></i></a>
      <div class="subject">
        <h3>门店前台报备申请 - 认证详情</h3>
        <h5>分销会员及认证管理</h5>
      </div>
    </div>
  </div>
  <table border="0" cellpadding="0" cellspacing="0" class="store-joinin">
    <thead>
      <tr>
        <th colspan="20">会员详情</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <th class="w150">会员名称：</th>
        <td colspan="20"><?php echo $output['member_info']['member_name'];?></td>
      </tr>
      <tr>
        <th>真实姓名：</th>
        <td><?php echo $output['member_info']['member_truename'];?></td>
        <th>性别：</th>
        <td><?php echo $output['sex_array'][$output['member_info']['member_sex']];?></td>
        <th>注册时间：</th>
        <td><?php echo date('Y-m-d H:i:s',$output['member_info']['member_time']);?></td>
      </tr>
      <tr>        
        <th>绑定手机：</th>
        <td><?php echo ($output['member_info']['member_mobile_bind'] == 1)?$output['member_info']['member_mobile']:'--';?></td>
        <th>绑定邮箱：</th>
        <td><?php echo ($output['member_info']['member_email_bind'] == 1)?$output['member_info']['member_email']:'--';?></td>
        <th>最后登录时间：</th>
        <td><?php echo date('Y-m-d H:i:s',$output['member_info']['member_login_time']);?></td>
      </tr>
    </tbody>
  </table>
  <table border="0" cellpadding="0" cellspacing="0" class="store-joinin">
    <thead>
      <tr>
        <th colspan="20">结算账号信息：</th>
      </tr>
    </thead>
    <tbody>
        <tr>
            <th class="w150">绑定分院：</th>
            <td><?php echo $output['member_info']['chain_name'];?></td>
       </tr>
        <tr>
            <th class="w150">品牌：</th>
            <td><?php echo $output['member_info']['brand_name'];?></td>
        </tr>
        <tr>
            <th class="w150">身份证号：</th>
            <td><span id="id_card"><?php echo $output['member_info']['member_identity'];?></span>
                <?php if ($output['member_info']['distri_state'] == 2){?>
                <a class="ncap-btn-mini ncap-btn-green" onclick="edit_id_card(<?php echo $output['member_info']['member_id'] ?>)">修改</a>
                <?php }?>
            </td>
        </tr>
        <?php if($output['member_info']['distri_state'] == 3){?>
        <tr>
            <th class="w150">审核意见：</th>
            <td><?php echo $output['member_info']['auth_message'];?></td>
        </tr>
        <?php }?>
        <?php if($output['member_info']['distri_state'] == 2 || $output['member_info']['distri_state'] == 3){?>
        <tr>
            <th class="w150">备注：</th>
            <td><span id="auth_desc"><?php echo $output['member_info']['auth_desc'];?></span>
                <?php if ($output['member_info']['distri_state'] == 2){?>
                <a class="ncap-btn-mini ncap-btn-green" onclick="edit_auth_desc(<?php echo $output['member_info']['member_mobile'] ?>)">修改</a>
                <?php }?>
            </td>
        </tr>
        <?php }?>
    </tbody>
  </table>
  
  <form id="form_store_verify" action="index.php?act=distri_member&op=auth&type=1" method="post">
    <input id="verify_type" name="verify_type" type="hidden" />
    <input name="member_id" type="hidden" value="<?php echo $output['member_info']['member_id'];?>" />
    <input name="distri_chainid" type="hidden" value="<?php echo $output['member_info']['distri_chainid'];?>" />
    <?php if(in_array(intval($output['member_info']['distri_state']), array(0,1))) { ?>
    <table border="0" cellpadding="0" cellspacing="0" class="store-joinin">
      <tbody>
        <tr>
          <th>审核意见：</th>
          <td colspan="2"><textarea id="joinin_message" name="joinin_message"></textarea></td>
        </tr>
        <tr>
            <th>备注：</th>
            <td colspan="2"><input id="auth_desc" style="width: 50%;" maxlength="100" placeholder='可用于备注身份证对应的员工姓名' name="auth_desc"></td>
        </tr>
      </tbody>
    </table>
    <div id="validation_message" style="color:red;display:none;"></div>
    <div class="bottom">
      <a id="btn_pass" class="ncap-btn-big ncap-btn-green mr10" href="JavaScript:void(0);">通过</a>
      <a id="btn_fail" class="ncap-btn-big ncap-btn-red" href="JavaScript:void(0);">拒绝</a> 
    </div>
    <?php } ?>
  </form>
</div>
<script type="text/javascript" src="<?php echo ADMIN_RESOURCE_URL;?>/js/jquery.nyroModal.js"></script>
<script type="text/javascript" src="<?php echo RESOURCE_SITE_URL;?>/js/jquery.poshytip.min.js" charset="utf-8"></script>

<script type="text/javascript">
    $(document).ready(function(){
        $('a[nctype="nyroModal"]').nyroModal();

        $('#btn_fail').on('click', function() {
            if($('#joinin_message').val() == '') {
                $('#validation_message').text('请输入审核意见');
                $('#validation_message').show();
                return false;
            } else {
                $('#validation_message').hide();
            }
            if(confirm('确认拒绝申请？')) {
                $('#verify_type').val('fail');
                $('#form_store_verify').submit();
            }
        });
        $('#btn_pass').on('click', function() {
            $('#validation_message').hide();
            if(confirm('确认通过申请？')) {
              $('#verify_type').val('pass');
              $('#form_store_verify').submit();
            } 
        });
    });

    function edit_id_card($id){
        var value = prompt('修改身份证号', '');
        if(value == null){
        }else if(value == ''){
            alert('输入为空，请重新输入！');
        }else{
            var data = {};
            data.id = $id
            data.id_card = value
            $.ajax({
                type:'POST',
                url:'index.php?act=distri_member&op=edit_card',
                cache:false,
                data:data,
                dataType:'json',
                success:function(data){
                    if(data.status == 200){
                        var id_card_obj = document.getElementById("id_card")
                        id_card_obj.innerText = data.id_card
                    }
                    alert(data.msg);
                }
            });
        }
    }

    function edit_auth_desc($mobile){
        var value = prompt('修改备注信息', '');
        if(value == null){
        }else if(value == ''){
            alert('输入为空，请重新输入！');
        }else{
            var data = {};
            data.mobile = $mobile
            data.auth_desc = value
            $.ajax({
                type:'POST',
                url:'index.php?act=distri_member&op=edit_desc',
                cache:false,
                data:data,
                dataType:'json',
                success:function(data){
                    if(data.status == 200){
                        var auth_desc_obj = document.getElementById("auth_desc")
                        auth_desc_obj.innerText = data.auth_desc
                    }
                    alert(data.msg);
                }
            });
        }
    }
</script>