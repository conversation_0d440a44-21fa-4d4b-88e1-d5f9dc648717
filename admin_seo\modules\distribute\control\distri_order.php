<?php
/**
 * 分销-订单管理
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Box\Spout\Common\Type;
use Box\Spout\Writer\Style\StyleBuilder;
use Box\Spout\Writer\WriterFactory;
use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');
class distri_orderControl extends SystemControl{
    const EXPORT_SIZE = 5000;
    private $_links = array(array('url' => 'act=distri_order&op=index', 'text' => '实物分销订单'), array('url' => 'act=distri_order&op=vr_index', 'text' => '虚拟分销订单'), array('url' => 'act=distri_order&op=modify_index', 'text' => '编辑分销订单'));

    function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取分销订单列表
     */
    public function indexOp(){
        $payment_list = Model('payment')->getPaymentOpenList();
        $payment_list['wxpay'] = array(
            'payment_code' => 'wxpay',
            'payment_name' => '微信支付'
        );

        $payment_list['card'] = [
            'payment_code' => 'card',
            'payment_name' => '储值卡'
        ];

        Tpl::output('top_link', $this->sublink($this->_links, 'index'));
        Tpl::output('payment_list',$payment_list);
        Tpl::showpage('distri_order.index');
    }
    /**
     * 获取分销订单列表
     */
    public function vr_indexOp(){
        $payment_list = Model('payment')->getPaymentOpenList();
        $payment_list['wxpay'] = array(
            'payment_code' => 'wxpay',
            'payment_name' => '微信支付'
        );

        $payment_list['card'] = [
            'payment_code' => 'card',
            'payment_name' => '储值卡'
        ];

        Tpl::output('top_link', $this->sublink($this->_links, 'vr_index'));
        Tpl::output('payment_list',$payment_list);
        Tpl::showpage('distri_order.vr_index');
    }
    /**
     * 获取分销订单列表
     */
    public function modify_indexOp(){
        $payment_list = Model('payment')->getPaymentOpenList();
        $payment_list['wxpay'] = array(
            'payment_code' => 'wxpay',
            'payment_name' => '微信支付'
        );

        $payment_list['card'] = [
            'payment_code' => 'card',
            'payment_name' => '储值卡'
        ];

        Tpl::output('top_link', $this->sublink($this->_links, 'modify_index'));
        Tpl::output('payment_list',$payment_list);
        Tpl::showpage('distri_order.modify_index');
    }
    /**
     * 获取订单xml数据
     */
    public function get_xmlOp(){
        $condition = array();
        $model_dis_order = Model('dis_order');
        $model_order = Model('order');

        $condition['order_demolition'] = 0;
        $this->_get_condition($condition);
        if ($_REQUEST['qtype'] =='service_name' && $_REQUEST['query']){
            $member_info = Model('member')->where(['bill_user_name'=>$_REQUEST['query']])->field('member_id')->select();
            $member_ids = array_column($member_info,'member_id');
            $condition['customer_service_id'] = implode(',',$member_ids);
        }
        $sort_fields = array('buyer_name','store_name','order_id','payment_code','order_state','order_amount','order_from','pay_sn','rcb_amount','pd_amount','payment_time','finnshed_time','evaluation_state','refund_amount','buyer_id','store_id');
        if ($_POST['sortorder'] != '' && in_array($_POST['sortname'],$sort_fields)) {
            $order = $_POST['sortname'].' '.$_POST['sortorder'];
        }
        $page = $_POST['rp'];
        $condition['store_id'] = $_SESSION['store_id'];
        $order_list = $model_dis_order->getDisOrderList($condition,$page,'',$order);
        $data = array();
        $data['now_page'] = $model_dis_order->shownowpage();
        $data['total_num'] = $model_dis_order->gettotalnum();

        foreach ($order_list as $order_id => $order_info) {
            $order_info['if_system_cancel'] = $model_order->getOrderOperateState('system_cancel',$order_info);
            $order_info['if_system_receive_pay'] = $model_order->getOrderOperateState('system_receive_pay',$order_info);
            $order_info['state_desc'] = orderState($order_info);
            //取得订单其它扩展信息
            $model_order->getOrderExtendInfo($order_info);
            $list = array();
            $list['operation'] = '--';
            $list['order_sn'] = $order_info['order_sn'].Logic('order')->getOrderTypeText($order_info['order_type']);
            $list['add_times'] = date('Y-m-d H:i:s',$order_info['add_time']);
            $list['order_amount'] = ncPriceFormat($order_info['order_amount']);
            if ($order_info['shipping_fee']) {
                $list['order_amount'] .= '(含运费'.ncPriceFormat($order_info['shipping_fee']).')';
            }
            $list['order_state'] = $order_info['state_desc'];
            $list['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
            $list['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s',$order_info['finnshed_time']) : '';
            $list['store_name'] = $order_info['store_name'];
            $list['buyer_id'] = $order_info['buyer_id'];
            $list['buyer_name'] = $order_info['buyer_name'];
            $list['dis_order_amount'] = ncPriceFormat($order_info['dis_order_amount']);
            $list['dis_member_name'] = $order_info['dis_member_name'];
            $list['chain_name']  = $order_info['chain_name'];
            $list['dis_member_id'] = $order_info['dis_member_id'];
            $list['customer_service_name'] = $order_info['customer_service_name'];
            $list['customer_service_id'] = $order_info['customer_service_id'];
            $data['list'][$order_info['order_id']] = $list;
        }

        exit(Tpl::flexigridXML($data));
    }
    /**
     * 获取订单xml数据
     */
    public function get_modify_xmlOp(){
        if ($_REQUEST['query']==""){
            $data = array();
            $data['now_page'] = 1;
            $data['total_num'] = 0;
            exit(Tpl::flexigridXML($data));
        }

        if (in_array($_REQUEST['qtype'], array('order_sn', 'buyer_phone'))){
            //实物订单
            $condition = array();
            $model_order = Model('order');
            $model_member = Model('member');
            $model_disorder = Model('dis_order');
            $page = $_POST['rp'];
            $condition[$_REQUEST['qtype']] = $_REQUEST['query'];
            $condition['order_state'] = array('in','20,30,40');
            $condition['refund_state'] = 0;
            $condition['is_dis'] = 1;
            $condition['order_father'] = array('gt',0);
            $condition['store_id'] = $_SESSION['store_id'];
            $order_list = $model_order->getOrderList($condition, $page, '*', 'order_id desc', '', array('order_goods', 'chain'));
            //var_dump($order_list);exit;
            $data = array();
            $data['now_page'] = $model_order->shownowpage();
            $data['total_num'] = $model_order->gettotalnum();

            foreach ($order_list as $order_id => $order_info) {
                $list = array();
                $list['order_sn'] = $order_info['order_sn'].Logic('order')->getOrderTypeText($order_info['order_type']);
                $list['add_time'] = date('Y-m-d H:i:s',$order_info['add_time']);
                $list['order_amount'] = ncPriceFormat($order_info['order_amount']);
                $dis_member_id = $dis_chain = $dis_state = $dis_pay_time = '-';
                foreach($order_info['extend_order_goods'] as $key => $val){
                    if ($val['dis_member_id']){
                        $dis_member_id = $val['dis_member_id'];
                        //分销员所属门店
                        $info = $model_member->getDistriMemberInfo(array('member_id'=>$dis_member_id), 'chain_name');
                        if (isset($info[0]['chain_name'])){
                            $dis_chain = $info[0]['chain_name'];
                        }
                        break;
                    }
                }
                $disorder = $model_disorder->getDisPayInfo(array('order_id'=>$order_info['order_id']));
                if ($disorder){
                    $dis_state = $disorder['log_state']==1 ? '已结' : '未结';
                    if ($disorder['dis_pay_time']){
                        $dis_pay_time = date('Y-m-d H:i:s', $disorder['dis_pay_time']);
                    }
                }
                $list['dis_state'] = $dis_state;
                $list['dis_pay_time'] = $dis_pay_time;
                $list['dis_member_id'] = $dis_member_id;
                $list['dis_chain'] = $dis_chain;
                $list['operation'] = "<a class='btn green' href='javascript:void(0)' onclick=\"ajax_form('order_info','绑定分销员', 'index.php?act=distri_order&op=order_modify&order_type=1&order_id=". $order_info['order_id'] ."', 840)\" ><i class='fa fa-list-alt'></i>绑定分销员</a>";
                $data['list'][$order_info['order_id']] = $list;
            }
        }else{
            //虚拟订单
            $condition = array();
            $model_order = Model('vr_order');
            $model_member = Model('member');
            $model_disorder = Model('dis_order');
            $page = $_POST['rp'];
            $condition[str_replace('v_','',$_REQUEST['qtype'])] = $_REQUEST['query'];
            $condition['order_state'] = array('in','20,30,40');
            $condition['is_dis'] = 1;
            $condition['refund_state'] = 0;
            $condition['store_id'] = $_SESSION['store_id'];
            $order_list = $model_order->getOrderList($condition, $page, '*', 'order_id desc');

            //var_dump($order_list);exit;
            $data = array();
            $data['now_page'] = $model_order->shownowpage();
            $data['total_num'] = $model_order->gettotalnum();

            foreach ($order_list as $order_id => $order_info) {
                $list = array();
                $list['order_sn'] = $order_info['order_sn'].Logic('order')->getOrderTypeText($order_info['order_type']);
                $list['add_time'] = date('Y-m-d H:i:s',$order_info['add_time']);
                $list['order_amount'] = ncPriceFormat($order_info['order_amount']);
                $dis_member_id = $dis_chain = $dis_state = $dis_pay_time = '-';
                if ($order_info['dis_member_id']){
                    $dis_member_id = $order_info['dis_member_id'];
                    //分销员所属门店
                    $info = $model_member->getDistriMemberInfo(array('member_id'=>$dis_member_id), 'chain_name');
                    if (isset($info[0]['chain_name'])){
                        $dis_chain = $info[0]['chain_name'];
                    }
                }
                $disorder = $model_disorder->getDisPayInfo(array('order_id'=>$order_info['order_id']));
                if ($disorder){
                    $dis_state = $disorder['log_state']==1 ? '已结' : '未结';
                    if ($disorder['dis_pay_time']){
                        $dis_pay_time = date('Y-m-d H:i:s', $disorder['dis_pay_time']);
                    }
                }
                $list['dis_state'] = $dis_state;
                $list['dis_pay_time'] = $dis_pay_time;
                $list['dis_member_id'] = $dis_member_id;
                $list['dis_chain'] = $dis_chain;
                $list['operation'] = "<a class='btn green' href='javascript:void(0)' onclick=\"ajax_form('order_info','绑定分销员', 'index.php?act=distri_order&op=order_modify&order_type=2&order_id=". $order_info['order_id'] ."', 840)\" ><i class='fa fa-list-alt'></i>绑定分销员</a>";
                $data['list'][$order_info['order_id']] = $list;
            }
        }

        exit(Tpl::flexigridXML($data));
    }
    /**
     * 获取虚拟订单xml数据
     */
    public function get_vr_xmlOp(){
        $condition = array();
        $model_dis_order = Model('dis_order');
        $this->_get_condition($condition);
        if ($_REQUEST['qtype'] =='service_name' && $_REQUEST['query']){
            $member_info = Model('member')->where(['bill_user_name'=>$_REQUEST['query']])->field('member_id')->select();
            $member_ids = array_column($member_info,'member_id');
            $condition['customer_service_id'] = ['in',$member_ids];
        }
        $sort_fields = array('buyer_name','store_name','order_id','payment_code','order_state','order_amount','order_from','pay_sn','rcb_amount','pd_amount','payment_time','finnshed_time','evaluation_state','refund_amount','buyer_id','store_id');
        if ($_POST['sortorder'] != '' && in_array($_POST['sortname'],$sort_fields)) {
            $order = $_POST['sortname'].' '.$_POST['sortorder'];
        }
        $page = $_POST['rp'];
        $condition['store_id'] = $_SESSION['store_id'];
        $order_list = $model_dis_order->getDisVrOrderList($condition,$page,'',$order);
        $data = array();
        $data['now_page'] = $model_dis_order->shownowpage();
        $data['total_num'] = $model_dis_order->gettotalnum();
        foreach ($order_list as $order_id => $order_info) {
            $list = array();
            $list['operation'] = '--';
            $list['order_sn'] = $order_info['order_sn'].Logic('order')->getOrderTypeText($order_info['order_type']);
            $list['add_times'] = date('Y-m-d H:i:s',$order_info['add_time']);
            $list['order_amount'] = ncPriceFormat($order_info['order_amount']);
            if ($order_info['shipping_fee']) {
                $list['order_amount'] .= '(含运费'.ncPriceFormat($order_info['shipping_fee']).')';
            }
            $list['order_state'] = $order_info['state_desc'];
            $list['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
            $list['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s',$order_info['finnshed_time']) : '';
            $list['store_name'] = $order_info['store_name'];
            $list['buyer_id'] = $order_info['buyer_id'];
            $list['buyer_name'] = $order_info['buyer_name'];
            $list['dis_order_amount'] = ncPriceFormat($order_info['dis_order_amount']);
            $list['dis_member_name'] = $order_info['dis_member_name'];
            $list['chain_name']  = $order_info['chain_name'];
            $list['dis_member_id'] = $order_info['dis_member_id'];
            $list['customer_service_name'] = $order_info['customer_service_name'];
            $list['customer_service_id'] = $order_info['customer_service_id']?:'';
            $data['list'][$order_info['order_id']] = $list;
        }
        exit(Tpl::flexigridXML($data));
    }
    /**
     * 查看订单
     *
     */
    public function show_orderOp(){
        $order_id = intval($_GET['order_id']);
        if($order_id <= 0 ){
            showMessage(L('miss_order_number'));
        }
        $model_order    = Model('order');
        $order_info = $model_order->getOrderInfo(array('order_id'=>$order_id),array('order_goods','order_common','store'));

        foreach ($order_info['extend_order_goods'] as $value) {
            $value['image_60_url'] = cthumb($value['goods_image'], 60, $value['store_id']);
            $value['image_240_url'] = cthumb($value['goods_image'], 240, $value['store_id']);
            $value['goods_type_cn'] = orderGoodsType($value['goods_type']);
            $value['goods_url'] = urlShop('goods','index',array('goods_id'=>$value['goods_id']));
            if ($value['goods_type'] == 5) {
                $order_info['zengpin_list'][] = $value;
            } else {
                $order_info['goods_list'][] = $value;
            }
        }

        if (empty($order_info['zengpin_list'])) {
            $order_info['goods_count'] = count($order_info['goods_list']);
        } else {
            $order_info['goods_count'] = count($order_info['goods_list']) + 1;
        }

        //取得订单其它扩展信息
        $model_order->getOrderExtendInfo($order_info);

        //订单变更日志
        $log_list   = $model_order->getOrderLogList(array('order_id'=>$order_info['order_id']));
        Tpl::output('order_log',$log_list);

        //退款退货信息
        $model_refund = Model('refund_return');
        $condition = array();
        $condition['order_id'] = $order_info['order_id'];
        $condition['seller_state'] = 2;
        $condition['admin_time'] = array('gt',0);
        $return_list = $model_refund->getReturnList($condition);
        Tpl::output('return_list',$return_list);

        //退款信息
        $refund_list = $model_refund->getRefundList($condition);
        Tpl::output('refund_list',$refund_list);

        //商家信息
        $store_info = Model('store')->getStoreInfo(array('store_id'=>$order_info['store_id']));
        Tpl::output('store_info',$store_info);

        //商家发货信息
        if (!empty($order_info['extend_order_common']['daddress_id'])) {
            $daddress_info = Model('daddress')->getAddressInfo(array('address_id'=>$order_info['extend_order_common']['daddress_id']));
            Tpl::output('daddress_info',$daddress_info);
        }

        //显示快递信息
        if ($order_info['shipping_code'] != '') {
            $express = rkcache('express',true);
            $order_info['express_info']['e_code'] = $express[$order_info['extend_order_common']['shipping_express_id']]['e_code'];
            $order_info['express_info']['e_name'] = $express[$order_info['extend_order_common']['shipping_express_id']]['e_name'];
            $order_info['express_info']['e_url'] = $express[$order_info['extend_order_common']['shipping_express_id']]['e_url'];
        }

        //如果订单已取消，取得取消原因、时间，操作人
        if ($order_info['order_state'] == ORDER_STATE_CANCEL) {
            $order_info['close_info'] = $model_order->getOrderLogInfo(array('order_id'=>$order_info['order_id'],'log_orderstate'=>ORDER_STATE_CANCEL),'log_id desc');
        }

        //如果订单已支付，取支付日志信息(主要是第三方平台支付单号)
        if ($order_info['order_state'] == ORDER_STATE_PAY) {
            $order_info['pay_info'] = $model_order->getOrderLogInfo(array('order_id'=>$order_info['order_id'],'log_orderstate'=>ORDER_STATE_PAY),'log_id desc');
        }

        Tpl::output('order_info',$order_info);
        Tpl::showpage('order.view');
    }

    /**
     * 导出实物订单数据
     */
    public function export_step1Op(){
        ini_set('memory_limit','512M');
        $lang   = Language::getLangContent();

        set_time_limit(0);
        $model_dis_order = Model('dis_order');
        $model_order = Model('order');

        $condition  = array();
        $condition['is_dis'] = 1;
        if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
            $_GET['order_id'] = explode(',', trim($_GET['order_id'], ','));
            $condition['order_id'] = array('in', $_GET['order_id']);
        }
        $condition['order_demolition'] = 0;
        $this->_get_condition($condition);

        $sort_fields = array('buyer_name','store_name','order_id','payment_code','order_state','order_amount',
            'order_from','pay_sn','rcb_amount','pd_amount','payment_time','finnshed_time','evaluation_state','refund_amount','buyer_id','store_id');
        if ($_POST['sortorder'] != '' && in_array($_POST['sortname'],$sort_fields)) {
            $order = $_POST['sortname'].' '.$_POST['sortorder'];
        } else {
            $order = 'order_id desc';
        }

        if (!is_numeric($_GET['curpage'])){
            $count = $model_order->getOrderCount($condition);
            $array = array();
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=distri_order&op=index');
                Tpl::showpage('export.excel');
            }else{  //如果数量小，直接下载
                $data = $model_dis_order->getDisOrderList($condition,'',self::EXPORT_SIZE,$order);
                $this->createExcel($data);
            }
        }else{  //下载
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $data = $model_dis_order->getDisOrderList($condition,'',"{$limit1},{$limit2}",$order);
            $this->createExcel($data);
        }
    }

    /**
     * 导出实物订单商品数据
     */
    public function export_step2Op(){
        ini_set('memory_limit','512M');
        set_time_limit(0);
        $lang   = Language::getLangContent();
        $model_dis_order = Model('dis_order');
        $condition  = array();
        $condition['orders.is_dis'] = 1;

        if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
            $_GET['order_id'] = explode(',',trim($_GET['order_id'],','));
            //$condition['order_id'] = array('in',$_GET['order_id']);
            $condition['orders.order_id'] = array('in',$_GET['order_id']);
        }
        $condition['orders.order_demolition'] = 0;
        $this->_get_conditionGoods($condition);
        $sort_fields = array('buyer_name','store_name','order_id','payment_code','order_state','order_amount','order_from','pay_sn','rcb_amount','pd_amount','payment_time','finnshed_time','evaluation_state','refund_amount','buyer_id','store_id');
        if ($_POST['sortorder'] != '' && in_array($_POST['sortname'],$sort_fields)) {
            $order = $_POST['sortname'].' '.$_POST['sortorder'];
        } else {
            $order = 'order_id desc';
        }
        $on = 'orders.order_id = order_goods.order_id,orders.order_id = order_common.order_id,order_goods.goods_id=goods.goods_id,order_goods.dis_member_id=member.member_id,orders.chain_id = chain.chain_id';
        $field = "orders.order_id,orders.order_sn,orders.chain_id,orders.order_type,orders.order_from,orders.lock_state,orders.add_time,orders.order_amount,orders.shipping_fee,orders.order_state,orders.chain_code,orders.pay_sn,orders.payment_code,orders.payment_time,orders.rcb_amount,orders.pd_amount,orders.shipping_code,orders.refund_amount,orders.finnshed_time,orders.evaluation_state,orders.store_id,orders.store_name,orders.buyer_id,orders.buyer_name,
        order_goods.goods_id,order_goods.goods_name,order_goods.dis_member_id,order_goods.goods_num,order_goods.goods_pay_price,order_goods.goods_spec,order_common.reciver_name,order_common.reciver_info,goods.goods_serial,
        member.bill_user_name,chain.chain_name,chain.account_id,order_goods.is_dis,order_goods.dis_member_id
        ";

        if (!is_numeric($_GET['curpage'])){
            $count = $model_dis_order->getDisOrderCount($condition,$on);

            $array = array();
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=distri_order&op=index');
                Tpl::showpage('export.excel');
                exit();
            }else{  //如果数量小，直接下载
                $data = $model_dis_order->getOrderListExport($condition,$field,$on,self::EXPORT_SIZE,'orders.order_id desc',self::EXPORT_SIZE,array('order_common'));
                $this->createExcelExport($data);
            }
        }else{  //下载
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $data = $model_dis_order->getOrderListExport($condition,$field,$on,'','orders.order_id desc',"{$limit1},{$limit2}",array('order_common'));
            $this->createExcelExport($data);
        }
    }

    /**
     * 导出实物订单商品数据
     * @param array $data
     */
    private function createExcelExport($data = array()){
        $writer = WriterFactory::create(Type::XLSX);
        $writer->setDefaultRowStyle((new StyleBuilder())->setShouldWrapText(false)->build());//不自动换行
        $writer->openToBrowser('dis_order_goods-'.$_GET['curpage'].'-'.date('Y-m-d-H',time()).'.xlsx');
        $writer->addRow(['订单编号','商品名称','商品货号','订单来源','商品数量','商品实际成交价','商品SKU规格','下单时间','订单金额(元)',
            '运费金额(元)','代金券面额','订单状态','拼团状态','支付单号','支付方式','支付时间','充值卡支付(元)','预存款支付(元)','发货物流单号',
            '退款金额(元)','订单完成时间','是否评价','店铺ID','店铺名称','买家ID','买家账号','收货人','买家手机号','买家收货地址','是否分销',
            '分销员','分销员ID','门店名称','门店财务编码','门店ID']);
        $list = array();
        foreach ((array)$data as $k=>$order_info){
            $list[$k]['order_sn'] = $order_info['order_sn'].Logic('order')->getOrderTypeText($order_info['order_type']);
            $list[$k]['goods_name'] = $order_info['goods_name'].","."数量:".$order_info['goods_num'];
            $list[$k]['goods_serial'] = $order_info['goods_serial'];
            $list[$k]['order_from'] = orderFromName($order_info['order_from']);
            $list[$k]['goods_num'] = $order_info['goods_num'];
            $list[$k]['goods_pay_price'] = $order_info['goods_pay_price'];
            $list[$k]['goods_sku_spec'] = $order_info['goods_spec'];
            $list[$k]['add_time'] = date('Y-m-d H:i:s',$order_info['add_time']);
            $list[$k]['order_amount'] = ncPriceFormat($order_info['order_amount']);
            $list[$k]['shipping_fee'] = ncPriceFormat($order_info['shipping_fee']);
            $list[$k]['voucher_price'] =  $order_info['extend_order_common']['voucher_price']?$order_info['extend_order_common']['voucher_price']:"-";
            $list[$k]['order_state'] =$order_info['order_state'].(intval($order_info['lock_state']) ? "(退款退货中)" : false);
            $_pintuan_state="-";
            if($order_info['order_type']==4){
                $model_pintuan = Model('p_pintuan');
                $_info = $model_pintuan->getOrderInfo(array('order_id'=> $order_info['order_id']));
                $_pintuan_state=$_info['lock_state']?"未成团":"已成团";
            }
            $list[$k]['pintuan_state'] =$_pintuan_state;

            $list[$k]['pay_sn'] = empty($order_info['pay_sn']) ? '' : $order_info['pay_sn'];
            $list[$k]['payment_code'] = orderPaymentName($order_info['payment_code']);
            $list[$k]['payment_time'] = !empty($order_info['payment_time']) ? (intval(date('His',$order_info['payment_time'])) ? date('Y-m-d H:i:s',$order_info['payment_time']) : date('Y-m-d',$order_info['payment_time'])) : '';
            $list[$k]['rcb_amount'] = ncPriceFormat($order_info['rcb_amount']);
            $list[$k]['pd_amount'] = ncPriceFormat($order_info['pd_amount']);
            $list[$k]['shipping_code'] = $order_info['shipping_code'];
            $list[$k]['refund_amount'] = $order_info['refund_amount']>$order_info['order_amount']?ncPriceFormat($order_info['order_amount']):ncPriceFormat($order_info['refund_amount']);
            $list[$k]['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s',$order_info['finnshed_time']) : '';
            $list[$k]['evaluation_state'] = str_replace(array(0,1,2), array('未评价','已评价','未评价'),$order_info['evaluation_state']);
            $list[$k]['store_id'] = $order_info['store_id'];
            $list[$k]['store_name'] = $order_info['store_name'];
            $list[$k]['buyer_id'] = $order_info['buyer_id'];
            $list[$k]['buyer_name'] = $order_info['buyer_name'];
            $list[$k]['receiver_name'] = $order_info['reciver_name'];
            $list[$k]['buyer_phone'] = hideStr($order_info['reciver_info']['mob_phone']);
            $list[$k]['buyer_address'] = $order_info['reciver_info']['address'];
            $list[$k]['is_dis_text'] = $order_info['is_dis']?'是':'否';
            $list[$k]['dis_member_name'] = $order_info['bill_user_name'];
            $list[$k]['dis_member_id'] = $order_info['dis_member_id'];
            $list[$k]['chain_name'] = $order_info['chain_name'];
            $list[$k]['account_id'] = $order_info['account_id'];
            $list[$k]['chain_id'] = $order_info['chain_id'];
        }
        $writer->addRows($list);
        $writer->close();
    }
    /**
     * 生成excel
     *
     * @param array $data
     */
    private function createExcel($data = array()){
        $writer = WriterFactory::create(Type::XLSX);
        $writer->setDefaultRowStyle((new StyleBuilder())->setShouldWrapText(false)->build());//不自动换行
        $writer->openToBrowser('distri_order-'.$_GET['curpage'].'-'.date('Y-m-d-H',time()).'.xlsx');
        $writer->addRow(['订单编号','下单时间','订单金额(元)','订单运费(元)','订单状态','退款金额(元)','订单完成时间','店铺名称','买家ID','买家账号','订单分销金额','门店名称','门店ID','分销员','分销员ID','客服','客服ID','分销状态']);
        $list = array();
        foreach ((array)$data as $k=>$order_info){
            $order_info['state_desc'] = orderState($order_info);
            $list[$k]['order_sn'] = $order_info['order_sn'].Logic('order')->getOrderTypeText($order_info['order_type']);
            $list[$k]['add_time'] = date('Y-m-d H:i:s',$order_info['add_time']);
            $list[$k]['order_amount'] = ncPriceFormat($order_info['order_amount']);
            $list[$k]['shipping_fee'] = ncPriceFormat($order_info['shipping_fee']);
            $list[$k]['order_state'] = $order_info['state_desc'];
            $list[$k]['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
            $list[$k]['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s',$order_info['finnshed_time']) : '';
            $list[$k]['store_name'] = $order_info['store_name'];
            $list[$k]['buyer_id'] = $order_info['buyer_id'];
            $list[$k]['buyer_name'] = $order_info['buyer_name'];
            $list[$k]['dis_order_amount'] = ncPriceFormat($order_info['dis_order_amount']);
            $list[$k]['chain_name'] = $order_info['chain_name'];
            $list[$k]['chain_id'] = $order_info['chain_id'];
            $list[$k]['dis_member_name'] = $order_info['dis_member_name'];
            $list[$k]['dis_member_id'] = $order_info['dis_member_id'];
            $list[$k]['customer_service_name'] = $order_info['customer_service_name'];
            $list[$k]['customer_service_id'] = $order_info['customer_service_id'];
            $list[$k]['dis_pay_state_text'] = $order_info['dis_pay_state_text'];
        }
        $writer->addRows($list);
        $writer->close();
    }

    /**
     * 导出虚拟订单数据
     *
     */
    public function vr_export_step1Op(){
        $lang   = Language::getLangContent();

        $model_dis_order = Model('dis_order');
        $model_order = Model('vr_order');

        $condition  = array();
        $condition['is_dis'] = 1;
        if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
            $_GET['order_id'] = explode(',', trim($_GET['order_id'], ','));
            $condition['order_id'] = array('in', $_GET['order_id']);
        }
        $this->_get_condition($condition);

        $sort_fields = array('buyer_name','store_name','order_id','payment_code','order_state','order_amount','order_from','pay_sn','rcb_amount','pd_amount','payment_time','finnshed_time','evaluation_state','refund_amount','buyer_id','store_id');
        if ($_POST['sortorder'] != '' && in_array($_POST['sortname'],$sort_fields)) {
            $order = $_POST['sortname'].' '.$_POST['sortorder'];
        } else {
            $order = 'order_id desc';
        }

        if (!is_numeric($_GET['curpage'])){
            $count = $model_order->getOrderCount($condition);
            $array = array();
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=distri_order&op=index');
                Tpl::showpage('export.excel');
            }else{  //如果数量小，直接下载
                $data = $model_dis_order->getDisVrOrderList($condition,'',self::EXPORT_SIZE,$order);
                $this->vrCreateExcel($data);
            }
        }else{  //下载
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $data = $model_dis_order->getDisVrOrderList($condition,'',"{$limit1},{$limit2}",$order);
            $this->vrCreateExcel($data);
        }
    }

    /**
     * 导出虚拟订单商品数据
     *
     */
    public function vr_export_step2Op(){

        //set_time_limit(0);

        $lang   = Language::getLangContent();
        $model_order = Model('vr_order');
        $model_dis_order = Model('dis_order');
        $condition  = array();
        $condition['vr_order.is_dis'] = 1;
        if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
            $_GET['order_id'] = explode(',',trim($_GET['order_id'],','));
            $condition['vr_order.order_id'] = array('in',$_GET['order_id']);
        }
        $this->_get_vr_conditionGoods($condition);

        $on = 'vr_order.goods_id=goods.goods_id';
        $field = "vr_order.order_id,vr_order.order_sn,vr_order.goods_num,vr_order.goods_name,vr_order.trade_no,vr_order.evaluation_state,vr_order.dis_member_id,vr_order.chain_id,vr_order.order_from,vr_order.add_time,vr_order.order_amount,vr_order.order_state,vr_order.payment_code,vr_order.payment_time,
        vr_order.rcb_amount,vr_order.pd_amount,vr_order.refund_amount,vr_order.finnshed_time,vr_order.evaluation_state,vr_order.store_id,vr_order.store_name,vr_order.buyer_id,vr_order.buyer_name,
       goods.goods_serial";

        if (!is_numeric($_GET['curpage'])){
            $count = $model_order->getOrderCount($condition);
            $array = array();
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=distri_order&op=index');
                Tpl::showpage('export.excel');
                exit();
            }else{  //如果数量小，直接下载
                $data = $model_dis_order->getVrOrderListExport($condition,$field,$on,self::EXPORT_SIZE,'vr_order.order_id desc',self::EXPORT_SIZE);
                $this->vrCreateExcelExport($data);
            }
        }else{  //下载
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $data = $model_dis_order->getVrOrderListExport($condition,$field,$on,'','vr_order.order_id desc',"{$limit1},{$limit2}");
            $this->vrCreateExcelExport($data);
        }

    }
    /**
     * 导出虚拟订单商品数据
     * @param array $data
     */
    private function vrCreateExcelExport($data = array()){
        $writer = WriterFactory::create(Type::XLSX);
        $writer->setDefaultRowStyle((new StyleBuilder())->setShouldWrapText(false)->build());//不自动换行
        $writer->openToBrowser('团餐分销订单-'.$_GET['curpage'].'-'.date('Y-m-d-H',time()).'.xlsx');
        $writer->addRow(['订单编号','商品名称','商品货号','订单来源','商品数量','下单时间','订单金额(元)','订单状态','拼团状态','支付单号',
            '支付方式','支付时间','充值卡支付(元)','预存款支付(元)','退款金额(元)','订单完成时间','是否评价','店铺ID','店铺名称','买家ID',
            '买家账号','门店名称','门店ID','分销员','分销员ID']);
        $list = array();
        foreach ((array)$data as $k=>$order_info){
            $list[$k]['order_sn'] = $order_info['order_sn'].Logic('order')->getOrderTypeText($order_info['order_type']);
            $list[$k]['goods_name'] = $order_info['goods_name'].","."数量:".$order_info['goods_num'];
            $list[$k]['goods_serial'] = $order_info['goods_serial'];
            $list[$k]['order_from'] = orderFromName($order_info['order_from']);
            $list[$k]['goods_num'] = $order_info['goods_num'];
            $list[$k]['add_time'] = date('Y-m-d H:i:s',$order_info['add_time']);
            $list[$k]['order_amount'] = ncPriceFormat($order_info['order_amount']);
            $list[$k]['order_state'] =$order_info['order_state'].(intval($order_info['lock_state']) ? "(退款退货中)" : false);
            $_pintuan_state="-";
            if($order_info['order_type']==4){
                $model_pintuan = Model('p_pintuan');
                $_info = $model_pintuan->getOrderInfo(array('order_id'=> $order_info['order_id']));
                $_pintuan_state=$_info['lock_state']?"未成团":"已成团";
            }
            $list[$k]['pintuan_state'] =$_pintuan_state;
            $list[$k]['trade_no'] = empty($order_info['trade_no']) ? '' : $order_info['trade_no'];
            $list[$k]['payment_code'] = orderPaymentName($order_info['payment_code']);
            $list[$k]['payment_time'] = !empty($order_info['payment_time']) ? (intval(date('His',$order_info['payment_time'])) ? date('Y-m-d H:i:s',$order_info['payment_time']) : date('Y-m-d',$order_info['payment_time'])) : '';
            $list[$k]['rcb_amount'] = ncPriceFormat($order_info['rcb_amount']);
            $list[$k]['pd_amount'] = ncPriceFormat($order_info['pd_amount']);
            $list[$k]['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
            $list[$k]['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s',$order_info['finnshed_time']) : '';
            $list[$k]['evaluation_state'] = str_replace(array(0,1,2), array('未评价','已评价','未评价'),$order_info['evaluation_state']);
            $list[$k]['store_id'] = $order_info['store_id'];
            $list[$k]['store_name'] = $order_info['store_name'];
            $list[$k]['buyer_id'] = $order_info['buyer_id'];
            $list[$k]['buyer_name'] = $order_info['buyer_name'];

            if($order_info['chain_id'] >0){
                $chain_info = Model('chain')->getChainInfo(['chain_id'=>$order_info['chain_id']],'chain_name');
                $list[$k]['chain_name'] = $chain_info['chain_name'];
            }
            $list[$k]['chain_id'] = $order_info['chain_id'];
            $list[$k]['dis_member_name'] = $order_info['dis_member_name'];
            $list[$k]['dis_member_id'] = $order_info['dis_member_id'];
        }
        $writer->addRows($list);
        $writer->close();
    }

    /**
     * 生成excel
     *
     * @param array $data
     */
    private function vrCreateExcel($data = array()){
        $writer = WriterFactory::create(Type::XLSX);
        $writer->setDefaultRowStyle((new StyleBuilder())->setShouldWrapText(false)->build());//不自动换行
        $writer->openToBrowser('团餐分销订单-'.$_GET['curpage'].'-'.date('Y-m-d-H',time()).'.xlsx');
        $writer->addRow(['订单编号','下单时间','订单金额(元)','订单状态','退款金额(元)','订单完成时间','店铺名称','买家ID','买家账号','订单分销金额','门店名称','门店ID','分销员','分销员ID','客服','客服ID','分销状态']);
        $list = array();
        foreach ((array)$data as $k=>$order_info){
            $list[$k]['order_sn'] = $order_info['order_sn'].Logic('order')->getOrderTypeText($order_info['order_type']);
            $list[$k]['add_time'] = date('Y-m-d H:i:s',$order_info['add_time']);
            $list[$k]['order_amount'] = ncPriceFormat($order_info['order_amount']);
            $list[$k]['order_state'] = $order_info['state_desc'];
            $list[$k]['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
            $list[$k]['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s',$order_info['finnshed_time']) : '';
            $list[$k]['store_name'] = $order_info['store_name'];
            $list[$k]['buyer_id'] = $order_info['buyer_id'];
            $list[$k]['buyer_name'] = $order_info['buyer_name'];
            $list[$k]['dis_order_amount'] = ncPriceFormat($order_info['dis_order_amount']);
            $list[$k]['chain_name'] = $order_info['chain_name'];
            $list[$k]['chain_id'] = $order_info['chain_id'];
            $list[$k]['dis_member_name'] = $order_info['dis_member_name'];
            $list[$k]['dis_member_id'] = $order_info['dis_member_id'];
            $list[$k]['customer_service_name'] = $order_info['customer_service_name'];
            $list[$k]['customer_service_id'] = $order_info['customer_service_id']?:'';
            $list[$k]['dis_pay_state_text'] = $order_info['dis_pay_state_text'];
        }
        $writer->addRows($list);
        $writer->close();
    }

    /**
     * 处理搜索条件
     */
    private function _get_condition(&$condition) {
        if ($_REQUEST['query'] != '' && in_array($_REQUEST['qtype'],array('order_sn','store_name','buyer_name','pay_sn','buyer_phone','dis_member_id'))) {
            $condition[$_REQUEST['qtype']] = $_REQUEST['query'];
        }
        if ($_GET['keyword'] != '' && in_array($_GET['keyword_type'],array('order_sn','store_name','buyer_name','pay_sn','buyer_phone','shipping_code','dis_member_id'))) {
            if ($_GET['jq_query'] || $_GET['keyword_type'] == 'dis_member_id') {
                $condition[$_GET['keyword_type']] = $_GET['keyword'];
            } else {
                $condition[$_GET['keyword_type']] = array('like',"%{$_GET['keyword']}%");
            }
        }
        if (!in_array($_GET['qtype_time'],array('add_time','payment_time','finnshed_time'))) {
            $_GET['qtype_time'] = null;
        }
        $if_start_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_start_date']);
        $if_end_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_end_date']);
        $start_unixtime = $if_start_time ? strtotime($_GET['query_start_date']) : null;
        $end_unixtime = $if_end_time ? strtotime($_GET['query_end_date']): null;
        if ($_GET['qtype_time'] && ($start_unixtime || $end_unixtime)) {
            $condition[$_GET['qtype_time']] = array('time',array($start_unixtime,$end_unixtime));
        }
        if($_GET['payment_code']) {
            if ($_GET['payment_code'] == 'wxpay') {
                $condition['payment_code'] = array('in',array('wxpay','wx_saoma','wx_jsapi'));
            } elseif($_GET['payment_code'] == 'alipay') {
                $condition['payment_code'] = array('in',array('alipay','ali_native'));
            } else {
                $condition['payment_code'] = $_GET['payment_code'];
            }
        }
        if(in_array($_GET['order_state'],array('0','10','20','30','40'))){
            $condition['order_state'] = $_GET['order_state'];
        }
        if (!in_array($_GET['query_amount'],array('order_amount','shipping_fee','refund_amount'))) {
            $_GET['query_amount'] = null;
        }
        if (floatval($_GET['query_start_amount']) > 0 && floatval($_GET['query_end_amount']) > 0 && $_GET['query_amount']) {
            $condition[$_GET['query_amount']] = array('between',floatval($_GET['query_start_amount']).','.floatval($_GET['query_end_amount']));
        }
        if(in_array($_GET['order_from'],array('1','2'))){
            $condition['order_from'] = $_GET['order_from'];
        }
    }

    /**
     * 处理虚拟搜索条件
     */
    private function _get_vr_conditionGoods(& $condition) {
        if ($_REQUEST['query'] != '' && in_array($_REQUEST['qtype'],array('order_sn','store_name','buyer_name','pay_sn','dis_member_id'))) {
            $condition['vr_order.'.$_REQUEST['qtype']] = array('like',"%{$_REQUEST['query']}%");
        }
        if ($_GET['keyword'] != '' && in_array($_GET['keyword_type'],array('order_sn','store_name','buyer_name','pay_sn','shipping_code','dis_member_id'))) {
            if ($_GET['jq_query'] || $_GET['keyword_type'] == 'dis_member_id') {
                $condition['vr_order.'.$_GET['keyword_type']] = $_GET['keyword'];
            } else {
                $condition['vr_order.'.$_GET['keyword_type']] = array('like',"%{$_GET['keyword']}%");
            }
        }
        if (!in_array($_GET['qtype_time'],array('add_time','payment_time','finnshed_time'))) {
            $_GET['qtype_time'] = null;
        }
        $if_start_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_start_date']);
        $if_end_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_end_date']);
        $start_unixtime = $if_start_time ? strtotime($_GET['query_start_date']) : null;
        $end_unixtime = $if_end_time ? strtotime($_GET['query_end_date']): null;
        if ($_GET['qtype_time'] && ($start_unixtime || $end_unixtime)) {
            $condition['vr_order.'.$_GET['qtype_time']] = array('time',array($start_unixtime,$end_unixtime));
        }
        if($_GET['payment_code']) {
            if ($_GET['payment_code'] == 'wxpay') {
                $condition['vr_order.payment_code'] = array('in',array('wxpay','wx_saoma','wx_jsapi'));
            } elseif($_GET['vr_order.payment_code'] == 'alipay') {
                $condition['vr_order.payment_code'] = array('in',array('alipay','ali_native'));
            } else {
                $condition['vr_order.payment_code'] = $_GET['payment_code'];
            }
        }
        if(in_array($_GET['order_state'],array('0','10','20','30','40'))){
            $condition['vr_order.order_state'] = $_GET['order_state'];
        }
        if (!in_array($_GET['query_amount'],array('order_amount','shipping_fee','refund_amount'))) {
            $_GET['query_amount'] = null;
        }
        if (floatval($_GET['query_start_amount']) >= 0 && floatval($_GET['query_end_amount']) >= 0 && $_GET['query_amount']) {
            $condition['vr_order.'.$_GET['query_amount']] = array('between',floatval($_GET['query_start_amount']).','.floatval($_GET['query_end_amount']));
        }
        if(in_array($_GET['order_from'],array('1','2','3','4','5','6','7','8'))){
            $condition['vr_order.order_from'] = $_GET['order_from'];
        }
        $condition['vr_order.store_id'] = $_SESSION['store_id'];
    }
    /**
     * 处理搜索条件
     */
    private function _get_conditionGoods(& $condition) {

        if ($_REQUEST['query'] != '' && in_array($_REQUEST['qtype'],array('order_sn','store_name','buyer_name','pay_sn','dis_member_id'))) {
            if($_REQUEST['qtype'] == 'dis_member_id'){
                $condition['order_goods.'.$_REQUEST['qtype']] = $_REQUEST['query'];
            }else{
                $condition['orders.'.$_REQUEST['qtype']] = array('like',"%{$_REQUEST['query']}%");
            }
        }
        if ($_GET['keyword'] != '' && in_array($_GET['keyword_type'],array('order_sn','store_name','buyer_name','pay_sn','shipping_code','dis_member_id'))) {
            if($_GET['keyword_type'] == 'dis_member_id'){
                $condition['order_goods.'.$_GET['keyword_type']] = $_GET['keyword'];
            }else if ($_GET['jq_query']) {
                $condition['orders.'.$_GET['keyword_type']] = $_GET['keyword'];
            } else {
                $condition['orders.'.$_GET['keyword_type']] = array('like',"%{$_GET['keyword']}%");
            }
        }
        if (!in_array($_GET['qtype_time'],array('add_time','payment_time','finnshed_time'))) {
            $_GET['qtype_time'] = null;
        }
        $if_start_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_start_date']);
        $if_end_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_end_date']);
        $start_unixtime = $if_start_time ? strtotime($_GET['query_start_date']) : null;
        $end_unixtime = $if_end_time ? strtotime($_GET['query_end_date']): null;
        if ($_GET['qtype_time'] && ($start_unixtime || $end_unixtime)) {
            $condition['orders.'.$_GET['qtype_time']] = array('time',array($start_unixtime,$end_unixtime));
        }
        if($_GET['payment_code']) {
            if ($_GET['payment_code'] == 'wxpay') {
                $condition['orders.payment_code'] = array('in',array('wxpay','wx_saoma','wx_jsapi'));
            } elseif($_GET['orders.payment_code'] == 'alipay') {
                $condition['orders.payment_code'] = array('in',array('alipay','ali_native'));
            } else {
                $condition['orders.payment_code'] = $_GET['payment_code'];
            }
        }
        if(in_array($_GET['order_state'],array('0','10','20','30','40'))){
            $condition['orders.order_state'] = $_GET['order_state'];
        }
        if (!in_array($_GET['query_amount'],array('order_amount','shipping_fee','refund_amount'))) {
            $_GET['query_amount'] = null;
        }
        if (floatval($_GET['query_start_amount']) >= 0 && floatval($_GET['query_end_amount']) >= 0 && $_GET['query_amount']) {
            $condition['orders.'.$_GET['query_amount']] = array('between',floatval($_GET['query_start_amount']).','.floatval($_GET['query_end_amount']));
        }
        if(in_array($_GET['order_from'],array('1','2','3','4','5','6','7','8'))){
            $condition['orders.order_from'] = $_GET['order_from'];
        }
    }

    /**
     * 编辑绑定分销员
     */
    public function order_modifyOp(){
        if ($_POST['form_submit']=='ok'){
            //提交
            $order_id = intval($_POST['order_id']);
            $order_type = intval($_POST['order_type']);
            $dis_name_type = $_POST['dis_name_type'];
            $dis_name = $_POST['dis_name'];
            $model_member = Model('member');
            $model_pay = Model('dis_bill');
            $model_modify_log = Model('dis_modify_log');
            $condition = array();
            $condition['order_id'] = $order_id;
            $condition['order_state'] = array('in','20,30,40');
            //$condition['is_dis'] = 1;
            if ($order_type==1){ //1:实物 2:虚拟
                $model_order = Model('order');
                $data_pay = $model_order->getOrderInfo($condition, array('order_goods'));
                if ($data_pay){
                    $data_pay['dis_member_id'] = $data_pay['extend_order_goods'][0]['dis_member_id'];
                }
            }else{
                $model_order = Model('vr_order');
                $data_pay = $model_order->getOrderInfo($condition);
            }
            $condition = array();
            $field = ($dis_name_type == 1 ? 'member_mobile' : 'member_id');
            $condition[$field] = $dis_name;
            $condition['distri_state'] = 2;
            $data_n = $model_member->getDistriMemberInfo($condition, 'member_id,member_name,member_mobile,chain_name');
            if (!$data_n){
                showMessage('新分销员信息有误！','index.php?act=distri_order&op=modify_index','','error');
            }

            if (!$data_pay){
                showMessage('参数错误！','index.php?act=distri_order&op=modify_index','','error');
            }

            if ($data_pay['dis_member_id']==$data_n[0]['member_id']){
                showMessage('新分销员不能与老分销员相同！','index.php?act=distri_order&op=modify_index','','error');
            }
            $condition = array();
            $condition['member_id'] = $data_pay['dis_member_id'];
            $data_o = $model_member->getDistriMemberInfo($condition, 'member_id,member_name,member_mobile,chain_name');
            //修改日志
            $info['order_id']  = $data_pay['order_id'];
            $info['order_sn']  = $data_pay['order_sn'];
            if ($order_type==2){
                $goods = $model_order->getOrderInfo(array('order_id'=>$order_id), 'goods_name, order_amount, dis_commis_rate, dis_member_id, buyer_id');
                if ($goods){
                    $info['goods_name'] = $goods['goods_name'];
                    $info['dis_pay_amount'] = ncPriceFormat($goods['order_amount']*$goods['dis_commis_rate']/100);
                    $info['pay_goods_amount'] = $goods['order_amount'];
                    $buyer_id = $goods['buyer_id'];
                }
            }else{
                $goods = $model_order->getOrderGoodsList(array('order_id'=>$order_id), 'goods_name, goods_pay_price, dis_commis_rate, dis_member_id, buyer_id');
                if ($goods){
                    $goods_name = [];
                    $dis_pay_amount = $pay_goods_amount = 0;
                    foreach($goods as $key => $val){
                        $goods_name[] = $val['goods_name'];
                        $dis_pay_amount += ncPriceFormat($val['goods_pay_price']*$val['dis_commis_rate']/100);
                        $pay_goods_amount += $val['goods_pay_price'];
                        $buyer_id = $val['buyer_id'];
                    }
                    $info['dis_pay_amount'] = $dis_pay_amount;
                    $info['goods_name'] = implode('|',$goods_name);
                    $info['pay_goods_amount'] = $pay_goods_amount;
                }

            }
            $info['store_id'] = $_SESSION['store_id'];
            $info['is_virtual']  = ($order_type==1 ? 1 : 0);
            $info['op_id']   = $this->admin_info['id'];
            $info['op_name'] = $this->admin_info['name'];
            $info['dis_o_memberid']  = $data_pay['dis_member_id'];
            if ($data_o){
                $info['dis_o_chain'] = $data_o[0]['chain_name'];
            }
            $info['dis_n_memberid']  = $data_n[0]['member_id'];
            $info['dis_n_chain'] = $data_n[0]['chain_name'];
            $info['op_date'] = time();
            for ($i=1;$i <= 3;$i++) {
                $file = 'pic'.$i;
                if (!empty($_FILES[$file]['name'])) {//上传图片
                    $filename_tmparr = explode('.', $_FILES[$file]['name']);
                    $ext = end($filename_tmparr);
                    $file_name = "distri_modify_{$order_id}_{$info['op_date']}_{$i}.{$ext}";
                    $upload = new UploadFile();
                    $upload->set('default_dir', ATTACH_PATH.'/distri');
                    $upload->set('file_name', $file_name);
                    $upload->set('max_size', 3072);
                    $upload->set('allow_type', array('gif','jpg','jpeg','png'));
                    $result = $upload->upfile($file);
                    if ($result) {
                        $info['image'.$i] = $file_name;
                    }
                    else{
                        showMessage($upload->get('error'),'index.php?act=distri_order&op=modify_index');
                    }
                }
            }
            $model_modify_log->beginTransaction();
            $result = $model_modify_log->table('dis_modify_log')->insert($info);
            if (!$result){
                $model_modify_log->rollback();
                showMessage('修改失败！','index.php?act=distri_order&op=modify_index');
            }else{
                //更改分销信息 TODO
                $dis_pay = $model_pay->table('dis_pay')->where(array('order_id'=>$info['order_id'], 'log_state'=>1))->sum('dis_pay_amount');
                if ($dis_pay){
                    $data_trad['trad_amount'] = array('exp', "if(trad_amount>$dis_pay,trad_amount-$dis_pay,0)");
                    $updateMem = Model('member')->editMember(array('member_id' => $info['dis_o_memberid']), $data_trad);
                    $updatePay = $model_pay->table('dis_pay')->where(array('order_id'=>$info['order_id']))->update(array('dis_pay_time'=>0, 'log_state'=>0));
                    if (!$updateMem || !$updatePay){
                        $model_modify_log->rollback();
                        showMessage('修改失败！','index.php?act=modify_index');
                    }
                }
                //订单修改
                $errMsg = '';
                if ($order_type==2){
                    $resultOrder = $model_modify_log->updateVrOrderOp($data_pay['order_sn'], $buyer_id, $data_n[0]['member_mobile'], '', $errMsg);
                }else{
                    $resultOrder = $model_modify_log->updateOrderOp($data_pay['order_sn'], $buyer_id, $data_n[0]['member_mobile'], '', $errMsg);
                }
                if ($resultOrder){
                    $model_modify_log->commit();
                    showMessage('修改成功！','index.php?act=distri_order&op=modify_index');
                }
                $model_modify_log->rollback();
                showMessage(($errMsg ? $errMsg : '修改失败！'),'index.php?act=distri_order&op=modify_index');
            }
        }
        $order_id = intval($_GET['order_id']);
        $order_type = intval($_GET['order_type']);
        Tpl::output('order_type', $order_type);
        Tpl::output('order_id', $order_id);
        Tpl::showpage('distri_order.modify', 'null_layout');
    }
}