<?php
defined('InShopNC') or exit('Access Invalid!');
/**
 * control
 */
$lang['circle_inform_untreated']	= '未处理';
$lang['circle_inform_treated']		= '已处理';
$lang['circle_inform_reply_title']	= '的回复';
$lang['circle_inform_have_been_reported']	= '该话题或回复已经被举报过了。';
$lang['circle_inform_rewards']		= '奖惩';
$lang['circle_inform_exp']			= '经验';

$lang['circle_inform_info']			= '举报详情';
$lang['circle_informer']			= '举报人';
$lang['circle_handle']				= '处理';
$lang['circle_handle_result']		= '处理结果';
$lang['circle_handel_state']		= '处理状态';
$lang['circle_inform_url']			= '举报链接';
$lang['circle_inform_content']		= '举报内容';
$lang['circle_rewards']				= '奖惩';
$lang['circle_handler']				= '处理人';
$lang['circle_not_rewards']			= '无奖惩';
$lang['circle_no_rewards']			= '不奖惩';
$lang['circle_message']				= '留言';
$lang['circle_come_from']			= '来自';
$lang['circle_name']				= '圈子名称';
$lang['nc_quote1']          = '“';
$lang['nc_quote2']          = '”';
$lang['circle_theme']				= '话题';