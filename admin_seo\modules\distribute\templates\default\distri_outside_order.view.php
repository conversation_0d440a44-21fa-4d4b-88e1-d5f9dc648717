<?php defined('InShopNC') or exit('Access Invalid!');?>
  <div class="ncap-form-default">
    <dl class="row">
      <dt class="tit">
        <label>订单编号</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['order_sn']; ?>
        <p class="notic"></p>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit">
        <label>下单时间</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['add_time']; ?>
        <p class="notic"></p>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit">
        <label>订单金额</label>
      </dt>
      <dd class="opt"><?php if(isset($output['info']['shipping_fee'])){ echo $output['info']['order_amount'].'(含运费'.$output['info']['shipping_fee'].')';}else{echo $output['info']['order_amount'];} ?>&nbsp;<?php echo $lang['currency_zh'];?>
        <p class="notic"></p>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit">
        <label>业绩金额</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['dis_goods_amount']?$output['info']['dis_goods_amount']:$output['info']['order_amount']; ?>
        <p class="notic"></p>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit">
        <label>店铺名称</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['store_name']; ?>
        <p class="notic"></p>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit">
        <label>买家帐号</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['buyer_name']; ?>
        <p class="notic"></p>
      </dd>
    </dl>

    <dl class="row">
      <dt class="tit">
        <label>分销员</label>
      </dt>
      <dd class="opt"> <?php echo $output['info']['dis_member_name'];?>
        <p class="notic"></p>
      </dd>
    </dl>


  </div>


