<?php

use Shopnc\Tpl;
use Upet\Models\BasedataRelation;
use Upet\Models\DcProduct\Product;
use Upet\Models\Goods;
use Upet\Models\GoodsCommon;

defined('InShopNC') or exit('Access Invalid!');

class goods_manage_handelControl extends SystemControl
{
    private $links = array(
        array('url' => 'act=goods_manage_handel&op=index', 'text' => '更新商品分类'),
        array('url' => 'act=goods_manage_handel&op=drop_goods', 'text' => '删除商品'),
    );

    public function __construct()
    {
        parent::__construct();
        Tpl::output('top_title', '商品问题处理');
        Tpl::output('top_desc', '针对异常的商品问题处理');
        Tpl::output('top_link', $this->sublink($this->links, $_GET['op']));
    }

    /**
     * Notes:订单延迟收货
     * User: rocky
     * DateTime: 2022/1/7 15:06
     */
    public function indexOp()
    {
        if ($_POST['sku_no'] && $_POST['cate_name']) {
            $catelike = htmlspecialchars_decode($_POST['cate_name']);
            $sku_no = $_POST['sku_no'];
            $cate_info = product::where('category_name', 'like', "%{$catelike}")->field('category_id,category_name')->find();
            if ($cate_info) {
                $res = product::alias('p')
                    ->join('dc_product.sku_third st', 'st.product_id = p.id', 'inner')
                    ->where(['st.third_sku_id' => $sku_no])
                    ->update(['p.category_id' => $cate_info['category_id'], 'p.category_name' => $cate_info['category_name']]);
                if (!$res) {
                    showMessage('无更新操作');
                }
                showMessage('操作成功', '');
            } else {
                showMessage('查找不到分类');
            }
        }
        Tpl::showpage('goods_manage_handel');
    }

    //删除异常商品，重新认领
    public function drop_goodsOp(){
        if ($_POST['sku_no']) {
            $store_id = $_SESSION['store_id'];
            $sku_no = $_POST['sku_no'];
            if (!$sku_no) {
                showMessage('参数错误');
            }
            $goods_info = Goods::where(['goods_sku' => $sku_no,'store_id'=>$store_id])->find();

            if (empty($goods_info)) {
                showMessage('查询无记录');
            }
            $where =[];
            $where['goods_commonid'] = $goods_info['goods_commonid'];
            $where['store_id'] = $store_id;
            Goods::where($where)->delete();
            GoodsCommon::where($where)->delete();
//            BasedataRelation::where(['data_id' => $goods_commonid])->delete();
            showMessage('操作成功');
        }
        Tpl::showpage('drop_goods');
    }

}