<?php
/**
 * 默认展示页面
 *
 * 默认展示页面
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');

class indexControl extends SystemControl{
    public function __construct(){
        parent::__construct();
        Language::read('index');
    }
    public function indexOp(){
        //输出管理员信息
        Tpl::output('admin_info',$this->getAdminInfo());
        Tpl::output('store',$this->getStoreInfo());
        //输出菜单
        $result = $this->getNav();
        list($top_nav, $left_nav, $map_nav, $quicklink) = $result;
        Tpl::output('top_nav',$top_nav);
        Tpl::output('left_nav',$left_nav);
        Tpl::output('map_nav',$map_nav);
        // 快捷菜单
        Tpl::output('quicklink', $quicklink);

        Tpl::showpage('index','index_layout');
    }
    // 切换管理员店铺
    public function storeids_changeOp(){
        $store_id = intval($_POST['store_id']);
        $_SESSION['store_id'] = $store_id;
        exit(json_encode(array('state'=>true,'msg'=> $store_id)));

    }
    /**
     * 退出
     */
    public function logoutOp(){
        setNcCookie('sys_key','',-1,'',null);
        @header("Location: index.php");
        exit;
    }
    /**
     * 修改密码
     */
    public function modifypwOp(){
        if (chksubmit()){
            if (trim($_POST['new_pw']) !== trim($_POST['new_pw2'])){
                //showMessage('两次输入的密码不一致，请重新输入');
                showMessage(Language::get('index_modifypw_repeat_error'));
            }
            $admininfo = $this->getAdminInfo();
            //查询管理员信息
            $admin_model = Model('admin');
            $admininfo = $admin_model->getOneAdmin($admininfo['id']);
            if (!is_array($admininfo) || count($admininfo)<= 0){
                showMessage(Language::get('index_modifypw_admin_error'));
            }
            //旧密码是否正确
            if ($admininfo['admin_password'] != md5(trim($_POST['old_pw']))){
                showMessage(Language::get('index_modifypw_oldpw_error'));
            }
            // 弱密码校验
            if (isWeakPassword($_POST['new_pw'])){
                showMessage('密码需含英文、数字及标点符号');
            }
            $new_pw = md5(trim($_POST['new_pw']));
            $update = array();
            $update['admin_password'] = $new_pw;
            $update['admin_id'] = $admininfo['admin_id'];
            $result = $admin_model->updateAdmin($update);
            if ($result){
                $_SESSION['is_week_password'] = 0;
                //更新口令时间
                setPasswordCycle($admininfo['admin_name'],1);
                showDialog(Language::get('index_modifypw_success'), urlAdmin('index', 'logout'), 'succ');
            }else{
                showMessage(Language::get('index_modifypw_fail'));
                showDialog(Language::get('index_modifypw_fail'), '', '', 'CUR_DIALOG.click();');
            }
        }else{
            Language::read('admin');
            Tpl::showpage('admin.modifypw', 'null_layout');
        }
    }
    
    public function save_avatarOp() {
        $admininfo = $this->getAdminInfo();
        $admin_model = Model('admin');
        $admininfo = $admin_model->getOneAdmin($admininfo['id']);
        if ($_GET['avatar'] == '') {
            echo false;die;
        }
        @unlink(BASE_UPLOAD_PATH . '/' . ATTACH_ADMIN_AVATAR . '/' . cookie('admin_avatar'));
        $update['admin_avatar'] = $_GET['avatar'];
        $update['admin_id'] = $admininfo['admin_id'];
        $result = $admin_model->updateAdmin($update);
        if ($result) {
            setNcCookie('admin_avatar',$_GET['avatar'],86400 * 365,'',null);
        }
        echo $result;die;
    }
}
