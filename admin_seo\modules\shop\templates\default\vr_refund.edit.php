<?php use Upet\Models\Order as OrderAlias;

defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title"><a class="back" href="javascript:history.back(-1)" title="返回列表"><i class="fa fa-arrow-circle-o-left"></i></a>
      <div class="subject">
        <h3>虚拟订单退款 - 处理退款“退单编号：<?php echo $output['refund']['refund_sn']; ?>”</h3>
          <h5>虚拟类商品订单退款申请及审核处理</h5>
      </div>
    </div>
  </div>
    <form id="post_form" method="post"
          action="index.php?act=vr_refund&op=edit&refund_id=<?php echo $output['refund']['refund_id']; ?>">
        <input type="hidden" name="form_submit" value="ok"/>
        <div class="ncap-form-default">
            <div class="title">
                <h3>买家退款申请</h3>
            </div>
            <dl class="row">
                <dt class="tit">订单编号</dt>
                <dd class="opt"><?php echo $output['refund']['order_sn']; ?> </dd>
            </dl>
            <dl class="row">
                <dt class="tit">下单时间</dt>
                <dd class="opt"><?php echo date('Y-m-d H:i:s', $output['order']['add_time']); ?> </dd>
            </dl>
            <dl class="row">
                <dt class="tit">申请时间</dt>
                <dd class="opt"><?php echo date('Y-m-d H:i:s', $output['refund']['add_time']); ?> </dd>
            </dl>
            <dl class="row">
                <dt class="tit">商品名称</dt>
                <dd class="opt"><a
                            href="<?php echo urlShop('goods', 'index', array('goods_id' => $output['refund']['goods_id'])); ?>"
                            target="_blank"><?php echo $output['refund']['goods_name']; ?></a></dd>
            </dl>
            <dl class="row">
                <dt class="tit">兑换码</dt>
        <dd class="opt">
          <?php $has_tips=false;?>
          <?php if (is_array($output['code_array']) && !empty($output['code_array'])) { ?>
          <?php foreach ($output['code_array'] as $key => $val) { 
          $model_vr_order = Model('vr_order');
          $vr_code_info = $model_vr_order->getOrderCodeInfo(array('vr_code' => $val),'vr_usetime,chain_name');
          //如果超过 3个月（90天）
          if(($output['refund']['add_time']-$vr_code_info['vr_usetime'])>90*86400 && $vr_code_info['vr_usetime']>0){
          	$has_tips=true;
          }
          ?>
          <?php echo $val.($vr_code_info['vr_usetime']?'<font color="red">（核销时间：'.date("Y-m-d H:i:s",$vr_code_info['vr_usetime']).' 核销门店：'.$vr_code_info['chain_name'].'）</font>':'');?><br />
          <?php } ?>
          <?php } ?>
        </dd>
      </dl>
      <dl class="row">
        <dt class="tit"><?php echo $lang['refund_order_refund'];?></dt>
        <dd class="opt"><?php echo ncPriceFormat($output['refund']['refund_amount']); ?> 
            <span id="pay_amount">
            <?php if ($output['detail_array']['pay_time'] > 0) { ?>
            (已完成在线退款金额 <?php echo ncPriceFormat($output['detail_array']['pay_amount']); ?>)
            <?php } ?>
            </span>
        </dd>
      </dl>
            <dl class="row">
                <dt class="tit">退款说明</dt>
                <dd class="opt"><?php echo $output['refund']['buyer_message']; ?> </dd>
            </dl>
            <div class="title">
                <h3>订单支付信息</h3>
            </div>
            <dl class="row">
                <dt class="tit">支付方式</dt>
                <dd class="opt">
                    <?php echo orderPaymentName($output['order']['payment_code']); ?>
                    <?php if ($output['order']['payment_code'] <> 'card' && $output['order']['payment_from'] > 0 && $output['order']['payment_code'] <> 'bd_pay') { ?> (电银通道)<?php } ?>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">订单总额</dt>
                <dd class="opt"><?php echo ncPriceFormat($output['order']['order_amount']); ?></dd>
            </dl>
            <?php if ($output['order']['refund_amount'] > 0) { ?>
                <dl class="row">
                    <dt class="tit">已退款金额</dt>
                    <dd class="opt"><?php echo ncPriceFormat($output['order']['refund_amount']); ?></dd>
                </dl>
      <?php } ?>
      <?php if ($output['refund']['gift_order']) { ?>
        <dl class="row">
            <dt class="tit">赠品订单信息</dt>
            <dd class="opt">
                <div class="ncap-order-details">
                    <div class="tabs-panels">
                        <div class="misc-info">
                            <h4>赠品订单信息</h4>
                            <dl>
                                <dt>赠品订单号：</dt><dd><?php echo $output['refund']['gift_order']['order_sn']; ?></dd>
                                <dt>买家：</dt><dd><?php echo $output['refund']['gift_order']['buyer_name']; ?></dd>
                                <dt>下单手机：</dt><dd><?php echo hideStr($output['refund']['gift_order']['buyer_phone']); ?></dd>
                                <dt></dt><dd></dd>
                            </dl>
                            <dl>
                                <dt>投保状态：</dt><dd><?php if ($output['refund']['gift_order']['order_state'] == ORDER_STATE_NEW) { ?>待支付<?php }elseif ($output['refund']['gift_order']['order_state'] == ORDER_STATE_PAY) { ?>待投保
                                    <?php }elseif ($output['refund']['gift_order']['order_state'] == ORDER_STATE_SUCCESS) { ?>已投保<?php }else{?>已取消<?php }?></dd>
                                <dt>投保单号：</dt><dd><?php echo $output['refund']['gift_order']['safe_app_no']; ?></dd>
                                <dt>保单号：</dt><dd><?php echo $output['refund']['gift_order']['safe_no']; ?></dd>
                                <?php if ($output['refund']['gift_order']['safe_time']) { ?>
                                <dt>投保时间：</dt><dd><?php $output['refund']['gift_order']['safe_time']; ?></dd>
                                <?php }?>
                            </dl>
                        </div>
                        <!--<div class="addr-note">
                            <h4>投保人信息</h4>
                            <dl>
                                <dt>投保人：</dt><dd>深圳市巨星网络技术有限公司</dd>
                                <dt>税号：</dt><dd>91440300306110974N</dd>
                            </dl>
                            <h4>被投保人信息</h4>
                            <dl>
                                <dt>证件类型：</dt><dd>身份证</dd>
                                <dt>证件号码：</dt><dd>440301198811075139</dd>
                            </dl>
                            <dl>
                                <dt>姓名：</dt><dd>笔记本</dd>
                                <dt>性别：</dt><dd>男</dd>
                            </dl>
                            <dl>
                                <dt>手机号码：</dt><dd>13640999852</dd>
                            </dl>
                            <h4>宠物信息</h4>
                            <dl>
                                <dt>宠物名称：</dt><dd>测试</dd>
                                <dt>宠物类别：</dt><dd>狗</dd>
                                <dt>宠物分类：</dt><dd>阿彭策尔山地犬</dd>
                            </dl>
                            <dl>
                                <dt>宠物性别：</dt><dd>公</dd>
                                <dt>出生日期：</dt><dd>2019-10-25</dd>
                            </dl>
                            <dl>
                                <dt>是否绝育：</dt><dd>是</dd>
                                <dt>是否免疫：</dt><dd>否</dd>
                            </dl>
                            <dl>
                                <dt>宠物faceID：</dt><dd>c650b2315f26447f93c118df55542738</dd>
                            </dl>
                            <dl>
                                <dt>养犬许可证号码：</dt><dd>265823686838</dd>
                                <dt>免疫许可证号码：</dt><dd>8683863836</dd>
                            </dl>
                            <dl>
                                <dt>宠物鼻纹和图片：</dt>
                                <dd>
                                    <img width="100" src="https://rpfield-zlerp.oss-cn-beijing.aliyuncs.com/scrmmini/tmp_fca5fbd55b834699e56dbc703e25c47ed0399d72087c5c14.jpg">
                                    <img width="100" src="https://rpfield-zlerp.oss-cn-beijing.aliyuncs.com/scrmmini/tmp_fca5fbd55b834699e56dbc703e25c47ed0399d72087c5c14.jpg">
                                    <img width="100" src="https://rpfield-zlerp.oss-cn-beijing.aliyuncs.com/scrmmini/tmp_fca5fbd55b834699e56dbc703e25c47ed0399d72087c5c14.jpg">
                                </dd>
                            </dl>
                        </div>-->

                        <div class="goods-info">
                            <h4>商品信息</h4>
                            <table>
                                <thead>
                                <tr>
                                    <th colspan="2">商品</th>
                                    <th>单价</th>
                                    <th>数量</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td class="w30">
                                        <div class="goods-thumb">
                                            <a href="<?php echo SHOP_SITE_URL;?>/index.php?act=goods&goods_id=<?php echo $output['refund']['gift_order']['goods_id'];?>" target="_blank">
                                                <img alt="" src="<?php echo $output['refund']['gift_order']['goods_image']; ?>">
                                            </a>
                                        </div>
                                    </td>
                                    <td style="text-align: left;">
                                        <a href="<?php echo SHOP_SITE_URL;?>/index.php?act=goods&goods_id=<?php echo $output['refund']['gift_order']['goods_id'];?>" target="_blank"><?php echo $output['refund']['gift_order']['goods_name']; ?></a>
                                        <br><?php echo $output['refund']['gift_order']['goods_spec']; ?>
                                    </td>
                                    <td class="w80">￥<?php echo $output['refund']['gift_order']['goods_price']; ?></td>
                                    <td class="w60"><?php echo $output['refund']['gift_order']['goods_num']; ?></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="total-amount">
                            <h3>订单总额：<strong class="red_common">￥<?php echo $output['refund']['gift_order']['order_amount']; ?></strong></h3>
                            <h4>(运费：￥0.00)</h4>
                        </div>
                    </div>
                </div>
            </dd>
        </dl>
      <?php }?>
      <?php if ($output['order']['pay_amount'] > 0) { ?>
      <dl class="row">
        <dt class="tit">在线支付金额</dt>
        <dd class="opt"><?php echo ncPriceFormat($output['order']['pay_amount']);?></dd>
      </dl>
      <?php if($has_tips){?>
      <dl class="row">
	    <dt class="tit"><font color="red">异常退款</font></dt>
	    <dd class="opt"><font color="red">核销完超过3个月退款！ </font></dd>
	  </dl>
	  <?php }?>
          <?php if (in_array($output['detail_array']['refund_code'],OrderAlias::PAY_CENTER_TYPES)) { ?>
          <dl class="row">
            <dt class="tit"></dt>
            <dd class="opt">
                <a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="wxpayBtn">
                    确认<?php $payNames = array('card'=>'储值卡','alipay'=>'支付宝','bd_pay'=>'百度支付'); echo isset($payNames[$output['detail_array']['refund_code']]) ? $payNames[$output['detail_array']['refund_code']] : '微信'; ?>退款
                </a>
            </dd>
          </dl>
          <?php } elseif ($output['detail_array']['refund_code'] == 'alipay') { ?>
          <dl class="row">
            <dt class="tit"></dt>
            <dd class="opt">
                <a href="<?php echo ADMIN_SITE_URL;?>/index.php?act=vr_refund&op=alipay&refund_id=<?php echo $output['refund']['refund_id']; ?>" target="_blank">支付宝退款</a>  
                <a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="alipayBtn">退款查询</a>
            </dd>
          </dl>
          <?php } ?>
      <?php } ?>
      <div class="title">
        <h3>平台退款审核</h3>
      </div>
        <dl class="row">
            <dt class="tit">
                <label><em>*</em>ERP积分</label>
            </dt>
            <dd class="opt"><?php echo $output['refund']['erpoints']; ?>分</dd>
        </dl>
        <dl class="row">
            <dt class="tit">
                <label><em>*</em>需扣积分</label>
            </dt>
            <dd class="opt"><?php echo $output['refund']['needpoints']; ?>分</dd>
        </dl>
      <dl class="row">
        <dt class="tit">
          <label><em>*</em>是否同意</label>
        </dt>
        <dd class="opt">
          <div class="onoff">
            <label for="state1" class="cb-enable selected" title="<?php echo $lang['nc_yes'];?>"><?php echo $lang['nc_yes'];?></label>
            <label for="state0" class="cb-disable" title="<?php echo $lang['nc_no'];?>"><?php echo $lang['nc_no'];?></label>
            <input id="state1" name="admin_state" checked="checked" value="2" type="radio">
            <input id="state0" name="admin_state" value="3" type="radio">
          </div>
          <span class="err"></span>
        </dd>
      </dl>
      <dl class="row">
      	<dt class="tit">
      		<label><em>*</em>是否退款站内余额</label>
      	</dt>
      	<dd class="opt">
      		<div class="onoff">
      			<label for="state1" class="cb-enable" title="<?php echo $lang['nc_yes'];?>"><?php echo $lang['nc_yes'];?></label>
      			<label for="state0" class="cb-disable selected" title="<?php echo $lang['nc_no'];?>"><?php echo $lang['nc_no'];?></label>
      			<input id="state1" name="admin_state_virbalance" value="2" type="radio">
      			<input id="state0" name="admin_state_virbalance" checked="checked" value="3" type="radio">
      		</div>
      	</dd>
      </dl>
      <dl class="row">
        <dt class="tit">
          <label><em>*</em><?php echo $lang['refund_message'];?></label>
        </dt>
        <dd class="opt">
          <textarea id="admin_message" name="admin_message" class="tarea"></textarea>
          <span class="err"></span> 
          <p class="notic">系统默认退款到“站内余额”，如果“在线退款”到原支付账号，建议在备注里说明，方便核对。</p>
        </dd>
      </dl>
      <div class="bot"><a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="submitBtn"><?php echo $lang['nc_submit'];?></a> </div>
    </div>
  </form>
</div>
<script type="text/javascript" src="<?php echo ADMIN_RESOURCE_URL;?>/js/jquery.nyroModal.js"></script>
<script type="text/javascript" src="<?php echo ADMIN_RESOURCE_URL;?>/js/refund.js"></script>
<script type="text/javascript">
$(function(){
    $('.nyroModal').nyroModal();
	$("#submitBtn").click(function(){
        if($("#post_form").valid()){
            if(confirm('提交后将不能恢复，确认吗？')) $("#post_form").submit();
    	}
	});
	$("#wxpayBtn").click(function(){
	    var ajaxurl = '<?php echo ADMIN_SITE_URL;?>/index.php?act=vr_refund&op=wxpay&refund_id=<?php echo $output['refund']['refund_id']; ?>';
		show_msg(ajaxurl);
        setTimeout(function(){
            window.history.back();
        },3500);
	});
	$("#alipayBtn").click(function(){
	    var ajaxurl = '<?php echo ADMIN_SITE_URL;?>/index.php?act=vr_refund&op=get_detail&refund_id=<?php echo $output['refund']['refund_id']; ?>';
		show_msg(ajaxurl);
	});
    $('#post_form').validate({
		errorPlacement: function(error, element){
			var error_td = element.parentsUntil('dl').children('span.err');
            error_td.append(error);
        },
        rules : {
            admin_state : {
                required   : true
            },
            admin_message : {
                required   : true
            }
        },
        messages : {
            admin_state : {
                required : '<i class="fa fa-exclamation-circle"></i>请选择是否同意退款'
            },
            admin_message  : {
                required : '<i class="fa fa-exclamation-circle"></i><?php echo $lang['refund_message_null'];?>'
            }
        }
    });
});
</script>