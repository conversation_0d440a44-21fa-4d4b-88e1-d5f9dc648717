<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <!-- 页面导航 -->
  <div class="fixed-bar">
    <div class="item-title">
      <div class="subject">
        <h3><?php echo $lang['nc_voucher_price_manage'];?></h3>
        <h5><?php echo $lang['nc_voucher_price_manage_subhead'];?></h5>
      </div>
      <ul class="tab-base nc-row">
        <?php   foreach($output['menu'] as $menu) {  if($menu['menu_key'] == $output['menu_key']) { ?>
        <li><a href="JavaScript:void(0);" class="current"><?php echo $menu['menu_name'];?></a></li>
        <?php }  else { ?>
        <li><a href="<?php echo $menu['menu_url'];?>" ><?php echo $menu['menu_name'];?></a></li>
        <?php  } }  ?>
      </ul>
    </div>
  </div>

  <form id="add_form" method="post" action="index.php?act=voucher&op=setting">
    <input type="hidden" id="form_submit" name="form_submit" value="ok"/>
    <div class="ncap-form-default">
      <dl class="row">
        <dt class="tit">
          <label><em>*</em>购买单价（元/月）</label>
        </dt>
        <dd class="opt">
          <input type="text" id="promotion_voucher_price" name="promotion_voucher_price" value="<?php echo $output['setting']['promotion_voucher_price'];?>" class="input-txt">
          <span class="err"></span>
          <p class="notic">购买代金劵活动所需费用，购买后商家可以在所购买周期内发布代金劵促销活动</p>
          <p class="notic">相关费用会在店铺的账期结算中扣除</p>
          <p class="notic">若设置为0，则商家可以免费发布此种促销活动</p>
        </dd>
      </dl>
      <dl class="row">
        <dt class="tit">
          <label><em>*</em><?php echo $lang['admin_voucher_setting_storetimes'];?></label>
        </dt>
        <dd class="opt">
          <input type="text" id="promotion_voucher_storetimes_limit" name="promotion_voucher_storetimes_limit" value="<?php echo $output['setting']['promotion_voucher_storetimes_limit'];?>" class="input-txt">
          <span class="err"></span>
          <p class="notic"><?php echo $lang['admin_voucher_setting_storetimes_tip'];?></p>
        </dd>
      </dl>
      <dl class="row">
        <dt class="tit">
          <label><em>*</em><?php echo $lang['admin_voucher_setting_buyertimes'];?></label>
        </dt>
        <dd class="opt">
          <input type="text" id="promotion_voucher_buyertimes_limit" name="promotion_voucher_buyertimes_limit" value="<?php echo $output['setting']['promotion_voucher_buyertimes_limit'];?>" class="input-txt">
          <span class="err"></span>
          <p class="notic"><?php echo $lang['admin_voucher_setting_buyertimes_tip'];?>，该值最大为20</p>
        </dd>
      </dl>
      <div class="bot"><a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="submitBtn"><?php echo $lang['nc_submit'];?></a></div>
    </div>
  </form>
</div>
<script>
$(document).ready(function(){
    $("#submitBtn").click(function(){
        $("#add_form").submit();
    });
    //页面输入内容验证
	$("#add_form").validate({
		errorPlacement: function(error, element){
			var error_td = element.parent('dd').children('span.err');
            error_td.append(error);
        },

        rules : {
        	promotion_voucher_price: {
                required : true,
                digits : true,
                min : 0
            },
            promotion_voucher_storetimes_limit: {
                required : true,
                digits : true,
                min : 1
            },
            promotion_voucher_buyertimes_limit: {
                required : true,
                digits : true,
                min : 1
            }
        },
        messages : {
        	promotion_voucher_price: {
       			required : '<i class="fa fa-exclamation-circle"></i><?php echo $lang['admin_voucher_setting_price_error'];?>',
       			digits : '<i class="fa fa-exclamation-circle"></i><?php echo $lang['admin_voucher_setting_price_error'];?>',
                min : '<i class="fa fa-exclamation-circle"></i><?php echo $lang['admin_voucher_setting_price_error'];?>'
	    	},
	    	promotion_voucher_storetimes_limit: {
                required : '<i class="fa fa-exclamation-circle"></i><?php echo $lang['admin_voucher_setting_storetimes_error'];?>',
                digits : '<i class="fa fa-exclamation-circle"></i><?php echo $lang['admin_voucher_setting_storetimes_error'];?>',
                min : '<i class="fa fa-exclamation-circle"></i><?php echo $lang['admin_voucher_setting_storetimes_error'];?>'
            },
            promotion_voucher_buyertimes_limit: {
                required : '<i class="fa fa-exclamation-circle"></i><?php echo $lang['admin_voucher_setting_buyertimes_error'];?>',
                digits : '<i class="fa fa-exclamation-circle"></i><?php echo $lang['admin_voucher_setting_buyertimes_error'];?>',
                min : '<i class="fa fa-exclamation-circle"></i><?php echo $lang['admin_voucher_setting_buyertimes_error'];?>'
            }
        }
	});
});
</script>
