<?php
/**
 * 登录
 *
 * 包括 登录 验证 退出 操作
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');
class LoginControl extends SystemControl {

    /**
     * 不进行父类的登录验证，所以增加构造方法重写了父类的构造方法
     */
    public function __construct(){
        Language::read('common,layout,login');
        //企业二维码回调验证
        if (isset($_GET['code']) && $_GET['code']) {
            $result = weixinLogin($_GET['code']);
            if ($result['state']) {
                $condition = ['admin_mobile' => $result['data']['mobile']];
                $admin_info = Model('admin')->where($condition)->find();
                if (!$admin_info) {
                    $token = [
                        'mobile'=> $result['data']['mobile'],
                        'state'=> 'admin_wxlogin',
                        'appid'=> C('work_weixin_appid'),
                    ];
                    $sign = Logic('connect_api')->getJwtToken($token);
                    $url = '/admin_seo/index.php?act=login&sign=' . $sign;
                    redirect(C('shop_site_url').$url);
                }
                $this->admin_login($admin_info);
            }
        }
        $result = chksubmit(true,true,'num');
        if ($result){
            if ($result === -11){
                showMessage('非法请求');
            }elseif ($result === -12){
                showMessage(L('login_index_checkcode_wrong'));
            }
            if (process::islock('admin')) {
                showMessage('您的操作过于频繁，请稍后再试');
            }
            // 密码解密重新赋值
            $_POST['password'] = rsaDecrypt($_POST['password']);
            // 登录错误次数检测
            $redis_key = 'login_'.$_POST['user_name'];
            if (loginLimit($redis_key, 'get') > 5){
                showMessage('错误次数过多，30分钟后再试');
            }

            $arr=array(
                array("input"=>$_POST["user_name"], "require"=>"true", "message"=>L('login_index_username_null')),
                array("input"=>$_POST["password"],  "require"=>"true", "message"=>L('login_index_password_null'))
            );
            
            if($_SESSION['error_num']>=DEFAULT_CONNECT_LOGIN_ERROR_NUM){
            	array_push($arr,  array("input"=>$_POST["captcha"],   "require"=>"true", "message"=>L('login_index_checkcode_null')));
            }

            $obj_validate = new Validate();
            $error = $obj_validate->validate();
            if ($error != '') {
                showMessage(L('error').$error);
            } else {
                $model_admin = Model('admin');
                $array  = array();
                $array['admin_name']    = $_POST['user_name'];
                $array['admin_email']    = $_POST['admin_email'];
                $array['admin_password']= md5(trim($_POST['password']));
                $admin_info = $model_admin->infoAdmin($array);
                if(empty($admin_info)) {
                    loginLimit($redis_key);
                    process::addprocess('admin');
                    showMessage('账号异常或账号密码错误','index.php?act=login&op=login');
                }
                //企业微信登录判断绑定手机
                if (isset($_POST['sign']) && $_POST['sign']) {
                    $sign_res = checkSign($_POST['sign']);
                    if (!$sign_res['state']){
                        showMessage(L('error').$sign_res['msg'],'index.php?act=login&op=login');
                    }
                    $mobile = $sign_res['mobile'];
                    $admin_model = Model('admin');
                    $info = $admin_model->where(['admin_mobile'=>$mobile])->find();
                    if(empty($info['admin_mobile']) && $mobile){
                        $res = $admin_model->where(['admin_id'=>$admin_info['admin_id']])->update(['admin_mobile'=>$mobile]);
                    }
                }
                $this->admin_login($admin_info);
            }
        }
        Tpl::output('html_title',L('login_index_need_login'));
        $wx_redirect_uri = C('shop_site_url').'/admin_seo/index.php?act=login';
        Tpl::output('wx_config', ['wx_redirect_uri' => $wx_redirect_uri, 'wx_agentid' => C('work_weixin_agentid'), 'wx_appid' => C('work_weixin_appid')]);
        if ((isset($_GET['sign']) && $_GET['sign']) || $_SERVER['HTTP_HOST'] =='localhost') {
            Tpl::output('wx_login', 0);
        }else{
            Tpl::output('wx_login', C('work_weixin_open'));
        }
        Tpl::showpage('login','login_layout');
    }

    public function loginOp(){}
    public function indexOp(){}

    public function admin_login($admin_info){
        if ($admin_info['admin_gid'] > 0) {
            $gamdin_info = Model('gadmin')->getGadminInfoById($admin_info['admin_gid']);
            $group_name = $gamdin_info['gname'];
        } else {
            $group_name = '超级管理员';
        }
        $array = array();
        $array['name']  = $admin_info['admin_name'];
        $array['admin_mobile']  = $admin_info['admin_mobile'];
        $array['id']    = $admin_info['admin_id'];
        $array['time']  = $admin_info['admin_login_time'];
        $array['ip']    = getIp();
        $array['gid']   = $admin_info['admin_gid'];
        $array['gname'] = $group_name;
        $array['sp']    = $admin_info['admin_is_super'];
        $array['qlink'] = $admin_info['admin_quick_link'];
        $array['store_ids'] = $admin_info['store_ids'];
        $storeIds = explode(',',$admin_info['store_ids']);
        if(is_array($storeIds) && count($storeIds)>0){
            //将当前的店铺id存在session里
             $_SESSION['store_id'] = $storeIds[0];

        }

        $this->systemSetKey($array, $admin_info['admin_avatar'], true);
        $update_info    = array(
            'admin_id'=>$admin_info['admin_id'],
            'admin_login_num'=>($admin_info['admin_login_num']+1),
            'admin_login_time'=>TIMESTAMP
        );
        Model('admin')->updateAdmin($update_info);
        $this->log(L('nc_login'),1);
        process::clear('admin');
        @header('Location: index.php');exit;
    }
}
