<?php
/**
 * 虚拟订单管理
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;
use Upet\Models\Goods;
use Upet\Modules\Order\Actions\ValidateVipOrderRefundableAction;

defined('InShopNC') or exit('Access Invalid!');
class vr_orderControl extends SystemControl{
    /**
     * 每次导出订单数量
     * @var int
     */
    const EXPORT_SIZE = 10000;

    public function __construct(){
        parent::__construct();
        Language::read('trade');
    }

    public function indexOp(){

        //显示支付接口列表(搜索)
        $payment_list = Model('payment')->getPaymentOpenList();
        $payment_list['wxpay'] = array(
                'payment_code' => 'wxpay',
                'payment_name' => '微信支付'
        );

        $payment_list['card'] = [
            'payment_code' => 'card',
            'payment_name' => '储值卡'
        ];

        foreach ($payment_list as $key => $value){
            if ($value['payment_code'] == 'offline') {
                unset($payment_list[$key]);
            }
        }

        Tpl::output('order_type_list', array( //订单类型
            "" => "全部",
            '0t' => "普通订单",
            '4t' => "拼团订单",
            '10t' => "新人订单",
            '14t' => "活动订单",
            '17t' => "付费会员",
            '18t' => "家庭医生服务包",
            '99t' => "助力订单",
        ));

        Tpl::output('admin_id',$this->admin_info['id']);
        Tpl::output('payment_list',$payment_list);
        Tpl::showpage('vr_order.index');
    }

    public function get_xmlOp() {
        $model_vr_order = Model('vr_order');
        $model_stat = Model('stat');
        $condition  = ['vr_order.erp_status' => ['lt',2]];
        $on = '';
        $condition['vr_order.store_id'] = $_SESSION['store_id'];
        if($_GET['qtype_time'] == "vr_usetime") {
            $this->_get_conditionHx($condition);
            $field = "vr_order.order_id,vr_order.order_sn,vr_order.erp_order_sn,vr_order.chain_id,vr_order.order_from,vr_order.add_time,vr_order.order_amount,vr_order.order_state,vr_order.payment_code,vr_order.payment_time,vr_order.buyer_name,vr_order.buyer_phone,vr_order.buyer_id,vr_order.store_name,vr_order.store_id,vr_order.goods_name,vr_order.vr_indate,vr_order.rcb_amount,vr_order.pd_amount,vr_order.finnshed_time,vr_order.evaluation_state,vr_order.refund_amount,vr_order.dis_member_id";
            $on = 'vr_order.order_id = vr_order_code.order_id';
            $orderby = 'vr_order.order_id desc';
            $model = new Model();
            $order_allnum = $model->table('vr_order,vr_order_code')->join('inner')->on($on)->field('sum(order_amount) as allnum')->where($condition)->order($orderby)->select();
        }else{
            $this->_get_condition($condition);
            $field = 'order_id,order_sn,erp_order_sn,chain_id,order_from,add_time,order_amount,order_state,payment_code,payment_time,buyer_name,buyer_phone,buyer_id,store_name,store_id,goods_name,vr_indate,rcb_amount,pd_amount,finnshed_time,evaluation_state,refund_amount,dis_member_id';
            $orderby = 'order_id desc';
            $order_allnum =$model_stat->statByFlowstat('vr_order', $condition, 'sum(order_amount) as allnum');
        }
        //商品标签查询
        $like_arr = [];
        if (isset($_GET['tag_title'])) {
            foreach ($_GET['tag_title'] as $v){
                if ($v) {
                    $like_arr[] = ['eq', $v];
                }
            }
            if($like_arr) array_push($like_arr, 'or');
        }
        $order_list = $model_vr_order->getOrderListAndCode($condition, $field, $on, !empty($_POST['rp']) ? intval($_POST['rp']) : 15, $orderby,'',$like_arr);
        $data = array();
        $data['now_page'] = $model_vr_order->shownowpage();
        $data['total_num'] = $model_vr_order->gettotalnum();
        $_SESSION['vr_order_sellnum_'.$_SESSION['store_id']] = $order_allnum[0]['allnum'];

        foreach ($order_list as $k => $order_info) {
            $list = array();
            $operation_detail = '';
            $list['operation'] = "<a class=\"btn green\" href=\"index.php?act=vr_order&op=show_order&order_id={$order_info['order_id']}\"><i class=\"fa fa-list-alt\"></i>查看</a>";
            $if_cancel = $model_vr_order->getOrderOperateState('system_cancel',$order_info);
            if ($if_cancel) {
                $operation_detail .= "<li><a href=\"javascript:void(0);\" onclick=\"fg_cancel({$order_info['order_id']})\">取消订单</a></li>";
            }
            $if_system_receive_pay = $model_vr_order->getOrderOperateState('system_receive_pay',$order_info);
            if ($if_system_receive_pay) {
                $operation_detail .= "<li><a href=\"index.php?act=vr_order&op=change_state&state_type=receive_pay&order_id={$order_info['order_id']}\">收到货款</a></li>";
            }
            if ($operation_detail) {
                $list['operation'] .= "<span class='btn'><em><i class='fa fa-cog'></i>设置 <i class='arrow'></i></em><ul>{$operation_detail}</ul>";
            }
            $list['order_sn'] = $order_info['order_sn'];
            $list['erp_order_sn'] = $order_info['erp_order_sn']?:'';
            $list['order_from'] = orderFromName($order_info['order_from'], 1);
            $list['add_time'] = date('Y-m-d H:i:s',$order_info['add_time']);
            $list['order_amount'] = ncPriceFormat($order_info['order_amount']);
            $list['order_state'] = $order_info['state_desc'];
            $list['payment_code'] = orderPaymentName($order_info['payment_code']);
            $list['payment_time'] = !empty($order_info['payment_time']) ? (intval(date('His',$order_info['payment_time'])) ? date('Y-m-d H:i:s',$order_info['payment_time']) : date('Y-m-d',$order_info['payment_time'])) : '';
            $list['buyer_name'] = $order_info['buyer_name'];
            $list['buyer_phone'] = hideStr($order_info['buyer_phone']);
            $list['dis_member_id'] = $order_info['dis_member_id'] > 0 ? $order_info['dis_member_id'] : '';
            $list['buyer_id'] = $order_info['buyer_id'];
            $list['store_name'] = $order_info['store_name'];
            $list['store_id'] = $order_info['store_id'];
            $list['goods_name'] = $order_info['goods_name'];
            $list['vr_indate'] = !empty($order_info['vr_indate']) ? date('Y-m-d H:i:s',$order_info['vr_indate']) : '';
            $list['rcb_amount'] = ncPriceFormat($order_info['rcb_amount']);
            $list['pd_amount'] = ncPriceFormat($order_info['pd_amount']);
            $list['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s',$order_info['finnshed_time']) : '';
            $list['evaluation_state'] = str_replace(array(0,1,2), array('未评价','已评价','未评价'),$order_info['evaluation_state']);
            $list['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
            $data['list'][$order_info['order_id']] = $list;
        }

        exit(Tpl::flexigridXML($data));
    }
    public function get_order_sellnumOp(){

        echo json_encode(array('vr_order_all_num'=>$_SESSION['vr_order_sellnum_'.$_SESSION['store_id']]));
    }
    /**
     * 平台订单状态操作
     *
     */
    public function change_stateOp() {
        $model_vr_order = Model('vr_order');
        $condition = array();
        $condition['order_id'] = intval($_GET['order_id']);
        $order_info = $model_vr_order->getOrderInfo($condition);
        if ($_GET['state_type'] == 'cancel') {
            $result = $this->_order_cancel($order_info);
        } elseif ($_GET['state_type'] == 'receive_pay') {
            $result = $this->_order_receive_pay($order_info,$_POST);
        }
        if(!$result['state']) {
            showMessage($result['msg'],$_POST['ref_url'],'html','error');
        } else {
            showMessage($result['msg'],$_POST['ref_url']);
        }
    }

    /**
     * 系统取消订单
     * @param unknown $order_info
     */
    private function _order_cancel($order_info) {
        $model_vr_order = Model('vr_order');
        $logic_vr_order = Logic('vr_order');
        $if_allow = $model_vr_order->getOrderOperateState('system_cancel',$order_info);
        if (!$if_allow) {
            return callback(false,'无权操作');
        }
        if (TIMESTAMP - 86400 < $order_info['api_pay_time']) {
            $_hour = ceil(($order_info['api_pay_time']+86400-TIMESTAMP)/3600);
            exit(json_encode(array('state'=>false,'msg'=>'该订单曾尝试使用第三方支付平台支付，须在'.$_hour.'小时以后才可取消')));
        }
        $this->log('关闭了订单,'.L('order_number').':'.$order_info['order_sn'],1);
        $result = $logic_vr_order->changeOrderStateCancel($order_info,'store', '管理员('.$this->admin_info['name'].')关闭订单');
        if ($result['state']) {
            exit(json_encode(array('state'=>true,'msg'=>'取消成功')));
        } else {
            exit(json_encode(array('state'=>false,'msg'=>'取消失败')));
        }
    }

    /**
     * 系统收到货款
     * @param unknown $order_info
     * @throws Exception
     */
    private function _order_receive_pay($order_info,$post) {
        $model_vr_order = Model('vr_order');
        $logic_vr_order = Logic('vr_order');
        $if_allow = $model_vr_order->getOrderOperateState('system_receive_pay',$order_info);
        if (!$if_allow) {
            return callback(false,'无权操作');
        }

        if (!chksubmit()) {
            Tpl::output('order_info',$order_info);
            //显示支付接口
            $payment_list = Model('payment')->getPaymentOpenList();
            //去掉预存款和货到付款
            foreach ($payment_list as $key => $value){
                if ($value['payment_code'] == 'predeposit' || $value['payment_code'] == 'offline') {
                   unset($payment_list[$key]);
                }
            }
            Tpl::output('payment_list',$payment_list);
            Tpl::showpage('order.receive_pay');
            exit();
        } else {
            $result = $logic_vr_order->changeOrderStatePay($order_info,'admin', $post);
            if ($result['state']) {
                $this->log('将订单改为已收款状态,'.L('order_number').':'.$order_info['order_sn'],1);
                //记录消费日志
                $api_pay_amount = $order_info['order_amount'] - $order_info['pd_amount'] - $order_info['rcb_amount'];
                QueueClient::push('addConsume', array('member_id'=>$order_info['buyer_id'],'member_name'=>$order_info['buyer_name'],
                'consume_amount'=>$api_pay_amount,'consume_time'=>TIMESTAMP,'consume_remark'=>'管理员更改虚拟订单为已收款状态，订单号：'.$order_info['order_sn']));
            }
            return $result;
        }
    }

    /**
     * 查看订单
     *
     */
    public function show_orderOp(){
        $order_id = intval($_GET['order_id']);
        if($order_id <= 0 ){
            showMessage(L('miss_order_number'));
        }
        $model_vr_order = Model('vr_order');
        $order_info = $model_vr_order->getOrderInfo(array('order_id'=>$order_id));
        if (empty($order_info)) {
            showMessage('订单不存在','','html','error');
        }

        //取兑换码列表
        $vr_code_list = $model_vr_order->getOrderCodeList(array('order_id' => $order_info['order_id']),'*',true);
        $order_info['extend_vr_order_code'] = $vr_code_list;

        //显示取消订单
        $order_info['if_cancel'] = $model_vr_order->getOrderOperateState('buyer_cancel',$order_info);

        $condition = array();
        $condition['order_id'] = $order_id;

        if(!in_array($order_info['order_type'],[17,18])){
            $condition['vr_state'] = 0;
        }

        $condition['refund_lock'] = '0';//退款锁定状态:0为正常(能退款),1为锁定(待审核),2为同意
        $order_info['code_list'] = $model_vr_order->getCodeList($condition,"*","","rec_id desc","","",true);
        $order_info['if_refund_cancel'] = $model_vr_order->getOrderOperateState('refund',$order_info);

        //显示订单进行步骤
        $order_info['step_list'] = $model_vr_order->getOrderStep($order_info);

        //显示系统自动取消订单日期
        if ($order_info['order_state'] == ORDER_STATE_NEW) {
            $order_info['order_cancel_day'] = $order_info['add_time'] + ORDER_AUTO_CANCEL_TIME * 60;
        }
        //显示门店信息
        if ($order_info['chain_id']) {
            $chain_model = Model('chain');
            $chain_info = $chain_model->getChainInfo(array('chain_id' => $order_info['chain_id']),"chain_name,account_id");
            $order_info['chain_name'] = $chain_info['chain_name'];
            $order_info['account_id'] = $chain_info['account_id'];
        }

        //退款信息
        $refund_list = Model('vr_refund')->getRefundList(array('order_id'=>$order_info['order_id']));

        Tpl::output('refund_list',$refund_list);

        //商家信息
        $store_info = Model('store')->getStoreInfo(array('store_id'=>$order_info['store_id']));
        Tpl::output('store_info',$store_info);

        Tpl::output('order_info',$order_info);
        Tpl::showpage('vr_order.view');
    }

    /**
     * 添加全部退款即取消订单
     *
     */
    public function add_vr_refund_allOp()
    {
        $model_vr_refund = Model('vr_refund');
        $model_vr_order = Model('vr_order');
        $order_id = intval($_GET['order_id']);
        if ($order_id < 1) {//参数验证
            showDialog(Language::get('wrong_argument'), 'reload', 'error');
        }
        $condition = array();
        //$condition['buyer_id'] = $_SESSION['member_id'];
        $condition['order_id'] = $order_id;
        $order = $model_vr_refund->getRightOrderList($condition);

        $goods_num = 0;//兑换码数量
        $refund_amount = 0;//退款金额
        $code_sn = '';
        $condition = array();
        $condition['order_id'] = $order_id;
        if (!in_array($order['order_type'], [17, 18])) {
            $condition['vr_state'] = 0;
        }
        $condition['refund_lock'] = '0';//退款锁定状态:0为正常(能退款),1为锁定(待审核),2为同意
        $code_list = $model_vr_order->getCodeList($condition, "*", "", "rec_id desc", "", "", true);
        $rec_id_array = $code_list;
        if (!empty($rec_id_array) && is_array($rec_id_array)) {//选择退款的兑换码
            foreach ($rec_id_array as $key => $value) {
                $code = $code_list[$key];
                if ((!empty($code) && $code['vr_state'] != '1') || in_array($order['order_type'], [17, 18])) {
                    $goods_num += 1;
                    $refund_amount += $code['pay_price'];//实际支付金额
                    $code_sn .= $code['vr_code'] . ',';//兑换码编号
                }
            }
        }

        $order['allow_refund_amount'] = $refund_amount;

        Tpl::output('order', $order);

        if (chksubmit()) {
            $model_vr_refund = Model('vr_refund');
            if (!$order['if_refund'] && !in_array($order['order_type'], [17, 18])) {//检查状态,防止页面刷新不及时造成数据错误
                showDialog("订单退款审核中", 'reload', 'error');
            }

            $refund_array = array();
            if ($goods_num < 1) {
                showDialog("没有可退数量", 'reload', 'error');
            }

            // 家庭医生服务包激活也可以退款
            if (in_array($order['order_type'], [17, 18])) {
                $refund_amount = $_POST['refund_amount'];
                if ($refund_amount < 0.009 || $refund_amount > $order['order_amount']) {
                    showDialog("退款金额不能为0或者大于订单金额", 'reload', 'error');
                }
            }

            if (Goods::isVipGood($order['goods_id']) && $order['payment_time'] > C('vip_timeline')) {
                try {
                    action(new ValidateVipOrderRefundableAction($order));
                } catch (Exception $e) {
                    showDialog($e->getMessage(), 'reload', 'error');
                }
                $model_member = Model('member');
                $refund_amount = $model_member->getMemberVipRefundMoney($refund_amount, $order);
                if ($refund_amount == 0) {
                    showDialog("没有可退金额,已过会员协议退款期限", 'reload', 'error');
                }
            }
            //有赠品订单
            $model_gift_order = Model('gift_order');
            $gift_order_info = $model_gift_order->getOrderInfo(['order_id_from' => $order_id], 'order_id,goods_price,order_state,payment_time');
            if (is_array($gift_order_info) && !empty($gift_order_info)) {
                $toubao_starttime = date('Y-m-d', ($order['payment_time'] + 1 * 24 * 60 * 60));
                $toubao_starttime = strtotime($toubao_starttime);
                $toubao_endtime = $toubao_starttime + 7 * 24 * 60 * 60;
                $now_time = time();
                if ($toubao_endtime < $now_time && $gift_order_info['order_state'] == ORDER_STATE_SUCCESS) {
                    $refund_amount = $refund_amount - ncPriceFormat($gift_order_info['goods_price']);
                }
            }
            $refund_array['code_sn'] = rtrim($code_sn, ',');
            $refund_array['admin_state'] = '1';//状态:1为待审核,2为同意,3为不同意
            $refund_array['refund_amount'] = ncPriceFormat($refund_amount);
            $refund_array['goods_num'] = $goods_num;
            $refund_array['buyer_message'] = $_POST['buyer_message'];
            $refund_array['add_time'] = time();
            $refund_array['order_id'] = $order_id;
            $refund_array['refund_sn'] = $model_vr_refund->getRefundsn($refund_array['store_id']);
            $refundapply = Logic('refund')->refundVrOrderApply($refund_array);
            if (!$refundapply) {
                showDialog("冻结ERP退款订单失败", 'reload', 'error');
            }
            $state = $model_vr_refund->addRefund($refund_array, $order);
            if ($state) {
                $model_vr_refund->editRefund(['refund_id' => $state], ['erp_refund_id' => $refundapply]);
                showDialog(Language::get('nc_common_save_succ'), 'reload', 'succ', 'CUR_DIALOG.close();');
            } else {
                showDialog(Language::get('nc_common_save_fail'), 'reload', 'error');
            }

        }
        Tpl::showpage('vr_order_refund_all', 'null_layout');
    }

    /**
     * 导出
     *
     */
    public function export_step1Op(){
        $lang   = Language::getLangContent();

        $model_vr_order = Model('vr_order');
        $condition  = ['vr_order.erp_status' => ['lt',2]];

        if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
            $_GET['order_id'] = explode(',',trim($_GET['order_id'],','));
            //$condition['order_id'] = array('in',$_GET['order_id']);
            $condition['vr_order.order_id'] = array('in',$_GET['order_id']);
        }
        $condition['vr_order.store_id'] = $_SESSION['store_id'];
        $this->_get_conditionHx($condition);
        $like_arr = [];
        if (isset($_GET['tag_title'])) {
            foreach ($_GET['tag_title'] as $v){
                if ($v) {
                    $like_arr[] = ['eq', $v];
                }
            }
            if($like_arr) array_push($like_arr, 'or');
        }
        $field = "vr_order.order_id,vr_order.order_sn,vr_order.chain_id,vr_order.dis_member_id,vr_order_code.erp_order_id,
        vr_order.order_from,vr_order.add_time,vr_order.order_amount,vr_order.order_state,vr_order.payment_code,vr_order.payment_time,
        vr_order.buyer_name,vr_order.buyer_phone,vr_order.buyer_id,vr_order.store_name,vr_order.store_id,vr_order.goods_name,
        vr_order.vr_indate,vr_order.rcb_amount,vr_order.pd_amount,vr_order.finnshed_time,vr_order.evaluation_state,
        vr_order.refund_amount,vr_order_code.pay_price,vr_order_code.vr_code,vr_order_code.vr_state,vr_order_code.vr_usetime,
        vr_order_code.erp_chargeoff,vr_order_code.erp_chargeoffhospitalid,vr_order_code.chain_id,vr_order_code.chain_name";
        $on = 'vr_order.order_id = vr_order_code.order_id';
        if (!is_numeric($_GET['curpage'])) {
            $count = $model_vr_order->getOrderCodeCountAll($condition, $field, $on, $like_arr);

            $array = array();
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=vr_order&op=index');
                Tpl::showpage('export.excel');
            }else{  //如果数量小，直接下载
                $data = $model_vr_order->getOrderListAndCodeSnapshot($condition,$field,$on,self::EXPORT_SIZE,'vr_order.order_id desc',self::EXPORT_SIZE,$like_arr);
                $this->createExcel($data);
            }
        }else{  //下载
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $data = $model_vr_order->getOrderListAndCodeSnapshot($condition,$field,$on,'','vr_order.order_id desc',"{$limit1},{$limit2}",$like_arr);
            $this->createExcel($data);
        }
    }

    /**
     * 生成cvs
     *
     * @param array $data
     */
    private function export_hxcsv($data = array()) {
        Language::read('export');
        import('libraries.csv');
        $csv_obj = new Csv();
        $csv_data = array();
        $csv_data[0]['order_sn'] = '订单编号';
        $csv_data[0]['erp_order_sn'] = 'ERP订单号';
        $csv_data[0]['goods_serial'] = '商品货号';
        $csv_data[0]['order_from'] = '订单来源';
        $csv_data[0]['add_time'] = '下单时间';
        $csv_data[0]['order_amount'] = '订单金额(元)';
        $csv_data[0]['order_state'] = '订单状态';
        $csv_data[0]['payment_code'] = '支付方式';
        $csv_data[0]['payment_time'] = '支付时间';
        $csv_data[0]['buyer_name'] = '买家账号';
        $csv_data[0]['buyer_phone'] = '接收手机';
        $csv_data[0]['buyer_id'] = '买家ID';
        $csv_data[0]['store_name'] = '店铺名称';
        $csv_data[0]['store_id'] = '店铺ID';
        $csv_data[0]['goods_name'] = '商品';
        $csv_data[0]['vr_indate'] = '有效期';
        $csv_data[0]['rcb_amount'] = '充值卡支付(元)';
        $csv_data[0]['pd_amount'] = '预存款支付(元)';
        $csv_data[0]['finnshed_time'] = '完成时间';
        $csv_data[0]['evaluation_state'] = '是否评价';
        $csv_data[0]['refund_amount'] = '退款金额(元)';
        $csv_data[0]['pay_price'] = '实际支付金额(元)';
        $csv_data[0]['off_code'] = '核销码';
        $csv_data[0]['off_code_state'] = '核销码状态';
        $csv_data[0]['vr_usetime'] = '使用时间';
        $csv_data[0]['erp_chargeoff'] = '核销平台';
        $csv_data[0]['chain_name'] = '核销门店';
        $csv_data[0]['chain_id'] = '核销门店ID';

        $t1 = microtime(true);
        foreach ((array)$data as $k=>$order_info){
            $arr=unserialize($order_info['goods_attr']);
            $goods_serial=$arr['货号'];
            $list = array();
            $list['order_sn'] = $order_info['order_sn'];
            $list['erp_order_sn'] = $order_info['erp_order_id']?:'';
            $list['goods_serial'] = $goods_serial;
            $list['order_from'] = orderFromName($order_info['order_from'], 1);
            $list['add_time'] = date('Y-m-d H:i:s',$order_info['add_time']);
            $list['order_amount'] = ncPriceFormat($order_info['order_amount']);
            $list['order_state'] = $order_info['state_desc'];
            $list['payment_code'] = orderPaymentName($order_info['payment_code']);
            $list['payment_time'] = !empty($order_info['payment_time']) ? (intval(date('His',$order_info['payment_time'])) ? date('Y-m-d H:i:s',$order_info['payment_time']) : date('Y-m-d',$order_info['payment_time'])) : '';
            $list['buyer_name'] = $order_info['buyer_name'];
            $list['buyer_phone'] = hideStr($order_info['buyer_phone']);
            $list['buyer_id'] = $order_info['buyer_id'];
            $list['store_name'] = $order_info['store_name'];
            $list['store_id'] = $order_info['store_id'];
            $list['goods_name'] = $order_info['goods_name'];
            $list['vr_indate'] = !empty($order_info['vr_indate']) ? date('Y-m-d H:i:s',$order_info['vr_indate']) : '';
            $list['rcb_amount'] = ncPriceFormat($order_info['rcb_amount']);
            $list['pd_amount'] = ncPriceFormat($order_info['pd_amount']);
            $list['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s',$order_info['finnshed_time']) : '';
            $list['evaluation_state'] = str_replace(array(0,1,2), array('未评价','已评价','未评价'),$order_info['evaluation_state']);
            $list['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
            $list['pay_price'] = ncPriceFormat($order_info['pay_price']);
            $list['off_code'] = $order_info['vr_code'];
            $list['off_code_state'] = $order_info['vr_state'];
            $list['vr_usetime'] = $order_info['vr_usetime']?date("Y-m-d H:i:s",$order_info['vr_usetime']):"-";
            $list['erp_chargeoff'] = $order_info['erp_chargeoff']?"ERP核销":"电商核销";
            $list['chain_name'] = $order_info['chain_name']?$order_info['chain_name']:$order_info['erp_chargeoffhospitalid'];
            $list['chain_id'] = $order_info['chain_id']?$order_info['chain_id']:"-";
            if(C('member_mobile_on')){
                $list['buyer_phone'] = encryptShow($list['buyer_phone'],4,4);
            }

            //查找核销码
            //$codelist=Model("vr_order")->getOrderCodeList(array("order_id"=>$order_info['order_id']));


            //$vr_code_desc="";
            //如有核销码
            //if($codelist){
            //	foreach ($codelist as $vv){
            //		$vr_code_desc.=($vv['vr_code']."【".$vv['vr_code_desc']."】");
            //	}
            //}
            //$list['ordercode'] =$vr_code_desc;
            $csv_data[] = $list;
        }
        $t2 = microtime(true);
        wkcache("nc_nc_timessssss",($t2-$t1));
        $csv_data = $csv_obj->charset($csv_data,CHARSET,'gbk');
        $csv_obj->filename='order-'.$_GET['curpage'].'-'.date('Y-m-d-H',time());
        //$csv_obj->export($export_data);
        $csv_obj->export($csv_data);

    }
    private function createExcel($data = array()){
        Language::read('export');
        import('libraries.excel');
        $excel_obj = new Excel();
        $excel_data = array();
        //设置样式
        $excel_obj->setStyle(array('id'=>'s_title','Font'=>array('FontName'=>'宋体','Size'=>'12','Bold'=>'1')));
        //header
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单编号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'ERP订单号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'商品货号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单来源');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'下单时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单金额(元)');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单状态');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'支付方式');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'支付时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'买家账号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'接收手机');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'分销员ID');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'买家ID');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'商品');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'有效期');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'完成时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'是否评价');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'退款金额(元)');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'实际支付金额(元)');      
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'核销码');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'核销码状态');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'使用时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'核销平台');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'核销门店');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'核销门店财务编码');

        foreach ((array)$data as $k=>$order_info){
            $arr=unserialize($order_info['goods_attr']);
        	$goods_serial=$arr['货号'];	
            $list = array();
            $list['order_sn'] = $order_info['order_sn'];
            $list['erp_order_sn'] = $order_info['erp_order_id']?:'';
            $list['goods_serial'] = $goods_serial;
            $list['order_from'] = orderFromName($order_info['order_from'], 1);
            $list['add_time'] = date('Y-m-d H:i:s',$order_info['add_time']);
            $list['order_amount'] = ncPriceFormat($order_info['order_amount']);
            $list['order_state'] = $order_info['state_desc'];
            $list['payment_code'] = orderPaymentName($order_info['payment_code']);
            $list['payment_time'] = !empty($order_info['payment_time']) ? (intval(date('His',$order_info['payment_time'])) ? date('Y-m-d H:i:s',$order_info['payment_time']) : date('Y-m-d',$order_info['payment_time'])) : '';
            $list['buyer_name'] = $order_info['buyer_name'];
            $list['buyer_phone'] = $order_info['buyer_phone'];
            $list['dis_member_id'] = $order_info['dis_member_id'] > 0 ? $order_info['dis_member_id']: '';
            $list['buyer_id'] = $order_info['buyer_id'];
            $list['goods_name'] = htmlentities($order_info['goods_name']);
            $list['vr_indate'] = !empty($order_info['vr_indate']) ? date('Y-m-d H:i:s',$order_info['vr_indate']) : '';
            $list['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s',$order_info['finnshed_time']) : '';
            $list['evaluation_state'] = str_replace(array(0,1,2), array('未评价','已评价','未评价'),$order_info['evaluation_state']);
            $list['refund_amount'] = ncPriceFormat($order_info['refund_amount']);     
            $list['pay_price'] = ncPriceFormat($order_info['pay_price']);
            $list['off_code'] = $order_info['vr_state_key'] == 0 ?
                rightEncrypt($order_info['vr_code'],6) : $order_info['vr_code'];
            $list['off_code_state'] = $order_info['vr_state'];
            $list['vr_usetime'] = $order_info['vr_usetime']?date("Y-m-d H:i:s",$order_info['vr_usetime']):"-";
            $list['erp_chargeoff'] = $order_info['erp_chargeoff']?"ERP核销":"电商核销";
            $list['chain_name'] = $order_info['chain_name']?$order_info['chain_name']:$order_info['erp_chargeoffhospitalid'];
            $list['chain_id'] = $order_info['chain_id']?$order_info['chain_id']:$order_info['erp_chargeoffhospitalid'];
            $list['account_id'] = $order_info['account_id']?$order_info['account_id']:"-";

            if(C('member_mobile_on')){
                $list['buyer_phone'] = encryptShow($list['buyer_phone'],4,4);
            }
            //查找核销码
           
            $tmp = array();
            $tmp[] = array('data'=>$list['order_sn']);
            $tmp[] = array('data'=>$list['erp_order_sn']);
            $tmp[] = array('data'=>$list['goods_serial']);
            $tmp[] = array('data'=>$list['order_from']);
            $tmp[] = array('data'=>$list['add_time']);
            $tmp[] = array('data'=>$list['order_amount']);
            $tmp[] = array('data'=>$list['order_state']);
            $tmp[] = array('data'=>$list['payment_code']);
            $tmp[] = array('data'=>$list['payment_time']);
            $tmp[] = array('data'=>$list['buyer_name']);
            $tmp[] = array('data'=>$list['buyer_phone']);
            $tmp[] = array('data'=>$list['dis_member_id']);
            $tmp[] = array('data'=>$list['buyer_id']);
            $tmp[] = array('data'=>$list['goods_name']);
            $tmp[] = array('data'=>$list['vr_indate']);
            $tmp[] = array('data'=>$list['finnshed_time']);
            $tmp[] = array('data'=>$list['evaluation_state']);
            $tmp[] = array('data'=>$list['refund_amount']);
            $tmp[] = array('data'=>$list['pay_price']);
            $tmp[] = array('data'=>$list['off_code']);
            $tmp[] = array('data'=>$list['off_code_state']);
            $tmp[] = array('data'=>$list['vr_usetime']);
            $tmp[] = array('data'=>$list['erp_chargeoff']);
            $tmp[] = array('data'=>$list['chain_name']);
            $tmp[] = array('data'=>$list['account_id']);
            //$tmp[] = array('data'=>$list['ordercode']);
            $excel_data[] = $tmp;
        }
        $excel_data = $excel_obj->charset($excel_data,CHARSET);
        $excel_obj->addArray($excel_data);
        $excel_obj->addWorksheet($excel_obj->charset(L('exp_od_order'),CHARSET));
        $excel_obj->generateXML('order-'.$_GET['curpage'].'-'.date('Y-m-d-H',time()));
        $this->log('导出核销状态码');
    }

    /**
     * 导出订单数据
     *
     */
    public function export_step2Op(){
        set_time_limit(0);
        $lang   = Language::getLangContent();

        $model_vr_order = Model('vr_order');
        $condition  = ['vr_order.erp_status' => ['lt',2]];

        if (preg_match('/^[\d,]+$/', $_GET['order_id'])) {
            $_GET['order_id'] = explode(',',trim($_GET['order_id'],','));
            $condition['order_id'] = array('in',$_GET['order_id']);
        }
        $on = '';
        if($_GET['qtype_time'] == "vr_usetime") {
            $this->_get_conditionHx($condition);
            $field = "vr_order.order_id,vr_order.order_sn,vr_order.erp_order_sn,vr_order.chain_id,vr_order.order_from,vr_order.goods_id,vr_order.goods_num,vr_order.goods_price,vr_order.add_time,vr_order.order_amount,vr_order.order_state,vr_order.payment_code,vr_order.payment_time,vr_order.buyer_name,vr_order.buyer_phone,vr_order.buyer_id,vr_order.store_name,vr_order.store_id,vr_order.goods_name,vr_order.vr_indate,vr_order.rcb_amount,vr_order.pd_amount,vr_order.finnshed_time,vr_order.evaluation_state,vr_order.refund_amount,vr_order.is_dis,vr_order.dis_member_id,vr_order.trade_no,vr_order.order_type";
            $on = 'vr_order.order_id = vr_order_code.order_id';
            $order ='vr_order.order_id desc';
        }else{
            $this->_get_condition($condition);
            $field = 'order_id,order_sn,erp_order_sn,chain_id,order_from,goods_id,goods_num,goods_price,add_time,order_amount,order_state,payment_code,payment_time,buyer_name,buyer_phone,buyer_id,store_name,store_id,goods_name,vr_indate,rcb_amount,pd_amount,finnshed_time,evaluation_state,refund_amount,is_dis,dis_member_id,trade_no,vr_order.order_type';
            $order ='order_id desc';
        }

        $like_arr = [];
        if (isset($_GET['tag_title'])) {
            foreach ($_GET['tag_title'] as $v){
                if ($v) {
                    $like_arr[] = ['eq', $v];
                }
            }
            if($like_arr) array_push($like_arr, 'or');
        }

        if (!is_numeric($_GET['curpage'])){
            $count = $model_vr_order->getVrOrderCodeCount($condition, $on, $like_arr);
            $array = array();
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','index.php?act=vr_order&op=index');
                Tpl::showpage('export.excel');
            }else{  //如果数量小，直接下载
                $data = $model_vr_order->getOrderList($condition,'',$field,$order,self::EXPORT_SIZE,array('chain','tags'),$like_arr, $on);
                $this->createExcel2($data);
                //$this->export_csv($data);
            }
        }else{  //下载
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $data = $model_vr_order->getOrderList($condition,'',$field, $order,"{$limit1},{$limit2}",array('chain','tags'),$like_arr, $on);
            $this->createExcel2($data);
        }
    }

    /**
     * 生成excel
     *
     * @param array $data
     */
    private function createExcel2($data = array()){
        Language::read('export');
        import('libraries.excel');
        $excel_obj = new Excel();
        $excel_data = array();
        //设置样式
        $excel_obj->setStyle(array('id'=>'s_title','Font'=>array('FontName'=>'宋体','Size'=>'12','Bold'=>'1')));
        //header
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单编号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'子订单号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单类型');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单来源');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'下单时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单金额(元)');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'商品单价(元)');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'商品数量');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'商品标签');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'订单状态');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'支付方式');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'交易单号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'支付时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'买家账号');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'接收手机');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'分销员ID');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'买家ID');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'商品');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'有效期');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'完成时间');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'是否评价');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'退款金额(元)');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'门店名称');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'门店财务编码');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'门店ID');
        $excel_data[0][] = array('styleid'=>'s_title','data'=>'分销订单');

        foreach ((array)$data as $k=>$order_info){
            $list = array();
            $list['order_sn'] = $order_info['order_sn'];
            $list['erp_order_sn'] = $order_info['erp_order_sn']?:'';
            $list['trade_no'] = $order_info['trade_no'] ?:'';
            $list['order_from'] = orderFromName($order_info['order_from'], 1);
            $list['add_time'] = date('Y-m-d H:i:s',$order_info['add_time']);
            $list['order_amount'] = ncPriceFormat($order_info['order_amount']);
            $list['goods_price'] = ncPriceFormat($order_info['goods_price']);
            $list['goods_num'] = $order_info['goods_num'];
            $list['goods_tags'] = $order_info['goods_tags']['goods_tag_title'];
            $list['order_state'] = $order_info['state_desc'];
            $list['payment_code'] = orderPaymentName($order_info['payment_code']);
            $list['payment_time'] = !empty($order_info['payment_time']) ? (intval(date('His',$order_info['payment_time'])) ? date('Y-m-d H:i:s',$order_info['payment_time']) : date('Y-m-d',$order_info['payment_time'])) : '';
            $list['buyer_name'] = $order_info['buyer_name'];
            $list['buyer_phone'] = $order_info['buyer_phone'];
            $list['dis_member_id'] = $order_info['dis_member_id'] > 0 ? $order_info['dis_member_id'] : '';
            $list['buyer_id'] = $order_info['buyer_id'];
            $list['goods_name'] = htmlentities($order_info['goods_name']);
            $list['vr_indate'] = !empty($order_info['vr_indate']) ? date('Y-m-d H:i:s',$order_info['vr_indate']) : '';
            $list['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s',$order_info['finnshed_time']) : '';
            $list['evaluation_state'] = str_replace(array(0,1,2), array('未评价','已评价','未评价'),$order_info['evaluation_state']);
            $list['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
            if($order_info['extend_chain']) {
                $list['chain_name'] = $order_info['extend_chain']['chain_name'];
                $list['account_id'] = $order_info['extend_chain']['account_id'];
            }else{
                $list['chain_name'] = "";
            }
            $list['chain_id'] = $order_info['chain_id'];
            $list['is_dis'] =$order_info['is_dis']==1?'是':'否';
            if(C('member_mobile_on')){
                $list['buyer_phone'] = encryptShow($list['buyer_phone'],4,4);
            }

            $tmp = array();
            $tmp[] = array('data'=>$list['order_sn']);
            $tmp[] = array('data'=>$list['erp_order_sn']);
            $tmp[] = array('data'=> Logic('order')->getOrderTypeText($order_info['order_type'],false));
            $tmp[] = array('data'=>$list['order_from']);
            $tmp[] = array('data'=>$list['add_time']);
            $tmp[] = array('data'=>$list['order_amount']);
            $tmp[] = array('data'=>$list['goods_price']);
            $tmp[] = array('data'=>$list['goods_num']);
            $tmp[] = array('data'=>$list['goods_tags']);
            $tmp[] = array('data'=>$list['order_state']);
            $tmp[] = array('data'=>$list['payment_code']);
            $tmp[] = array('data'=>$list['trade_no']);
            $tmp[] = array('data'=>$list['payment_time']);
            $tmp[] = array('data'=>$list['buyer_name']);
            $tmp[] = array('data'=>$list['buyer_phone']);
            $tmp[] = array('data'=>$list['dis_member_id']);
            $tmp[] = array('data'=>$list['buyer_id']);
            $tmp[] = array('data'=>$list['goods_name']);
            $tmp[] = array('data'=>$list['vr_indate']);
            $tmp[] = array('data'=>$list['finnshed_time']);
            $tmp[] = array('data'=>$list['evaluation_state']);
            $tmp[] = array('data'=>$list['refund_amount']);
            $tmp[] = array('data'=>$list['chain_name']);
            $tmp[] = array('data'=>$list['account_id']);
            $tmp[] = array('data'=>$list['chain_id']);
            $tmp[] = array('data'=>$list['is_dis']);
            $excel_data[] = $tmp;
        }
        $excel_data = $excel_obj->charset($excel_data,CHARSET);
        $excel_obj->addArray($excel_data);
        $excel_obj->addWorksheet($excel_obj->charset(L('exp_od_order'),CHARSET));
        $excel_obj->generateXML('order-'.$_GET['curpage'].'-'.date('Y-m-d-H',time()));
        $this->log('导出虚拟订单数据');
    }

    private function export_csv($data = array()){
        Language::read('export');
        import('libraries.csv');
        $csv_obj = new Csv();
        $csv_data = array();
        $csv_data[0]['order_sn'] = '订单编号';
        $csv_data[0]['order_from'] = '订单来源';
        $csv_data[0]['add_time'] = '下单时间';
        $csv_data[0]['order_amount'] = '订单金额(元)';
        $csv_data[0]['order_state'] = '订单状态';
        $csv_data[0]['payment_code'] = '支付方式';
        $csv_data[0]['payment_time'] = '支付时间';
        $csv_data[0]['buyer_name'] = '买家账号';
        $csv_data[0]['buyer_phone'] = '接收手机';
        $csv_data[0]['buyer_id'] = '买家ID';
        $csv_data[0]['store_name'] = '店铺名称';
        $csv_data[0]['store_id'] = '店铺ID';
        $csv_data[0]['goods_name'] = '商品';
        $csv_data[0]['vr_indate'] = '有效期';
        $csv_data[0]['finnshed_time'] = '完成时间';
        $csv_data[0]['evaluation_state'] = '是否评价';
        $csv_data[0]['refund_amount'] = '退款金额(元)';
        foreach ((array)$data as $k=>$order_info){
            $list = array();
            $list['order_sn'] = $order_info['order_sn'];
            $list['order_from'] = orderFromName($order_info['order_from'], 1);
            $list['add_time'] = date('Y-m-d H:i:s',$order_info['add_time']);
            $list['order_amount'] = ncPriceFormat($order_info['order_amount']);
            $list['order_state'] = $order_info['state_desc'];
            $list['payment_code'] = orderPaymentName($order_info['payment_code']);
            $list['payment_time'] = !empty($order_info['payment_time']) ? (intval(date('His',$order_info['payment_time'])) ? date('Y-m-d H:i:s',$order_info['payment_time']) : date('Y-m-d',$order_info['payment_time'])) : '';
            $list['buyer_name'] = $order_info['buyer_name'];
            $list['buyer_phone'] = $order_info['buyer_phone'];
            $list['buyer_id'] = $order_info['buyer_id'];
            $list['store_name'] = $order_info['store_name'];
            $list['store_id'] = $order_info['store_id'];
            $list['goods_name'] = $order_info['goods_name'];
            $list['vr_indate'] = !empty($order_info['vr_indate']) ? date('Y-m-d H:i:s',$order_info['vr_indate']) : '';
            $list['finnshed_time'] = !empty($order_info['finnshed_time']) ? date('Y-m-d H:i:s',$order_info['finnshed_time']) : '';
            $list['evaluation_state'] = str_replace(array(0,1,2), array('未评价','已评价','未评价'),$order_info['evaluation_state']);
            $list['refund_amount'] = ncPriceFormat($order_info['refund_amount']);
            if(C('member_mobile_on')){
                $list['buyer_phone'] = encryptShow($list['buyer_phone'],4,4);
            }

            $csv_data[] = $list;
        }
        $csv_data = $csv_obj->charset($csv_data,CHARSET,'gbk');
        $csv_obj->filename='order-'.$_GET['curpage'].'-'.date('Y-m-d-H',time());
        //$csv_obj->export($export_data);
        $csv_obj->export($csv_data);

    }

    /**
     * 处理搜索条件
     */
    private function _get_condition(& $condition) {
        $_REQUEST['query'] = trim($_REQUEST['query']);
        $_GET['keyword'] = trim($_GET['keyword']);

        if ($_REQUEST['query'] != '' && in_array($_REQUEST['qtype'],array('order_sn','erp_order_sn','store_name','store_id','buyer_name','trade_no','goods_name'))) {
            if($_REQUEST['qtype'] == 'goods_name'){
                $condition['goods_id'] = ['in raw',"select goods_id from upet_goods where goods_name like '%{$_REQUEST['query']}%'"];
            }else{
                $condition[$_REQUEST['qtype']] = array('eq',"{$_REQUEST['query']}");
            }
        }
        if ($_GET['keyword'] != '' && in_array($_GET['keyword_type'],array('order_sn','erp_order_sn','store_name','buyer_name','trade_no','goods_name'))) {
            if ($_GET['keyword_type'] == 'goods_name') {
                $condition['goods_id'] = ['in raw', "select goods_id from upet_goods where goods_name like '%{$_GET['keyword']}%'"];
            } else {
                $condition[$_GET['keyword_type']] = $_GET['keyword'];
            }
        }
        if (!in_array($_GET['qtype_time'],array('add_time','payment_time','finnshed_time','vr_usetime'))) {
            $_GET['qtype_time'] = null;
        }
        $if_start_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_start_date']);
        $if_end_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_end_date']);
        $start_unixtime = $if_start_time ? strtotime($_GET['query_start_date']) : null;
        $end_unixtime = $if_end_time ? strtotime($_GET['query_end_date']): null;
        if ($_GET['qtype_time'] && ($start_unixtime || $end_unixtime)) {
            $condition[$_GET['qtype_time']] = array('time',array($start_unixtime,$end_unixtime));
        }
        if($_GET['payment_code']) {
            $condition['payment_code'] = $_GET['payment_code'];
        }
        if(in_array($_GET['order_state'],array('0','10','20','40'))){
            $condition['order_state'] = $_GET['order_state'];
        }
        if (floatval($_GET['query_start_amount']) > 0 && floatval($_GET['query_end_amount']) > 0) {
            $condition['order_amount'] = array('between',floatval($_GET['query_start_amount']).','.floatval($_GET['query_end_amount']));
        }
        if ($_GET['order_from'] > 0) {
            if ($_GET['order_from'] == 99) { // 视频号
                $condition['is_live'] = 2;
                $condition['payment_code'] = 'wx_jsapi';
            } else {
                $condition['order_from'] = $_GET['order_from'];
            }
        }
        if($_REQUEST['order_type']){
            if(intval($_REQUEST['order_type']) == 14){
                $condition['vr_order.order_type'] = ['in','14,16'];
            }else{
                $condition['vr_order.order_type'] = intval($_REQUEST['order_type']);
            }

        }
        $condition['store_id'] = $_SESSION['store_id'];
    }

    /**
     * 处理搜索条件
     */
    private function _get_conditionHx(& $condition) {
        $_REQUEST['query'] = trim($_REQUEST['query']);
        $_GET['keyword'] = trim($_GET['keyword']);

        if ($_REQUEST['query'] != '' && in_array($_REQUEST['qtype'],array('order_sn','store_name','store_id','buyer_name','trade_no','goods_name'))) {
            if($_REQUEST['qtype'] == 'goods_name'){
                $condition['vr_order.goods_id'] = ['in raw',"select goods_id from upet_goods where goods_name like '%{$_REQUEST['query']}%'"];
            }else{
                $condition['vr_order.'.$_REQUEST['qtype']] = array('like',"%{$_REQUEST['query']}%");
            }
        }
        if ($_GET['keyword'] != '' && in_array($_GET['keyword_type'], array('order_sn', 'store_name', 'buyer_name','trade_no', 'goods_name'))) {
            if ($_GET['keyword_type'] == 'goods_name') {
                $condition['vr_order.goods_id'] = ['in raw', "select goods_id from upet_goods where goods_name like '%{$_GET['keyword']}%'"];
            } else {
                $condition['vr_order.' . $_GET['keyword_type']] = $_GET['keyword'];
            }
        }
        if (!in_array($_GET['qtype_time'],array('add_time','payment_time','finnshed_time','vr_usetime'))) {
            $_GET['qtype_time'] = null;
        }
        $if_start_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_start_date']);
        $if_end_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_end_date']);
        $start_unixtime = $if_start_time ? strtotime($_GET['query_start_date']) : null;
        $end_unixtime = $if_end_time ? strtotime($_GET['query_end_date']): null;
        if ($_GET['qtype_time'] && ($start_unixtime || $end_unixtime)) {
        	
        	if($_GET['qtype_time']=="vr_usetime"){
        		$condition['vr_order_code.'.$_GET['qtype_time']] = array('time',array($start_unixtime,$end_unixtime));
        	}else{
        		$condition['vr_order.'.$_GET['qtype_time']] = array('time',array($start_unixtime,$end_unixtime));
        	}
            
        }
        if($_GET['payment_code']) {
            $condition['vr_order.payment_code'] = $_GET['payment_code'];
        }
        if(in_array($_GET['order_state'],array('0','10','20','40'))){
            $condition['vr_order.order_state'] = $_GET['order_state'];
        }
        if (floatval($_GET['query_start_amount']) > 0 && floatval($_GET['query_end_amount']) > 0) {
            $condition['vr_order.order_amount'] = array('between',floatval($_GET['query_start_amount']).','.floatval($_GET['query_end_amount']));
        }
        if ($_GET['order_from'] > 0) {
            if ($_GET['order_from'] == 99) { // 视频号
                $condition['vr_order.is_live'] = 2;
                $condition['vr_order.payment_code'] = 'wx_jsapi';
            } else {
                $condition['vr_order.order_from'] = $_GET['order_from'];
            }
        }

        if($_REQUEST['order_type']){
            if(intval($_REQUEST['order_type']) == 14){
                $condition['vr_order.order_type'] = ['in','14,16'];
            }else{
                $condition['vr_order.order_type'] = intval($_REQUEST['order_type']);
            }
        }

        $condition['vr_order.store_id'] = $_SESSION['store_id'];

    }
}
