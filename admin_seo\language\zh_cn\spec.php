<?php
defined('InShopNC') or exit('Access Invalid!');
/**
 * index
 */
$lang['spec_index_spec_list']		= '规格列表';
$lang['spec_index_spec_name']		= '规格';
$lang['spec_index_spec_value']		= '规格值';
$lang['spec_index_no_checked']		= '请选择要操作的数据项。';
$lang['spec_index_prompts_one']		= '规格将会对应到商品发布的规格，规格值由店铺自己添加。';
$lang['spec_index_prompts_two']		= '默认安装中会添加一个默认颜色规格，请不要删除，只有这个颜色规格才能在商品详细页显示为图片。';
/**
 * 新增规格
 */
$lang['spec_add_spec_add']			= '添加规格值';
$lang['spec_add_name_no_null']		= '请填写规格名称';
$lang['spec_add_name_max']			= '规格名称长度应在1-10个字符之间';
$lang['spec_add_sort_no_null']		= '请填写规格排序';
$lang['spec_add_sort_no_digits']	= '请填写整数';
$lang['spec_index_continue_to_dd']	= '继续添加规格';
$lang['spec_index_return_type_list']= '返回规格列表';
$lang['spec_index_spec_name_desc']	= '请填写常用的商品规格的名称；例如：颜色；尺寸等。';
$lang['spec_index_spec_sort_desc']	= '请填写自然数。规格列表将会根据排序进行由小到大排列显示。';
$lang['spec_common_belong_class']	= '快捷定位';
$lang['spec_common_belong_class_tips']	= '选择分类，可关联到任意级分类。（只在后台快捷定位中起作用）';
/**
 * 编辑规格
 */
$lang['spec_edit_spec_value_null']	= '还没有添加规格值信息。';
