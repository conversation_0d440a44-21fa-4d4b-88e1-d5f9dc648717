<?php

use Shopnc\Tpl;
use Upet\Models\PayInfo;
use Upet\Models\VrOrder;
use Upet\Models\Member;
use Upet\Models\OrderMain;
use Upet\Models\Datacenter\VipCardOrder;
use Upet\Models\Datacenter\MemberPropertyGuaranteeQuota;
use Upet\Models\Datacenter\MemberPropertyGuaranteeQuotaDetail;

defined('InShopNC') or exit('Access Invalid!');

class order_delay_deliveryControl extends SystemControl
{
    private $links = array(
        array('url' => 'act=order_delay_delivery&op=index', 'text' => '订单延迟收货'),
        array('url' => 'act=order_delay_delivery&op=updateOrderDelivery', 'text' => '订单全部发货'),
        array('url' => 'act=order_delay_delivery&op=updateCycleOrderGoods', 'text' => '周期购更改商品发货'),
        array('url' => 'act=order_delay_delivery&op=checkRefundOrder', 'text' => '查询支付中心是否已退款'),
        array('url' => 'act=order_delay_delivery&op=vrOrderInvalidRefundFinish', 'text' => '过期虚拟订单退款完成'),
        array('url' => 'act=order_delay_delivery&op=transferVipCard', 'text' => '健康会员卡转移'),
        array('url' => 'act=order_delay_delivery&op=updateVipCard', 'text' => '更新会员卡分销'),
        array('url' => 'act=order_delay_delivery&op=update_vip_card_state', 'text' => '更新会员标识'),
        array('url' => 'act=order_delay_delivery&op=update_vip_card_data', 'text' => '恢复会员身份'),
    );

    public function __construct()
    {
        parent::__construct();
        Tpl::output('top_title', '订单问题管理');
        Tpl::output('top_desc', '针对常见的问题处理工具');
        Tpl::output('top_link', $this->sublink($this->links, $_GET['op']));
        switch ($_GET['op']) {
            case 'updateorderdelivery':
                $html = '<li><a href="index.php?act=order_delay_delivery&op=index"><span>订单延迟收货</span></a></li>
        <li><a class="current"><span>订单全部发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateCycleOrderGoods"><span>周期购更改商品发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=checkRefundOrder"><span>查询支付中心是否已退款</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=vrOrderInvalidRefundFinish"><span>过期虚拟订单退款完成</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=transferVipCard"><span>健康会员卡转移</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateVipCard"><span>更新会员卡主动购买</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_state"><span>更新会员标识</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_data"><span>恢复会员身份</span></a></li>
        ';
                break;
            case 'updatecycleordergoods':
                $html = '<li><a href="index.php?act=order_delay_delivery&op=index"><span>订单延迟收货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateOrderDelivery"><span>订单全部发货</span></a></li>
        <li><a class="current"><span>周期购更改商品发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=checkRefundOrder"><span>查询支付中心是否已退款</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=vrOrderInvalidRefundFinish"><span>过期虚拟订单退款完成</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=transferVipCard"><span>健康会员卡转移</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateVipCard"><span>更新会员卡主动购买</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_state"><span>更新会员标识</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_data"><span>恢复会员身份</span></a></li>
        ';
                break;
            case 'checkrefundorder':
                $html = '<li><a href="index.php?act=order_delay_delivery&op=index"><span>订单延迟收货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateOrderDelivery"><span>订单全部发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateCycleOrderGoods"><span>周期购更改商品发货</span></a></li>
        <li><a class="current"><span>查询支付中心是否已退款</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=vrOrderInvalidRefundFinish"><span>过期虚拟订单退款完成</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=transferVipCard"><span>健康会员卡转移</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateVipCard"><span>更新会员卡主动购买</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_state"><span>更新会员标识</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_data"><span>恢复会员身份</span></a></li>';
                break;
            case 'vrorderinvalidrefundfinish':
                $html = '<li><a href="index.php?act=order_delay_delivery&op=index"><span>订单延迟收货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateOrderDelivery"><span>订单全部发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateCycleOrderGoods"><span>周期购更改商品发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=checkRefundOrder"><span>查询支付中心是否已退款</span></a></li>
        <li><a class="current"><span>过期虚拟订单退款完成</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=transferVipCard"><span>健康会员卡转移</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateVipCard"><span>更新会员卡主动购买</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_state"><span>更新会员标识</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_data"><span>恢复会员身份</span></a></li>';
                break;
            case 'transfervipcard':
                $html = '<li><a href="index.php?act=order_delay_delivery&op=index"><span>订单延迟收货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateOrderDelivery"><span>订单全部发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateCycleOrderGoods"><span>周期购更改商品发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=checkRefundOrder"><span>查询支付中心是否已退款</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=vrOrderInvalidRefundFinish"><span>过期虚拟订单退款完成</span></a></li>
        <li><a class="current"><span>健康会员卡转移</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateVipCard"><span>更新会员卡主动购买</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_state"><span>更新会员标识</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_data"><span>恢复会员身份</span></a></li>';
                break;
            case 'updatevipcard':
                $html = '<li><a href="index.php?act=order_delay_delivery&op=index"><span>订单延迟收货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateOrderDelivery"><span>订单全部发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateCycleOrderGoods"><span>周期购更改商品发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=checkRefundOrder"><span>查询支付中心是否已退款</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=vrOrderInvalidRefundFinish"><span>过期虚拟订单退款完成</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=transferVipCard"><span>健康会员卡转移</span></a></li>
        <li><a class="current"><span>更新会员卡主动购买</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_state"><span>更新会员标识</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_data"><span>恢复会员身份</span></a></li>';
                break;
            case 'update_vip_card_state':
                $html = '<li><a href="index.php?act=order_delay_delivery&op=index"><span>订单延迟收货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateOrderDelivery"><span>订单全部发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateCycleOrderGoods"><span>周期购更改商品发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=checkRefundOrder"><span>查询支付中心是否已退款</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=vrOrderInvalidRefundFinish"><span>过期虚拟订单退款完成</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=transferVipCard"><span>健康会员卡转移</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateVipCard"><span>更新会员卡主动购买</span></a></li>
        <li><a class="current"><span>更新会员标识</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_data"><span>恢复会员身份</span></a></li>';
                break;
            case 'update_vip_card_data':
                $html = '<li><a href="index.php?act=order_delay_delivery&op=index"><span>订单延迟收货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateOrderDelivery"><span>订单全部发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateCycleOrderGoods"><span>周期购更改商品发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=checkRefundOrder"><span>查询支付中心是否已退款</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=vrOrderInvalidRefundFinish"><span>过期虚拟订单退款完成</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=transferVipCard"><span>健康会员卡转移</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateVipCard"><span>更新会员卡主动购买</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_state"><span>更新会员标识</span></a></li>
        <li><a class="current"><span>恢复会员身份</span></a></li>';
                break;
           default:
                $html = '<li><a class="current"><span>订单延迟收货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateOrderDelivery"><span>订单全部发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateCycleOrderGoods"><span>周期购更改商品发货</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=checkRefundOrder"><span>查询支付中心是否已退款</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=vrOrderInvalidRefundFinish"><span>过期虚拟订单退款完成</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=transferVipCard"><span>健康会员卡转移</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=updateVipCard"><span>更新会员卡主动购买</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_state"><span>更新会员标识</span></a></li>
        <li><a href="index.php?act=order_delay_delivery&op=update_vip_card_data"><span>恢复会员身份</span></a></li>';                break;
        }
        Tpl::output('top_tab', $html);
    }

    /**
     * Notes:订单延迟收货
     * User: rocky
     * DateTime: 2022/1/7 15:06
     */
    public function indexOp()
    {
        if ($_POST['order_sn']) {
            $model_order = Model('order');
            $order = $model_order->getOrderInfo(array('order_sn' => $_POST['order_sn']));
            if (empty($order)) {
                showMessage('订单不存在', '');
            }
            $model_order->editOrder(['delay_time' => time()], array('order_sn' => $_POST['order_sn']));
            //日志
            $this->log('订单延时发货,订单号：' . $_POST['order_sn']);
            showMessage('操作成功', '');
        }

        Tpl::showpage('order_delay_delivery');
    }

    /**
     * 更新实物订单为全部发货
     */
    public function updateOrderDeliveryOp()
    {
        if ($_POST['order_sn']) {
            $model_order = Model('order');
            $order = $model_order->getOrderInfo(array('order_sn' => $_POST['order_sn']));
            if (empty($order)) {
                showMessage('订单不存在', '');
            }
            $model_order->editOrder(['order_state' => 30], array('order_sn' => $_POST['order_sn']));
            //日志
            $this->log('更新实物订单为全部发货,订单号：' . $_POST['order_sn']);
            showMessage('操作成功', '');
        }
        Tpl::showpage('update_order_delivery');
    }

    /**
     * Notes:周期购订单推送的商品停产，需更改其它商品重推
     * goods_sku 商品货号
     * order_sn 订单号
     * User: rocky
     * DateTime: 2021/12/21 16:07
     */
    public function updateCycleOrderGoodsOp(){
        if($_POST['order_sn'] && $_POST['goods_sku']) {
            $goods_info = Model('goods')->where(['goods_sku' => $_POST['goods_sku']])->find();
            if (!$goods_info) {
                showMessage('商品不存在', '');
            }
            $where = [];
            $where['order_sn'] = $_POST['order_sn'];
            $where['is_head'] = 0;
            $order = Model('cycle_push_info')->where($where)->find();
            if (!$order) {
                showMessage('订单不存在', '');
            }
            if ($order['status'] == 1) {
                showMessage('订单已推送', '');
            }
            $order_data = json_decode(stripslashes($order['push_param']), true);
            foreach ($order_data as $k => $v) {
                if ($k == 'order_products') {
                    $order_data['order_products'][0]['goods_id'] = $goods_info['goods_id'];
                    $order_data['order_products'][0]['sku'] = $goods_info['goods_id'];
                    $order_data['order_products'][0]['product_id'] = $goods_info['goods_id'];
                    $order_data['order_products'][0]['goods_commonid'] = $goods_info['goods_commonid'];
                }
                if ($k == 'pay_info') {
                    $order_data['pay_info'] = (object)[];
                }
            }
            $push_data = json_encode($order_data, JSON_UNESCAPED_UNICODE);
            $res = Model('cycle_push_info')->where(['order_sn' => $_POST['order_sn']])->update(['push_param' => $push_data]);
            if ($res) {
                showMessage('操作成功', '');
            }else{
                showMessage('操作失败', '');
            }
        }
        Tpl::showpage('update_cycle_ordergoods');
    }

    /**
     * Notes:查询支付中心是否已退款
     * User: rocky
     * DateTime: 2022/1/10 10:34
     */
    public function checkRefundOrderOp()
    {
        if ($_POST['order_sn']) {
            $model_order = Model('order');
            $model_vr_order = Model('vr_order');
            $order = $model_order->getOrderInfo(array('order_sn' => $_POST['order_sn']));
            if (empty($order)) {
                $vr_order = $model_vr_order->getOrderInfo(array('order_sn' => $_POST['order_sn']));
                if (empty($vr_order)){
                    showMessage('订单号不存在', '');
                }
                $order_id = $vr_order['order_sn'];
            }else{
                $order_id = $order['pay_sn'];
            }

            $pay_info = PayInfo::where(['order_id'=>$order_id])->find();
            if (empty($pay_info)) {
                showMessage('支付信息不存在', '');
            }
            switch ($pay_info['status']) {
                case 0:
                    showMessage('未支付', '');
                case 1:
                    showMessage('已支付', '');
                    break;
                case 2:
                    showMessage('部分退款', '');
                    break;
                case 3:
                    showMessage('全部退款', '');
                    break;
                case 4:
                    showMessage('取消支付', '');
                    break;
                default:
                    showMessage('状态异常', '');
                    break;
            }
        }
        Tpl::showpage('check_refund_order');
    }

    // 虚拟订单过期退款完成
    public function vrOrderInvalidRefundFinishOp()
    {
        if ($orderSn = trim($_POST['order_sn'])) {
            if(!preg_match('/^\d+(,\d+)*$/',$orderSn)){
                showMessage('订单号格式不正确');
            }

            $sns = array_filter(explode(',', $orderSn));
            if (empty($sns)) {
                showMessage('订单号不能为空');
            }

            $rs = VrOrder::alias('o')
                ->join('upet_vr_order_code c', 'c.order_id = o.order_id')
                ->leftJoin('dc_order.order_verify_code oc', 'oc.verify_code = c.vr_code')
                ->whereRaw('o.order_sn in ('.implode(',',$sns).')')
                ->where('o.payment_code', 'wx_jsapi')
                ->where('o.payment_time', '<=', strtotime('-365 day'))
                ->where('o.order_state', 20)
                ->whereIn('c.vr_state', [0, 2])
                ->where('c.vr_indate', '<', time())
                ->update([
                    'o.order_state' => ORDER_STATE_SUCCESS,
                    'o.finnshed_time' => time(),
                    'c.refund_lock' => 2,
                    'oc.verify_status' => 2,
                ]);

            showMessage('操作成功，影响' . $rs . '行。');
        }

        Tpl::showpage('vr_order_invalid_refund_finish');
    }

    /**
     * Notes:转移会员卡信息
     * User: rocky
     * DateTime: 2024/2/2 16:48
     */
    public function transferVipCardOp(){
        if ($_POST['order_sn'] && $_POST['old_mobile'] && $_POST['new_mobile']) {
            if(!in_array($this->admin_info['gid'],[0,1,17])){
                showMessage('无权限操作');
            }
            $data = $_POST;
            $old_member = Member::field('member_id,member_name,scrm_user_id,vip_card_state')->where(['member_mobile' => $data['old_mobile']])->find();
            $new_member = Member::field('member_id,member_name,scrm_user_id,vip_card_state')->where(['member_mobile' => $data['new_mobile']])->find();
            if (!$old_member || $old_member['vip_card_state'] != 1) {
                showMessage('旧手机号不存在or未开通会员卡');
            }
            if (!$new_member || $new_member['vip_card_state'] != 0) {
                showMessage('新手机号不存在or已开通会员卡');
            }

            $vip_card_info = VipCardOrder::where('order_sn', $data['order_sn'])->find();
            if (!$vip_card_info || $vip_card_info['user_id'] != $old_member['scrm_user_id']) {
                showMessage('订单号不存在或订单号与旧手机号不匹配');
            }

            //判断订单用户跟旧手号是否一致
            if ($vip_card_info['user_id'] != $old_member['scrm_user_id']) {
                showMessage('订单购买与旧手机号不匹配');
            }

            //新手机号加密
            $new_phone_rc4 = base64_encode(rc4($data['new_mobile']));
            $new_phone_star = mobile_star($data['new_mobile']);

            $res1 = Member::where('member_id', $old_member['member_id'])->update(['vip_card_state' => 0]);
            if (!$res1) {
                showMessage('旧手机号会员卡状态更新失败');
            }
            $res2 = Member::where('member_id', $new_member['member_id'])->update(['vip_card_state' => 1]);
            if (!$res2) {
                showMessage('新手机号会员卡状态更新失败');
            }
            $res3 = VipCardOrder::where('order_sn', $data['order_sn'])->where('user_id', $old_member['scrm_user_id'])
                ->update(['user_id' => $new_member['scrm_user_id'], 'user_mobile' => $new_phone_star, 'en_user_mobile' => $new_phone_rc4]);
            if (!$res3) {
                showMessage('会员卡订单更新失败');
            }
            $res4 = VrOrder::where('erp_order_sn', $data['order_sn'])->update(['buyer_id' => $new_member['member_id'], 'buyer_name' => $new_member['member_name']]);

            if (!$res4) {
                showMessage('虚拟订单更新失败');
            }
            $res5 = OrderMain::where('order_sn', $data['order_sn'])->update([
                'member_id' => $new_member['scrm_user_id'],
                'member_tel' => $new_phone_star,
                'en_member_tel' => $new_phone_rc4,
                'receiver_mobile' => $new_phone_star,
                'en_receiver_mobile' => $new_phone_rc4,
            ]);
            if (!$res5) {
                showMessage('订单更新失败');
            }
            $res6 = MemberPropertyGuaranteeQuota::where('insurance_policy_number', $data['order_sn'])->update(['member_id' => $new_member['scrm_user_id']]);
            if (!$res6) {
                showMessage('保障额度更新失败');
            }
            $res7 = MemberPropertyGuaranteeQuotaDetail::where('insurance_policy_number', $data['order_sn'])->update(['member_id' => $new_member['scrm_user_id']]);
            if (!$res7) {
                showMessage('保障额度明细更新失败');
            }
            $this->log('会员卡转移,订单号：' . $data['order_sn']);
            showMessage('成功');
        }

        Tpl::showpage('transfer_vip_card');
    }

    /**
     * Notes:更新健康会员卡为主动购买
     * User: rocky
     * DateTime: 2024/2/4 12:23
     */
    public function updateVipCardOp(){
        if ($_POST['order_sn']) {
            if(!in_array($this->admin_info['gid'],[0,1,17])){
                showMessage('无权限操作');
            }
            $res = VrOrder::execute(sprintf("update upet_vr_order v left join datacenter.vip_card_order o 
on v.erp_order_sn = o.order_sn set v.chain_id =0,v.is_dis=0,v.dis_commis_rate =0,v.dis_member_id=0,v.dis_type=0,
                                   o.region='',o.province='',o.source =0,o.city='',o.dis_member_name='',o.dis_member_id=0,
                                   o.dis_commission=0 where v.erp_order_sn = '%s';",$_POST['order_sn']));
            if ($res) {
                showMessage('操作成功');
            } else {
                showMessage('操作失败');
            }
        }
        $this->log('更新健康会员卡为主动购买:'.$_POST['order_sn']);
        Tpl::showpage('update_vip_card');
    }

    /**
     * Notes:更新用户会员标识
     * User: rocky
     * DateTime: 2023/8/3 10:08
     */
    public function update_vip_card_stateOp(){
        if ($_POST['member_mobile']) {
            if(!in_array($this->admin_info['gid'],[0,1,17])){
                showMessage('无权限操作');
            }
            $member_mobile = $_POST['member_mobile'];
            $state = $_POST['state'];
            $res = Model('member')->where(['member_mobile' => $member_mobile])->update(['vip_card_state' => $state]);
            if ($res) {
                showMessage('操作成功');
            } else {
                showMessage('操作失败');
            }
            $this->log('更新用户会员标识:'.$_POST['member_mobile'].'-'.$state);
        }
        Tpl::showpage('update_vip_card_state');
    }

    /**
     * @desc 恢复用户会员卡
     */
    public function update_vip_card_dataOp()
    {
        if ($_POST['member_mobile']) {
            $member_mobile = $_POST['member_mobile'];
            $order_sn = $_POST['order_sn'];
            if (!$member_mobile || !$order_sn) {
                showMessage('Bad Request by params');
            }
            $res = Model('member')->field('member_id,member_mobile,scrm_user_id,member_name')->where(['member_mobile' => $member_mobile])->find();
            if (!$res) {
                showMessage('无用户信息');
            }

            //开启事务
            VrOrder::startTrans();
            try {
                $vr_res = VrOrder::where(['erp_order_sn' => $order_sn])->update([
                    'buyer_id' => $res['member_id'],
                    'buyer_name' => $res['member_name'],
                    'buyer_phone' => mobile_star($res['member_mobile']),
                    'encrypt_mobile' => base64_encode(rc4($res['member_mobile'])),
                ]);
//                echo VrOrder::getLastSql();exit;
                if (!$vr_res) {
                    log_error('更新VrOrder数据失败', ['order_sn' => $order_sn, 'res' => $vr_res]);
                }

                $vip_res = VipCardOrder::where(['order_sn' => $order_sn])->update([
                    'user_id' => $res['scrm_user_id'],
                    'user_name' => $res['member_name'],
                    'user_mobile' => mobile_star($res['member_mobile']),
                    'en_user_mobile' => base64_encode(rc4($res['member_mobile'])),
                ]);
                if (!$vip_res) {
                    log_error('更新VipCardOrder数据失败', ['order_sn' => $order_sn, 'res' => $vip_res]);
                }

                $pet_res = \Upet\Models\Datacenter\MemberPetProperty::where(['mall_order_sn' => $order_sn])->update(['member_id' => $res['scrm_user_id']]);
                if (!$pet_res) {
                    log_error('更新MemberPetProperty数据失败', ['order_sn' => $order_sn, 'res' => $pet_res]);
                }

                $main_res = OrderMain::where(['order_sn' => $order_sn])->update([
                    'member_id' => $res['scrm_user_id'],
                    'member_name' => $res['member_name'],
                    'member_tel' => mobile_star($res['member_mobile']),
                    'en_member_tel' => base64_encode(rc4($res['member_mobile'])),
                ]);
                if (!$main_res) {
                    log_error('更新OrderMain数据失败', ['order_sn' => $order_sn, 'res' => $main_res]);
                }

                $guarantee_res = \Upet\Models\Datacenter\MemberPropertyGuaranteeQuota::where(['insurance_policy_number' => $order_sn])->update(['member_id' => $res['scrm_user_id']]);
                if (!$guarantee_res) {
                    log_error('更新MemberPropertyGuaranteeQuota数据失败', ['order_sn' => $order_sn, 'res' => $guarantee_res]);
                }

                VrOrder::commit();
                showMessage('更新成功');
            } catch (Exception $e) {
                VrOrder::rollback();
                showMessage($e->getMessage());
            }
        }
        Tpl::showpage('update_vip_card_data');
    }
}
