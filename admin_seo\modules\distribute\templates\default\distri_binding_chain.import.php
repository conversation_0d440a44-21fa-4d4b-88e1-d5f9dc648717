<?php defined('InShopNC') or exit('Access Invalid!');?>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title"> <a class="back" href="index.php?act=distri_binding_chain&op=index" title="返回列表"> <i class="fa fa-arrow-circle-o-left"></i> </a>
            <div class="subject">
                <h3>返回列表</h3>
                <h5>操作 - 批量导入</h5>
            </div>
        </div>
    </div>
<div class="explanation" id="explanation">
    <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
        <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
        <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
    <ul>
        <li>
            <div class="bot" id="submit-holder"><a class="ncap-btn-big ncap-btn-green" href="index.php?act=distri_binding_chain&op=get_xml&op=down_export">下载导入模版</a></div>
        </li>
    </ul>
</div>
<form id="cash_form" method="post" action='index.php?act=distri_binding_chain&op=distri_import' enctype="multipart/form-data">
    <input type="hidden" name="form_submit" value="ok" />
    <div class="ncap-form-default" id="explanation">
        <dl class="row">
            <dt class="tit">
                <label><em>*</em>导入表格</label>
            </dt>
            <dd class="opt">
                <ul class="ncsc-form-radio-list">
                    <li><label><input name="cash_export" type="file"></label></li>
                </ul>
            </dd>
        </dl>
        <div class="bot">
            <input type="submit" id="submitBtn" value="确认提交">
            <a href="index.php?act=distri_binding_chain&op=import">查看导入数据</a>
        </div>
    </div>
</form>
</div>
<script src="<?php echo RESOURCE_SITE_URL;?>/js/common_select.js" charset="utf-8"></script>
<script src="<?php echo RESOURCE_SITE_URL;?>/js/jquery.ajaxContent.pack.js" type="text/javascript"></script>
<script>
    $(function(){
        $('#submitBtn').on('click',function(){

        })
    });
</script>