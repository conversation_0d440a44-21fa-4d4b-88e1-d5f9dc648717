<?php defined('InShopNC') or exit('Access Invalid!'); ?>
<style type="text/css">
    .flexigrid .bDiv tr:nth-last-child(2) span.btn ul { bottom: 0; top: auto}
</style>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>宠医云商品</h3>
                <h5>商品列表页面宠医云商品管理</h5>
            </div>
        </div>
    </div>
    <!-- 操作说明 -->
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
            <h4 title="<?php echo $lang['nc_prompts_title']; ?>"><?php echo $lang['nc_prompts']; ?></h4>
            <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span']; ?>"></span></div>
        <ul>
            <li>宠医云商品将显示在宠医云APP首页的云严选栏目。</li>
        </ul>
    </div>
    <form method='post'>
        <input type="hidden" name="form_submit" value="ok"/>
        <input type="hidden" name="submit_type" id="submit_type" value=""/>
        <table class="flex-table">
            <thead>
            <tr>
                <?php if (empty($output['rec_list'])) { ?>
                <th width="200" align="center" class="sign"><i class="ico-check"></i></th>
                <?php }else { ?>
                <th width="24" align="center" class="sign"><i class="ico-check"></i></th>
                <?php  } ?>
                <th width="200" class="handle" align="center"><?php echo $lang['nc_handle']; ?></th>
                <th width="80"  align="left">商品ID</th>
                <th width="300" align="left">商品名称</th>
                <th width="80" align="left">销售价</th>
                <th width="80" align="center">市场价</th>
                <th width="60" align="center">商品图片</th>
                <th width="120" align="center">添加时间</th>
                <th width="50" align="center">状态</th>
                <th width="80" align="center">库存</th>
                <th width="80" align="center">点击量</th>
                <th width="100" align="center">点击后下单数</th>
                <th width="100" align="center">点击转化率</th>
                <th width="60" align="center"><?php echo $lang['nc_sort']; ?></th>
                <th></th>
            </tr>
            </thead>
            <tbody>
            <?php if (!empty($output['rec_list']) && is_array($output['rec_list'])) { ?>
                <?php foreach ($output['rec_list'] as $k => $v) { ?>
                    <tr data-id="<?php echo $v['rec_id']; ?>">
                        <td class="sign" style="width: 24px;"><i class="ico-check"></i></td>
                        <td style="width: 200px;" class="handle"><a class='btn red' onclick="fg_delete(<?php echo $v['rec_id']; ?>)"><i class='fa fa-trash-o'></i>删除</a><a class='btn blue' href='index.php?act=goods_app&op=add&rec_gc_id=<?php echo $v['rec_gc_id']; ?>'><i class='fa fa-pencil-square-o'></i>编辑</a><a class='btn blue' href='index.php?act=goods_app&op=editImg&id=<?php echo $v['rec_id']; ?>'>上传图片</a></td>
                        <td style="width: 80px;"><?php echo $v['rec_goods_id']; ?></td>
                        <td style="width: 300px;"><a href=" <?php echo urlShop('goods', 'index', array('goods_id' => $v['rec_goods_id']));?>" target="_blank"><?php echo $v['goods_name']; ?></a></td>
                        <td style="width: 80px;"><?php echo $v['goods_promotion_price']; ?></td>
                        <td style="width: 80px;"><?php echo $v['goods_marketprice']; ?></td>
                        <td style="width: 60px;"><a href='javascript:void(0);' class='pic-thumb-tip' onMouseOut="toolTip()" onMouseOver='toolTip("<img src="<?php echo $v['goods_image']; ?>">")'><i class='fa fa-picture-o'></i></a></td>
                        <td style="width: 120px;"><?php echo $v['rec_addtime']; ?></td>
                        <td style="width: 50px;"><?php echo str_replace(array(0,1), array('不显示','显示'), $v['rec_state']);?></td>
                        <td style="width: 80px;"><?php echo $v['goods_storage']; ?></td>
                        <td style="width: 80px;"><?php echo $v['rec_views']; ?></td>
                        <td style="width: 100px;"><?php echo $v['order_count']; ?></td>
                        <td style="width: 100px;"><?php echo $v['order_zhl']; ?></td>
                        <td ><span title="<?php echo $lang['nc_editable']; ?>" column_id="<?php echo $v['rec_id']; ?>" fieldname="rec_sorts" nc_type="inline_edit" class="editable "><?php echo $v['rec_sorts']; ?></span></td>
                    </tr>
                <?php } ?>
            <?php } else { ?>
                <tr>
                    <td class="no-data" colspan="14" style="width: 100px;"><i class="fa fa-exclamation-circle"></i><?php echo $lang['nc_no_record']; ?></td>
                </tr>
            <?php } ?>
            </tbody>
        </table>
    </form>

    <!--<div id="flexigrid"></div>-->
</div>
<script type="text/javascript" src="<?php echo ADMIN_RESOURCE_URL; ?>/js/jquery.edit.js" charset="utf-8"></script>
<script type="text/javascript">
    $(function () {
        /*$("#flexigrid").flexigrid({
            url: 'index.php?act=goods_app&op=get_xml',
            colModel: [
                {display: '操作', name: 'operation', width: 150, sortable: false, align: 'center', className: 'handle'},
                {display: '商品ID', name: 'rec_goods_id', width: 80, sortable: false, align: 'left'},
                {display: '商品名称', name: 'goods_name', width: 400, sortable: false, align: 'left'},
                {display: '销售价', name: 'goods_price', width: 100, sortable: true, align: 'center'},
                {display: '市场价', name: 'goods_marketprice', width: 100, sortable: true, align: 'center'},
                {display: '商品图片', name: 'goods_image', width: 60, sortable: true, align: 'center'},
                {display: '添加时间', name: 'rec_addtime', width: 120, sortable: true, align: 'center'},
                {display: '点击量', name: 'rec_views', width: 60, sortable: true, align: 'center'},
                {display: '点击后下单数', name: 'order_count', width: 150, sortable: true, align: 'center'},
                {display: '点击转化率', name: 'order_zhl', width: 100, sortable: true, align: 'center'},
                {display: '推荐排序', name: 'rec_sorts', width: 60, sortable: false, align: 'center'}
            ],
            buttons: [
                {
                    display: '<i class="fa fa-plus"></i>新增数据',
                    name: 'add',
                    bclass: 'add',
                    title: '新增数据',
                    onpress: fg_operate
                }
                /!*{display: '<i class="fa fa-trash"></i>批量删除', name : 'del', bclass : 'del', title : '将选定行数据批量删除', onpress : fg_operate }*!/
            ],
            searchitems: [
                {display: '商品分类名称', name: 'rec_gc_name'}
            ],
            sortname: "rec_id",
            sortorder: "desc",
            title: '宠医云商品列表'
        });*/
        $('.flex-table').flexigrid({
            height:'auto',// 高度自动
            usepager: false,// 不翻页
            striped:false,// 不使用斑马线
            resizable: true,// 不调节大小
            title: '宠医云商品列表',// 表格标题
            reload: false,// 不使用刷新
            columnControl: false,// 不使用列控制
            buttons : [
                {display: '<i class="fa fa-plus"></i>新增数据', name : 'add', bclass : 'add', onpress : fg_operate }
            ]
        });
        $('span[nc_type="inline_edit"]').inline_edit({act: 'goods_app', op: 'ajax'});
    });

    function fg_operate(name, bDiv) {
        if (name == 'del') {
            if ($('.trSelected', bDiv).length > 0) {
                var itemlist = new Array();
                $('.trSelected', bDiv).each(function () {
                    itemlist.push($(this).attr('data-id'));
                });
                fg_delete(itemlist);
            } else {
                return false;
            }
        } else if (name == 'add') {
            window.location.href = 'index.php?act=goods_app&op=add';
        }
    }

    function fg_delete(id) {
        if (typeof id == 'number') {
            var id = new Array(id.toString());
        }
        ;
        if (confirm('删除后将不能恢复，确认删除这 ' + id.length + ' 项吗？')) {
            id = id.join(',');
        } else {
            return false;
        }
        $.ajax({
            type: "GET",
            dataType: "json",
            url: "index.php?act=goods_app&op=delete",
            data: "del_id=" + id,
            success: function (data) {
                if (data.state) {
                    $(".flex-table").flexReload();
                    window.location.reload();
                    //$("#flexigrid").flexReload();
                } else {
                    alert(data.msg);
                }
            }
        });
    }
</script>

