<?php

/**
 * 商品栏目管理
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');
class goodsControl extends SystemControl
{
    private $links = array(
        array('url' => 'act=goods&op=goods', 'text' => '所有商品'),
        array('url' => 'act=goods&op=lockup_list', 'text' => '违规下架'),
        array('url' => 'act=goods&op=waitverify_list', 'text' => '等待审核'),
        array('url' => 'act=goods&op=goods_set', 'text' => '商品设置'),
    );
    const EXPORT_SIZE = 2000;
    public function __construct()
    {
        parent::__construct();
        Language::read('goods');
    }

    public function indexOp()
    {
        $this->goodsOp();
    }
    /**
     * 商品管理
     */
    public function goodsOp()
    {
        //父类列表，只取到第二级
        $gc_list = Model('goods_class')->getGoodsClassList(array('gc_parent_id' => 0));
        Tpl::output('gc_list', $gc_list);

        Tpl::output('top_link', $this->sublink($this->links, 'goods'));
        Tpl::showpage('goods.index');
    }
    /**
     * 违规下架商品管理
     */
    public function lockup_listOp()
    {
        Tpl::output('type', 'lockup');
        Tpl::output('top_link', $this->sublink($this->links, 'lockup_list'));
        Tpl::showpage('goods.index');
    }
    /**
     * 等待审核商品管理
     */
    public function waitverify_listOp()
    {
        Tpl::output('type', 'waitverify');
        Tpl::output('top_link', $this->sublink($this->links, 'waitverify_list'));
        Tpl::showpage('goods.index');
    }

    /**
     * 输出XML数据
     */
    public function get_xmlOp()
    {
        $model_goods = Model('goods');
        $condition = array();
        if ($_GET['goods_name'] != '') {
            $condition['goods_name'] = array('like', '%' . $_GET['goods_name'] . '%');
        }
        if ($_GET['goods_commonid'] != '') {
            $condition['goods_commonid'] = array('like', '%' . $_GET['goods_commonid'] . '%');
        }
        if ($_GET['store_name'] != '') {
            $condition['store_name'] = array('like', '%' . $_GET['store_name'] . '%');
        }
        if ($_GET['brand_name'] != '') {
            $condition['brand_name'] = array('like', '%' . $_GET['brand_name'] . '%');
        }
        if (intval($_GET['cate_id']) > 0) {
            $condition['gc_id'] = intval($_GET['cate_id']);
        }
        if ($_GET['goods_state'] != '') {
            $condition['goods_state'] = $_GET['goods_state'];
        }
        if ($_GET['goods_verify'] != '') {
            $condition['goods_verify'] = $_GET['goods_verify'];
        }
        if ($_POST['query'] != '') {
            $condition[$_POST['qtype']] = array('like', '%' . $_POST['query'] . '%');
        }
        // 添加主体筛选
        $condition['store_id'] = $_SESSION['store_id'];
        $order = '';
        $param = array(
            'goods_commonid',
            'goods_name',
            'goods_price',
            'goods_state',
            'goods_verify',
            'goods_image',
            'goods_jingle',
            'gc_id',
            'gc_name',
            'store_id',
            'store_name',
            'is_own_shop',
            'brand_id',
            'brand_name',
            'goods_addtime',
            'goods_marketprice',
            'goods_costprice',
            'goods_freight',
            'is_virtual',
            'virtual_indate',
            'virtual_invalid_refund',
            'is_fcode',
            'is_presell',
            'presell_deliverdate'
        );
        if (in_array($_POST['sortname'], $param) && in_array($_POST['sortorder'], array('asc', 'desc'))) {
            $order = $_POST['sortname'] . ' ' . $_POST['sortorder'];
        }
        $page = $_POST['rp'];

        switch ($_GET['type']) {
            // 禁售
            case 'lockup':
                $goods_list = $model_goods->getGoodsCommonLockUpList($condition, '*', $page, $order);
                break;
            // 等待审核
            case 'waitverify':
                $goods_list = $model_goods->getGoodsCommonWaitVerifyList($condition, '*', $page, $order);
                break;
            // 全部商品
            default:
                $goods_list = $model_goods->getGoodsCommonList($condition, '*', $page, $order);
                break;
        }

        // 库存
        $storage_array = $model_goods->calculateStorage($goods_list);

        // 商品状态
        $goods_state = $this->getGoodsState();

        // 审核状态
        $verify_state = $this->getGoodsVerify();

        $data = array();
        $data['now_page'] = $model_goods->shownowpage();
        $data['total_num'] = $model_goods->gettotalnum();
        foreach ($goods_list as $value) {
            $param = array();
            $operation = '';
            switch ($_GET['type']) {
                // 禁售
                case 'lockup':
                    // 电商平台不能删除商品
                    //                    $operation .= "<a class='btn red' href='javascript:void(0);' onclick=\"fg_del('" . $value['goods_commonid'] . "')\"><i class='fa fa-trash-o'></i>删除</a>";
                    break;
                // 等待审核
                case 'waitverify':
                    $operation .= "<a class='btn orange' href='javascript:void(0);' onclick=\"fg_verify('" . $value['goods_commonid'] . "')\"><i class='fa fa-check-square'></i>审核</a>";
                    break;
                // 全部商品
                default:
                    $operation .= "<a class='btn red' href='javascript:void(0);' onclick=\"fg_lonkup('" . $value['goods_commonid'] . "')\"><i class='fa fa-ban'></i>下架</a>";
                    break;
            }
            $operation .= "<span class='btn'><em><i class='fa fa-cog'></i>设置 <i class='arrow'></i></em><ul>";
            $operation .= "<li><a href='" . urlShop('goods', 'index', array('goods_id' => $storage_array[$value['goods_commonid']]['goods_id'])) . "' target=\"_blank\">查看商品详细</a></li>";
            $operation .= "<li><a href='javascript:void(0)' onclick=\"fg_sku('" . $value['goods_commonid'] . "')\">查看商品SKU</a></li>";
            if ($value['is_virtual'] == 0) {
                $operation .= "<li><a href='javascript:void(0)' onclick=\"fg_goods_add('" . $value['goods_commonid'] . "')\">加入商品库</a></li>";
            }
            $operation .= "</ul>";
            $param['operation'] = $operation;
            $param['goods_commonid'] = $value['goods_commonid'];
            $param['goods_name'] = $value['goods_name'];
            $param['goods_price'] = ncPriceFormat($value['goods_price']);
            $param['goods_state'] = $goods_state[$value['goods_state']];
            $param['goods_verify'] = $verify_state[$value['goods_verify']];
            $param['goods_image'] = "<a href='javascript:void(0);' class='pic-thumb-tip' onMouseOut='toolTip()' onMouseOver='toolTip(\"<img src=" . thumb($value, '60') . ">\")'><i class='fa fa-picture-o'></i></a>";
            if (empty($value['goods_video'])) {
                $param['goods_video'] = '';
            } else {
                if (file_exists(BASE_UPLOAD_PATH . '/' . ATTACH_GOODS . '/' . $value['store_id'] . '/' . 'goods_video' . '/' . $value['goods_video'])) {
                    $param['goods_video'] = "<a href='javascript:void(0);' onclick='fg_show_video(" . $value['goods_commonid'] . ")'><i class='fa fa-file-video-o'></i></a>";
                } else {
                    $param['goods_video'] = '';
                }
            }
            $param['goods_jingle'] = $value['goods_jingle'];
            $param['gc_id'] = $value['gc_id'];
            $param['gc_name'] = $value['gc_name'];
            $param['store_id'] = $value['store_id'];
            $param['store_name'] = $value['store_name'];
            $param['is_own_shop'] = $value['is_own_shop'] == 1 ? '平台自营' : '入驻商户';
            $param['brand_id'] = $value['brand_id'];
            $param['brand_name'] = $value['brand_name'];
            $param['goods_addtime'] = date('Y-m-d', $value['goods_addtime']);
            $param['goods_marketprice'] = ncPriceFormat($value['goods_marketprice']);
            $param['goods_costprice'] = ncPriceFormat($value['goods_costprice']);
            $param['goods_freight'] = $value['goods_freight'] == 0 ? '免运费' : ncPriceFormat($value['goods_freight']);
            $param['goods_storage'] = $storage_array[$value['goods_commonid']]['sum'];
            $param['is_virtual'] = $value['is_virtual'] ==  '1' ? '<span class="yes"><i class="fa fa-check-circle"></i>是</span>' : '<span class="no"><i class="fa fa-ban"></i>否</span>';
            $param['virtual_indate'] = $value['is_virtual'] == '1' && $value['virtual_indate'] > 0 ? date('Y-m-d', $value['virtual_indate']) : '--';
            $param['virtual_invalid_refund'] = $value['is_virtual'] ==  '1' ? ($value['virtual_invalid_refund'] == 1 ? '<span class="yes"><i class="fa fa-check-circle"></i>是</span>' : '<span class="no"><i class="fa fa-ban"></i>否</span>') : '--';
            $data['list'][$value['goods_commonid']] = $param;
        }
        echo Tpl::flexigridXML($data);
        exit();
    }

    /**
     * 商品状态
     * @return multitype:string
     */
    private function getGoodsState()
    {
        return array('1' => '出售中', '0' => '仓库中', '10' => '违规下架');
    }

    private function getGoodsVerify()
    {
        return array('1' => '通过', '0' => '未通过', '10' => '等待审核');
    }

    /**
     * 违规下架
     */
    public function goods_lockupOp()
    {
        if (chksubmit()) {
            $commonid = intval($_POST['commonid']);
            if ($commonid <= 0) {
                showDialog(L('nc_common_op_fail'), 'reload');
            }
            $update = array();
            $update['goods_stateremark'] = trim($_POST['close_reason']);

            $where = array();
            $where['goods_commonid'] = $commonid;
            $where['store_id'] = $_SESSION['store_id'];
            Model('goods')->editProducesLockUp($update, $where);
            showDialog(L('nc_common_op_succ'), '', 'succ', '$("#flexigrid").flexReload();CUR_DIALOG.close()');
        }
        $common_info = Model('goods')->getGoodsCommonInfoByID($_GET['id']);
        Tpl::output('common_info', $common_info);
        Tpl::showpage('goods.close_remark', 'null_layout');
    }

    /**
     * 删除商品
     */
    public function goods_delOp()
    {
        $common_id = intval($_GET['id']);
        if ($common_id <= 0) {
            exit(json_encode(array('state' => false, 'msg' => '删除失败')));
        }
        Model('goods')->delGoodsAll(array('goods_commonid' => $common_id));
        $this->log('删除商品[ID:' . $common_id . ']', 1);
        exit(json_encode(array('state' => true, 'msg' => '删除成功')));
    }

    /**
     * 加入商品库
     *
     */
    public function goods_addOp()
    {
        $id = intval($_GET['id']);
        $common_info = Model('goods')->getGoodsCommonInfoByID($id);
        $condition = array();
        $condition['goods_name'] = $common_info['goods_name'];
        $model_goods = Model('lib_goods');
        $goods = $model_goods->getGoodsInfo($condition);
        if (empty($goods)) {
            $_array = array();
            $_array['goods_name'] = $common_info['goods_name'];
            $_array['goods_jingle'] = $common_info['goods_jingle'];
            $_array['gc_id'] = $common_info['gc_id'];
            $_array['gc_id_1'] = $common_info['gc_id_1'];
            $_array['gc_id_2'] = $common_info['gc_id_2'];
            $_array['gc_id_3'] = $common_info['gc_id_3'];
            $_array['gc_name'] = $common_info['gc_name'];
            $_array['brand_id'] = $common_info['brand_id'];
            $_array['brand_name'] = $common_info['brand_name'];
            $_array['goods_barcode'] = $common_info['goods_barcode'];
            $_array['goods_image'] = $common_info['goods_image'];
            $_array['goods_attr'] = $common_info['goods_attr'];
            $_array['goods_custom'] = $common_info['goods_custom'];
            $_array['goods_body'] = $common_info['goods_body'];
            $_array['mobile_body'] = $common_info['mobile_body'];
            $_array['goods_addtime'] = time();
            $_array['goods_edittime'] = time();
            $image_list = Model('goods')->getGoodsImageList(array('goods_commonid' => $id, 'is_default' => 0), 'goods_image', 'goods_image_id asc');
            if (!empty($image_list)) {
                $k = 2;
                $goods_img = array();
                foreach ($image_list as $v) {
                    $goods_img[$k++] = $v['goods_image'];
                    if ($k > 5) break;
                }
                $_array['goods_img'] = serialize($goods_img);
            }

            $state = $model_goods->addGoods($_array);
            if ($state) {
                exit(json_encode(array('state' => true, 'msg' => '加入成功')));
            } else {
                exit(json_encode(array('state' => false, 'msg' => '加入失败')));
            }
        } else {
            exit(json_encode(array('state' => false, 'msg' => '加入失败，商品库已经存在相同名称的商品')));
        }
    }

    /**
     * 审核商品
     */
    public function goods_verifyOp()
    {
        if (chksubmit()) {
            $commonid = intval($_POST['commonid']);
            if ($commonid <= 0) {
                showDialog(L('nc_common_op_fail'), 'reload');
            }
            $update2 = array();
            $update2['goods_verify'] = intval($_POST['verify_state']);

            $update1 = array();
            $update1['goods_verifyremark'] = trim($_POST['verify_reason']);
            $update1 = array_merge($update1, $update2);
            $where = array();
            $where['goods_commonid'] = $commonid;

            $model_goods = Model('goods');
            if (intval($_POST['verify_state']) == 0) {
                $model_goods->editProducesVerifyFail($where, $update1, $update2);
            } else {
                $model_goods->editProduces($where, $update1, $update2);
            }
            showDialog(L('nc_common_op_succ'), '', 'succ', '$("#flexigrid").flexReload();CUR_DIALOG.close();');
        }
        $common_info = Model('goods')->getGoodsCommonInfoByID($_GET['id']);
        Tpl::output('common_info', $common_info);
        Tpl::showpage('goods.verify_remark', 'null_layout');
    }

    /**
     * ajax获取商品列表
     */
    public function get_goods_sku_listOp()
    {
        $commonid = $_GET['commonid'];
        if ($commonid <= 0) {
            showDialog('参数错误', '', '', 'CUR_DIALOG.close();');
        }
        $model_goods = Model('goods');
        $goodscommon_list = $model_goods->getGoodsCommonInfoByID($commonid, 'spec_name');
        if (empty($goodscommon_list)) {
            showDialog('参数错误', '', '', 'CUR_DIALOG.close();');
        }
        $spec_name = array_values((array)unserialize($goodscommon_list['spec_name']));
        $goods_list = $model_goods->getGoodsList(array('goods_commonid' => $commonid), 'goods_id,goods_spec,store_id,goods_price,goods_serial,goods_storage,goods_image');
        if (empty($goods_list)) {
            showDialog('参数错误', '', '', 'CUR_DIALOG.close();');
        }

        foreach ($goods_list as $key => $val) {
            $goods_spec = array_values((array)unserialize($val['goods_spec']));
            $spec_array = array();
            foreach ($goods_spec as $k => $v) {
                $spec_array[] = '<div class="goods_spec">' . $spec_name[$k] . L('nc_colon') . '<em title="' . $v . '">' . $v . '</em>' . '</div>';
            }
            $goods_list[$key]['goods_image'] = thumb($val, '60');
            $goods_list[$key]['goods_spec'] = implode('', $spec_array);
            $goods_list[$key]['url'] = urlShop('goods', 'index', array('goods_id' => $val['goods_id']));
        }

        //         /**
        //          * 转码
        //          */
        //         if (strtoupper(CHARSET) == 'GBK') {
        //             Language::getUTF8($goods_list);
        //         }
        //         echo json_encode($goods_list);
        Tpl::output('goods_list', $goods_list);
        Tpl::showpage('goods.sku_list', 'null_layout');
    }

    /**
     * 商品设置
     */
    public function goods_setOp()
    {
        $model_setting = Model('setting');
        if (chksubmit()) {
            $update_array = array();
            $update_array['goods_verify'] = $_POST['goods_verify'];
            $result = $model_setting->updateSetting($update_array);
            if ($result === true) {
                $this->log(L('nc_edit,nc_goods_set'), 1);
                showMessage(L('nc_common_save_succ'));
            } else {
                $this->log(L('nc_edit,nc_goods_set'), 0);
                showMessage(L('nc_common_save_fail'));
            }
        }
        $list_setting = $model_setting->getListSetting();
        Tpl::output('list_setting', $list_setting);

        Tpl::output('top_link', $this->sublink($this->links, 'goods_set'));
        Tpl::showpage('goods.setting');
    }

    /**
     * csv导出
     */
    public function export_csvOp()
    {
        $model_goods = Model('goods');
        $condition = array();
        $limit = false;
        if ($_GET['id'] != '') {
            $id_array = explode(',', $_GET['id']);
            $condition['goods_commonid'] = array('in', $id_array);
        }
        if ($_GET['goods_name'] != '') {
            $condition['goods_name'] = array('like', '%' . $_GET['goods_name'] . '%');
        }
        if ($_GET['goods_commonid'] != '') {
            $condition['goods_commonid'] = array('like', '%' . $_GET['goods_commonid'] . '%');
        }
        if ($_GET['store_name'] != '') {
            $condition['store_name'] = array('like', '%' . $_GET['store_name'] . '%');
        }
        if ($_GET['brand_name'] != '') {
            $condition['brand_name'] = array('like', '%' . $_GET['brand_name'] . '%');
        }
        if ($_GET['cate_id'] != '') {
            $condition['gc_id'] = $_GET['cate_id'];
        }
        if ($_GET['goods_state'] != '') {
            $condition['goods_state'] = $_GET['goods_state'];
        }
        if ($_GET['goods_verify'] != '') {
            $condition['goods_verify'] = $_GET['goods_verify'];
        }
        if ($_REQUEST['query'] != '') {
            $condition[$_REQUEST['qtype']] = array('like', '%' . $_REQUEST['query'] . '%');
        }
        $order = '';
        $param = array(
            'goods_commonid',
            'goods_name',
            'goods_price',
            'goods_state',
            'goods_verify',
            'goods_image',
            'goods_jingle',
            'gc_id',
            'gc_name',
            'store_id',
            'store_name',
            'is_own_shop',
            'brand_id',
            'brand_name',
            'goods_addtime',
            'goods_marketprice',
            'goods_costprice',
            'goods_freight',
            'is_virtual',
            'virtual_indate',
            'virtual_invalid_refund',
            'is_fcode',
            'is_presell',
            'presell_deliverdate'
        );
        if (in_array($_REQUEST['sortname'], $param) && in_array($_REQUEST['sortorder'], array('asc', 'desc'))) {
            $order = $_REQUEST['sortname'] . ' ' . $_REQUEST['sortorder'];
        }
        if (!is_numeric($_GET['curpage'])) {
            switch ($_GET['type']) {
                // 禁售
                case 'lockup':
                    $count = $model_goods->getGoodsCommonLockUpCount($condition);
                    break;
                // 等待审核
                case 'waitverify':
                    $count = $model_goods->getGoodsCommonWaitVerifyCount($condition);
                    break;
                // 全部商品
                default:
                    $count = $model_goods->getGoodsCommonCount($condition);
                    break;
            }
            if ($count > self::EXPORT_SIZE) {   //显示下载链接
                $array = array();
                $page = ceil($count / self::EXPORT_SIZE);
                for ($i = 1; $i <= $page; $i++) {
                    $limit1 = ($i - 1) * self::EXPORT_SIZE + 1;
                    $limit2 = $i * self::EXPORT_SIZE > $count ? $count : $i * self::EXPORT_SIZE;
                    $array[$i] = $limit1 . ' ~ ' . $limit2;
                }
                Tpl::output('list', $array);
                Tpl::output('murl', 'index.php?act=goods&op=index');
                Tpl::showpage('export.excel');
                exit();
            }
        } else {
            $limit1 = ($_GET['curpage'] - 1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $limit = $limit1 . ',' . $limit2;
        }
        switch ($_GET['type']) {
            // 禁售
            case 'lockup':
                $goods_list = $model_goods->getGoodsCommonLockUpList($condition, '*', null, $order, $limit);
                break;
            // 等待审核
            case 'waitverify':
                $goods_list = $model_goods->getGoodsCommonWaitVerifyList($condition, '*', null, $order, $limit);
                break;
            // 全部商品
            default:
                $goods_list = $model_goods->getGoodsCommonList($condition, '*', null, $order, $limit);
                break;
        }
        $this->createCsv($goods_list);
    }

    /**
     * csv导出sku
     */
    public function exportsku_csvOp()
    {
        $model_goods = Model('goods');
        $condition = array();
        $limit = false;
        if ($_GET['id'] != '') {
            $id_array = explode(',', $_GET['id']);
            $condition['goods_commonid'] = array('in', $id_array);
        }
        if ($_GET['goods_name'] != '') {
            $condition['goods_name'] = array('like', '%' . $_GET['goods_name'] . '%');
        }
        if ($_GET['goods_commonid'] != '') {
            $condition['goods_commonid'] = array('like', '%' . $_GET['goods_commonid'] . '%');
        }
        if ($_GET['store_name'] != '') {
            $condition['store_name'] = array('like', '%' . $_GET['store_name'] . '%');
        }
        if ($_GET['brand_name'] != '') {
            $condition['brand_name'] = array('like', '%' . $_GET['brand_name'] . '%');
        }
        if ($_GET['cate_id'] != '') {
            $condition['gc_id'] = $_GET['cate_id'];
        }
        if ($_GET['goods_state'] != '') {
            $condition['goods_state'] = $_GET['goods_state'];
        }
        if ($_GET['goods_verify'] != '') {
            $condition['goods_verify'] = $_GET['goods_verify'];
        }
        if ($_GET['stime'] != '' && $_GET['etime'] != '') {
            $stime = strtotime($_GET['stime']);
            $etime = strtotime($_GET['etime']) + 86399;
            $condition['goods_addtime'] = array('between', "$stime,$etime");
        } elseif ($_GET['stime'] != '') {
            $stime = strtotime($_GET['stime']);
            $condition['goods_addtime'] = array('gt', $stime);
        } elseif ($_GET['etime'] != '') {
            $etime = strtotime($_GET['etime']) + 86399;
            $condition['goods_addtime'] = array('lt', $etime);
        }
        if ($_REQUEST['query'] != '') {
            $condition[$_REQUEST['qtype']] = array('like', '%' . $_REQUEST['query'] . '%');
        }
        $order = '';
        $param = array(
            'goods_commonid',
            'goods_name',
            'goods_price',
            'goods_state',
            'goods_verify',
            'goods_image',
            'goods_jingle',
            'gc_id',
            'gc_name',
            'store_id',
            'store_name',
            'is_own_shop',
            'brand_id',
            'brand_name',
            'goods_addtime',
            'goods_marketprice',
            'goods_costprice',
            'goods_freight',
            'is_virtual',
            'virtual_indate',
            'virtual_invalid_refund',
            'is_fcode',
            'is_presell',
            'presell_deliverdate'
        );
        if (in_array($_REQUEST['sortname'], $param) && in_array($_REQUEST['sortorder'], array('asc', 'desc'))) {
            $order = $_REQUEST['sortname'] . ' ' . $_REQUEST['sortorder'];
        }

        if (!is_numeric($_GET['curpage'])) {
            switch ($_GET['type']) {
                // 禁售
                case 'lockup':
                    $count = $model_goods->getGoodsCommonLockUpCount($condition);
                    break;
                // 等待审核
                case 'waitverify':
                    $count = $model_goods->getGoodsCommonWaitVerifyCount($condition);
                    break;
                // 全部商品
                default:
                    $count = $model_goods->getGoodsCommonCount($condition);
                    break;
            }
            if ($count > self::EXPORT_SIZE) {   //显示下载链接
                $array = array();
                $page = ceil($count / self::EXPORT_SIZE);
                for ($i = 1; $i <= $page; $i++) {
                    $limit1 = ($i - 1) * self::EXPORT_SIZE + 1;
                    $limit2 = $i * self::EXPORT_SIZE > $count ? $count : $i * self::EXPORT_SIZE;
                    $array[$i] = $limit1 . ' ~ ' . $limit2;
                }
                Tpl::output('list', $array);
                Tpl::output('murl', 'index.php?act=goods&op=index');
                Tpl::showpage('export.excel');
                exit();
            }
        } else {
            $limit1 = ($_GET['curpage'] - 1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $limit = $limit1 . ',' . $limit2;
        }
        switch ($_GET['type']) {
            // 禁售
            case 'lockup':
                $goods_list = $model_goods->getGoodsCommonSkuLockUpList($condition, '', null, $order, $limit);
                break;
            // 等待审核
            case 'waitverify':
                $goods_list = $model_goods->getGoodsCommonSkuWaitVerifyList($condition, '', null, $order, $limit);
                break;
            // 全部商品
            default:
                $goods_list = $model_goods->getGoodsCommonSkuList($condition, '', null, $order, $limit);
                break;
        }
        $this->createskuCsv($goods_list);
    }

    public function exportGoodsListOp()
    {
        $model_goods = Model('goods');
        $condition = array();
        $limit = false;
        if ($_GET['id'] != '') {
            $id_array = explode(',', $_GET['id']);
            $condition['goods_commonid'] = array('in', $id_array);
        }
        if ($_GET['goods_name'] != '') {
            $condition['goods_name'] = array('like', '%' . $_GET['goods_name'] . '%');
        }
        if ($_GET['goods_commonid'] != '') {
            $condition['goods_commonid'] = array('like', '%' . $_GET['goods_commonid'] . '%');
        }
        if ($_GET['store_name'] != '') {
            $condition['store_name'] = array('like', '%' . $_GET['store_name'] . '%');
        }
        if ($_GET['brand_name'] != '') {
            $condition['brand_name'] = array('like', '%' . $_GET['brand_name'] . '%');
        }
        if ($_GET['cate_id'] != '') {
            $condition['gc_id'] = $_GET['cate_id'];
        }
        if ($_GET['goods_state'] != '') {
            $condition['goods_state'] = $_GET['goods_state'];
        }
        if ($_GET['goods_verify'] != '') {
            $condition['goods_verify'] = $_GET['goods_verify'];
        }
        if ($_GET['stime'] != '' && $_GET['etime'] != '') {
            $stime = strtotime($_GET['stime']);
            $etime = strtotime($_GET['etime']) + 86399;
            $condition['goods_addtime'] = array('between', "$stime,$etime");
        } elseif ($_GET['stime'] != '') {
            $stime = strtotime($_GET['stime']);
            $condition['goods_addtime'] = array('gt', $stime);
        } elseif ($_GET['etime'] != '') {
            $etime = strtotime($_GET['etime']) + 86399;
            $condition['goods_addtime'] = array('lt', $etime);
        }
        if ($_REQUEST['query'] != '') {
            $condition[$_REQUEST['qtype']] = array('like', '%' . $_REQUEST['query'] . '%');
        }
        $order = '';
        $param = array(
            'goods_commonid',
            'goods_name',
            'goods_price',
            'goods_state',
            'goods_verify',
            'goods_image',
            'goods_jingle',
            'gc_id',
            'gc_name',
            'store_id',
            'store_name',
            'is_own_shop',
            'brand_id',
            'brand_name',
            'goods_addtime',
            'goods_marketprice',
            'goods_costprice',
            'goods_freight',
            'is_virtual',
            'virtual_indate',
            'virtual_invalid_refund',
            'is_fcode',
            'is_presell',
            'presell_deliverdate'
        );
        if (in_array($_REQUEST['sortname'], $param) && in_array($_REQUEST['sortorder'], array('asc', 'desc'))) {
            $order = $_REQUEST['sortname'] . ' ' . $_REQUEST['sortorder'];
        }

        if (!is_numeric($_GET['curpage'])) {

            switch ($_GET['type']) {
                // 禁售
                case 'lockup':
                    $count = $model_goods->getGoodsCommonLockUpCount($condition);
                    break;
                // 等待审核
                case 'waitverify':
                    $count = $model_goods->getGoodsCommonWaitVerifyCount($condition);
                    break;
                // 全部商品
                default:
                    $count = $model_goods->getGoodsCommonCount($condition);
                    break;
            }
            if ($count > self::EXPORT_SIZE) {   //显示下载链接
                $array = array();
                $page = ceil($count / self::EXPORT_SIZE);
                for ($i = 1; $i <= $page; $i++) {
                    $limit1 = ($i - 1) * self::EXPORT_SIZE + 1;
                    $limit2 = $i * self::EXPORT_SIZE > $count ? $count : $i * self::EXPORT_SIZE;
                    $array[$i] = $limit1 . ' ~ ' . $limit2;
                }
                Tpl::output('list', $array);
                Tpl::output('murl', 'index.php?act=goods&op=index');
                Tpl::showpage('export.excel');
                exit();
            }
        } else {
            $limit1 = ($_GET['curpage'] - 1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $limit = $limit1 . ',' . $limit2;
        }
        switch ($_GET['type']) {
            // 禁售
            case 'lockup':
                $goods_list = $model_goods->getGoodsCommonSkuLockUpList($condition, '', null, $order, $limit);
                break;
            // 等待审核
            case 'waitverify':
                $goods_list = $model_goods->getGoodsCommonSkuWaitVerifyList($condition, '', null, $order, $limit);
                break;
            // 全部商品
            default:
                $goods_list = $model_goods->getGoodsCommonSkuList($condition, '', null, $order, $limit);
                break;
        }
        $this->createGoodsListCsv($goods_list);
    }
    /**
     * 生成csvsku文件\
     */
    private function createGoodsListCsv($goods_list)
    {
        // 商品状态
        $goods_state = $this->getGoodsState();

        // 审核状态
        $verify_state = $this->getGoodsVerify();
        $data = array();

        // 一次性获取所有商品分类，并优化为索引数组
        $class = Model('goods_class')->limit(false)->select();
        $fenlei = array();
        foreach ($class as $v) {
            $fenlei[$v['gc_id']] = $v['gc_name'];
        }

        foreach ($goods_list as $value) {
            $param = array();
            $param['goods_id'] = $value['goods_id'];
            $param['goods_commonid'] = $value['goods_commonid'];
            $param['goods_serial'] = $value['goods_serial'];
            $param['goods_price'] = ncPriceFormat($value['goods_price']);
            $param['goods_marketprice'] = ncPriceFormat($value['goods_marketprice']);
            $param['goods_storage'] = $value['goods_storage'];
            $param['goods_freight'] = $value['goods_freight'] == 0 ? '免运费' : ncPriceFormat($value['goods_freight']);
            $param['goods_name'] = $value['goods_name'];
            $param['is_virtual'] = $value['is_virtual'] ==  '1' ? '是' : '否';
            $param['goods_url'] = urlShop('goods', 'index', array('goods_id' => $value['goods_id']));
            $param['goods_state'] = $goods_state[$value['goods_state']];
            $param['goods_verify'] = $verify_state[$value['goods_verify']];
            $param['goods_image'] = thumb($value, '360');
            $param['gc_id'] = $value['gc_id'];

            // 组装分类名称
            $gc_names = array();
            if (!empty($fenlei[$value['gc_id_1']])) $gc_names[] = $fenlei[$value['gc_id_1']];
            if (!empty($fenlei[$value['gc_id_2']])) $gc_names[] = $fenlei[$value['gc_id_2']];
            if (!empty($fenlei[$value['gc_id_3']])) $gc_names[] = $fenlei[$value['gc_id_3']];
            $param['gc_names'] = implode(' > ', $gc_names);

            $param['store_name'] = $value['store_name'];
            $param['is_own_shop'] = $value['is_own_shop'] == 1 ? '平台自营' : '入驻商户';
            $param['brand_name'] = $value['brand_name'];
            $param['goods_addtime'] = date('Y-m-d', $value['goods_addtime']);
            $param['virtual_indate'] = $value['is_virtual'] == '1' && $value['virtual_indate'] > 0 ? date('Y-m-d', $value['virtual_indate']) : '--';
            $param['virtual_invalid_refund'] = $value['is_virtual'] ==  '1' ? ($value['virtual_invalid_refund'] == 1 ? '是' : '否') : '--';

            $data[$value['goods_id']] = $param;
        }

        $header = array(
            'goods_id' => 'SKU',
            'goods_commonid' => 'SPU',
            'goods_serial' => '商品货号',
            'goods_price' => '商品价格(元)',
            'goods_marketprice' => '市场价格(元)',
            'goods_storage' => '库存',
            'goods_freight' => '运费(元)',
            'goods_name' => '商品名称',
            'is_virtual' => '虚拟商品',
            'goods_url' => '商品链接',
            'goods_state' => '商品状态',
            'goods_verify' => '审核状态',
            'goods_image' => '商品图片',
            /* 'goods_jingle' => '广告词', */
            'gc_id' => '分类ID',
            'gc_names' => '分类名称',

            /* 'store_id' => '店铺ID', */
            'store_name' => '店铺名称',
            'is_own_shop' => '店铺类型',
            /* 'brand_id' => '品牌ID', */
            'brand_name' => '品牌名称',
            'goods_addtime' => '发布时间',

            //'goods_costprice' => '成本价格(元)',

            'virtual_indate' => '有效期',
            'virtual_invalid_refund' => '允许退款',

        );


        \Shopnc\Lib::exporter()->output('goods_list_sku' . $_GET['curpage'] . '-' . date('Y-m-d'), $data, $header);
    }
    /**
     * 生成csvsku文件\
     */
    private function createskuCsv($goods_list)
    {
        // 商品状态
        $goods_state = $this->getGoodsState();

        // 审核状态
        $verify_state = $this->getGoodsVerify();
        $data = array();

        // 一次性获取所有商品分类，并优化为索引数组
        $class = Model('goods_class')->limit(false)->select();
        $fenlei = array();
        foreach ($class as $v) {
            $fenlei[$v['gc_id']] = $v['gc_name'];
        }

        // 一次性获取所有区域数据，优化为索引数组
        $region_list = array();
        $region_data = Model('region')->getRegionList();
        foreach ($region_data as $region) {
            $region_list[$region['region_id']] = $region['region_name'];
        }

        // 收集所有商品的chain_id，用于一次性查询
        $chain_ids = array();
        $goods_ids = array();
        foreach ($goods_list as $value) {
            if (!empty($value['chain_id'])) {
                $chain_ids[] = $value['chain_id'];
            }
            $goods_ids[] = $value['goods_id'];
        }

        // 一次性查询所有相关门店
        $chain_info = array();
        if (!empty($chain_ids)) {
            $chain_data = Model('chain')->getChainList(array('chain_id' => array('in', $chain_ids)), 'chain_id,chain_name');
            foreach ($chain_data as $chain) {
                $chain_info[$chain['chain_id']] = $chain['chain_name'];
            }
        }

        // 一次性查询所有核销数据
        $code_counts = array();
        if (!empty($goods_ids)) {
            $fields = 'vr_order_code.order_id, vr_order.goods_id';
            $on = 'vr_order_code.order_id = vr_order.order_id';
            $where = array(
                'vr_order.goods_id' => array('in', $goods_ids),
                'vr_order_code.vr_state' => 1
            );
            $order_code_list = Model()->table('vr_order_code,vr_order')->field($fields)->join('left,left')->on($on)->where($where)->limit(false)->select();

            // 统计每个商品的核销数量
            foreach ($order_code_list as $code) {
                if (!isset($code_counts[$code['goods_id']])) {
                    $code_counts[$code['goods_id']] = 0;
                }
                $code_counts[$code['goods_id']]++;
            }
        }

        // 处理每个商品数据
        foreach ($goods_list as $value) {
            $param = array();
            $param['goods_id'] = $value['goods_id'];
            $param['goods_commonid'] = $value['goods_commonid'];
            $param['goods_serial'] = $value['goods_serial'];
            $param['goods_price'] = ncPriceFormat($value['goods_price']);
            $param['goods_marketprice'] = ncPriceFormat($value['goods_marketprice']);
            $param['goods_storage'] = $value['goods_storage'];
            $param['goods_freight'] = $value['goods_freight'] == 0 ? '免运费' : ncPriceFormat($value['goods_freight']);
            $param['goods_name'] = $value['goods_name'];
            $param['is_virtual'] = $value['is_virtual'] ==  '1' ? '是' : '否';
            $param['goods_url'] = urlShop('goods', 'index', array('goods_id' => $value['goods_id']));
            $param['goods_state'] = $goods_state[$value['goods_state']];
            $param['goods_verify'] = $verify_state[$value['goods_verify']];
            $param['gc_id'] = $value['gc_id'];

            // 组装分类名称
            $gc_names = array();
            if (!empty($fenlei[$value['gc_id_1']])) $gc_names[] = $fenlei[$value['gc_id_1']];
            if (!empty($fenlei[$value['gc_id_2']])) $gc_names[] = $fenlei[$value['gc_id_2']];
            if (!empty($fenlei[$value['gc_id_3']])) $gc_names[] = $fenlei[$value['gc_id_3']];
            $param['gc_names'] = implode(' > ', $gc_names);

            $param['store_name'] = $value['store_name'];
            $param['is_own_shop'] = $value['is_own_shop'] == 1 ? '平台自营' : '入驻商户';
            $param['brand_name'] = $value['brand_name'];
            $param['goods_addtime'] = date('Y-m-d', $value['goods_addtime']);
            $param['virtual_indate'] = $value['is_virtual'] == '1' && $value['virtual_indate'] > 0 ? date('Y-m-d', $value['virtual_indate']) : '--';
            $param['virtual_invalid_refund'] = $value['is_virtual'] ==  '1' ? ($value['virtual_invalid_refund'] == 1 ? '是' : '否') : '--';

            // 直接从索引数组获取区域名称
            $param['region_name'] = !empty($value['region_id']) && isset($region_list[$value['region_id']]) ?
                $region_list[$value['region_id']] : '';

            // 直接从索引数组获取门店名称
            $param['chain_name'] = !empty($value['chain_id']) && isset($chain_info[$value['chain_id']]) ?
                $chain_info[$value['chain_id']] : '';

            // 已售数量
            $param['goods_salenum'] = $value['goods_salenum'];

            // 从预处理的数据中获取核销数量
            $param['count_order_code'] = isset($code_counts[$value['goods_id']]) ? $code_counts[$value['goods_id']] : 0;

            $data[$value['goods_id']] = $param;
        }

        $header = array(
            'goods_id' => 'SKU',
            'goods_commonid' => 'SPU',
            'goods_serial' => '商品货号',
            'goods_price' => '商品价格(元)',
            'goods_marketprice' => '市场价格(元)',
            'goods_storage' => '库存',
            'goods_freight' => '运费(元)',
            'goods_name' => '商品名称',
            'is_virtual' => '虚拟商品',
            'goods_url' => '商品链接',
            'goods_state' => '商品状态',
            'goods_verify' => '审核状态',
            /* 'goods_image' => '商品图片', */
            /* 'goods_jingle' => '广告词', */
            'gc_id' => '分类ID',
            'gc_names' => '分类名称',

            /* 'store_id' => '店铺ID', */
            'store_name' => '店铺名称',
            'is_own_shop' => '店铺类型',
            /* 'brand_id' => '品牌ID', */
            'brand_name' => '品牌名称',
            'goods_addtime' => '发布时间',

            //'goods_costprice' => '成本价格(元)',  

            'virtual_indate' => '有效期',
            'virtual_invalid_refund' => '允许退款',
            'region_name' => '归属大区',
            'chain_name' => '归属门店',
            'goods_salenum' => '已售数量',
            'count_order_code' => '已核销数量'
        );


        \Shopnc\Lib::exporter()->output('goods_list_sku' . $_GET['curpage'] . '-' . date('Y-m-d'), $data, $header);
    }

    /**
     * 生成csv文件
     */
    private function createCsv($goods_list)
    {
        // 优化：直接使用传入的商品列表计算库存，避免重复查询
        $storage_array = Model('goods')->calculateStorage($goods_list);

        // 商品状态
        $goods_state = $this->getGoodsState();

        // 审核状态
        $verify_state = $this->getGoodsVerify();
        $data = array();

        // 预处理，提高循环内处理速度
        foreach ($goods_list as $value) {
            $param = array();
            $param['goods_commonid'] = $value['goods_commonid'];
            $param['goods_name'] = $value['goods_name'];
            $param['goods_price'] = ncPriceFormat($value['goods_price']);
            $param['goods_state'] = $goods_state[$value['goods_state']];
            $param['goods_verify'] = $verify_state[$value['goods_verify']];
            $param['goods_image'] = thumb($value, '60');
            $param['goods_jingle'] = htmlspecialchars($value['goods_jingle']);
            $param['gc_id'] = $value['gc_id'];
            $param['gc_name'] = $value['gc_name'];
            $param['store_id'] = $value['store_id'];
            $param['store_name'] = $value['store_name'];
            $param['is_own_shop'] = $value['is_own_shop'] == 1 ? '平台自营' : '入驻商户';
            $param['brand_id'] = $value['brand_id'];
            $param['brand_name'] = $value['brand_name'];
            $param['goods_addtime'] = date('Y-m-d', $value['goods_addtime']);
            $param['goods_marketprice'] = ncPriceFormat($value['goods_marketprice']);
            $param['goods_costprice'] = ncPriceFormat($value['goods_costprice']);
            $param['goods_freight'] = $value['goods_freight'] == 0 ? '免运费' : ncPriceFormat($value['goods_freight']);

            // 直接从计算好的数组中获取库存
            $param['goods_storage'] = isset($storage_array[$value['goods_commonid']]['sum']) ?
                $storage_array[$value['goods_commonid']]['sum'] : 0;

            $param['is_virtual'] = $value['is_virtual'] == '1' ? '是' : '否';
            $param['virtual_indate'] = $value['is_virtual'] == '1' && $value['virtual_indate'] > 0 ?
                date('Y-m-d', $value['virtual_indate']) : '--';
            $param['virtual_invalid_refund'] = $value['is_virtual'] == '1' ?
                ($value['virtual_invalid_refund'] == 1 ? '是' : '否') : '--';

            $data[$value['goods_commonid']] = $param;
        }

        $header = array(
            'goods_commonid' => 'SPU',
            'goods_name' => '商品名称',
            'goods_price' => '商品价格(元)',
            'goods_state' => '商品状态',
            'goods_verify' => '审核状态',
            'goods_image' => '商品图片',
            'goods_jingle' => '广告词',
            'gc_id' => '分类ID',
            'gc_name' => '分类名称',
            'store_id' => '店铺ID',
            'store_name' => '店铺名称',
            'is_own_shop' => '店铺类型',
            'brand_id' => '品牌ID',
            'brand_name' => '品牌名称',
            'goods_addtime' => '发布时间',
            'goods_marketprice' => '市场价格(元)',
            'goods_costprice' => '成本价格(元)',
            'goods_freight' => '运费(元)',
            'goods_storage' => '库存',
            'is_virtual' => '虚拟商品',
            'virtual_indate' => '有效期',
            'virtual_invalid_refund' => '允许退款'
        );
        \Shopnc\Lib::exporter()->output('goods_list' . $_GET['curpage'] . '-' . date('Y-m-d'), $data, $header);
    }

    /**
     * 查看视频
     */
    public function goods_show_videoOp()
    {
        $model_goods = Model('goods');
        $common_info = $model_goods->getGoodsCommonInfoByID($_GET['id'], 'goods_video,store_id');
        Tpl::output('common_info', $common_info);
        Tpl::showpage('goods.show_video', 'null_layout');
    }
}
