<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title">
      <div class="subject">
        <h3>分销商管理</h3>
        <h5>分销会员及认证管理</h5>
      </div>
      <?php echo $output['top_link'];?>
    </div>
  </div>
  <!-- 操作说明 -->
  <div class="explanation" id="explanation">
    <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
      <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
      <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
    <ul>
        <?php echo $output['top_tips'];?>
    </ul>
  </div>
  <div id="flexigrid"></div>
</div>
<script type="text/javascript">
$(function(){
    // 高级搜索提交
    $('#ncsubmit').click(function(){
        $("#flexigrid").flexOptions({url: 'index.php?act=distri_member&op=get_service_list&'+$("#formSearch").serialize(),query:'',qtype:''}).flexReload();
    });
    // 高级搜索重置
    $('#ncreset').click(function(){
        $("#flexigrid").flexOptions({url: 'index.php?act=distri_member&op=get_service_list'}).flexReload();
        $("#formSearch")[0].reset();
    });
    $("#flexigrid").flexigrid({
        url: 'index.php?act=distri_member&op=get_service_list',
        colModel : [
            {display: '操作', name : 'operation', width : 80, sortable : false, align: 'center'},
            {display: '会员ID', name : 'member_id', width : 100, sortable : true, align: 'center'},
            {display: '手机号', name : 'member_mobile', width : 100, sortable : true, align: 'center'},
            {display: '会员名称', name : 'member_name', width : 150, sortable : true, align: 'center'},
            {display: '门店', name : 'chain_name', width : 150, sortable : true, align: 'center'},
            {display: '品牌', name : 'brand_name', width : 120, sortable : true, align: 'center'},
            {display: '财务编码', name : 'account_id', width : 100, sortable : true, align: 'left'},
            {display: '状态', name : 'distri_state', width : 100, sortable : true, align: 'center'},
        ],

        buttons : [
            {display: '<i class="fa fa-file-excel-o"></i>导出数据', name : 'csv', bclass : 'csv', title : '将选定行数据导出CSV文件', onpress : fg_operation }	,
        ],

        searchitems : [
            {display: '会员ID', name : 'member_id'},
            {display: '会员名称', name : 'member_name'},
            {display: '手机号', name : 'member_mobile'},
            {display: '门店名称', name : 'chain_name'},
            {display: '财务编码', name : 'account_id'},
            {display: '身份证', name : 'id_card'},
        ],

        sortname: "distri_time",
        sortorder: "desc",
        title: '认证分销商列表'
    });
});

function fg_operation(name, bDiv) {
    if ($('.trSelected', bDiv).length == 0) {
        if (!confirm('您确定要下载全部数据吗？')) {
            return false;
        }
    }
    var itemids = new Array();
    $('.trSelected', bDiv).each(function(i){
        itemids[i] = $(this).attr('data-id');
    });
    fg_csv(itemids);
}

function fg_csv(ids) {
    id = ids.join(',');
    window.location.href = $("#flexigrid").flexSimpleSearchQueryString()+'&op=get_service_list&export_id=1&id=' + id;
}

</script> 

