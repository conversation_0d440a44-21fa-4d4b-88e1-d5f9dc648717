<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3><?php echo $output['top_title'];?></h3>
                <h5><?php echo $output['top_desc'];?></h5>
            </div>
            <ul class="tab-base nc-row">
                <?php echo $output['top_tab'];?>
            </ul>
        </div>
    </div>
    <form id="add_form" method="post" enctype="multipart/form-data" action="index.php?act=order_delay_delivery&op=transferVipCard">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="order_sn">会员卡订单号：</label>
                </dt>
                <dd class="opt">
                    <input name="order_sn" id="order_sn" value="" type="text" class="input-txt" autocomplete="off" placeholder="请输入订单号，多个订单用英文逗号分隔">
                    <p class="notic"></p>
                    <span class="err"></span>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="old_mobile">原手要号：</label>
                </dt>
                <dd class="opt">
                    <input name="old_mobile" id="old_mobile" value="" type="text" class="input-txt" autocomplete="off" placeholder="请输入订单号，多个订单用英文逗号分隔">
                    <p class="notic">当前会员卡所绑定的手机号。</p>
                    <span class="err"></span>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="new_mobile">新手机号：</label>
                </dt>
                <dd class="opt">
                    <input name="new_mobile" id="new_mobile" value="" type="text" class="input-txt" autocomplete="off" placeholder="请输入订单号，多个订单用英文逗号分隔">
                    <p class="notic">会员卡要转移目标的用户手机号。</p>
                    <span class="err"></span>
                </dd>
            </dl>
            <div class="bot"><a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="btn_add"><?php echo $lang['nc_submit'];?></a></div>
        </div>
    </form>
</div>
<script type="text/javascript">
    //
    $(document).ready(function(){
        //添加按钮的单击事件
        $("#btn_add").click(function(){
            $("#add_form").submit();
        });
        //页面输入内容验证
        $("#add_form").validate({
            errorPlacement: function(error, element){
                var error_td = element.parent('dd').children('span.err');
                error_td.append(error);
            },
            rules : {
                order_sn: {
                    required : true,
                },
                old_mobile: {
                    required : true,
                },
                new_mobile: {
                    required : true,
                }
            },
            messages : {
                order_sn: {
                    required : '<i class="fa fa-exclamation-circle"></i>订单号必填',
                },
                old_mobile: {
                    required : '<i class="fa fa-exclamation-circle"></i>原手机号必填',
                },
                new_mobile: {
                    required : '<i class="fa fa-exclamation-circle"></i>新手机号必填',
                }
            }
        });
    });
</script>
