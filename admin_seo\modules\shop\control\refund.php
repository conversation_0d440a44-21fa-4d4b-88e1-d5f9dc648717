<?php
/**
 * 退款管理
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Box\Spout\Common\Type;
use Box\Spout\Writer\Style\StyleBuilder;
use Box\Spout\Writer\WriterFactory;
use Shopnc\Tpl;
use Upet\Integrates\Redis\RedisManager as Redis;

defined('InShopNC') or exit('Access Invalid!');
class refundControl extends SystemControl{
    const EXPORT_SIZE = 1000;
    private $links = array(
            array('url'=>'act=refund','text'=>'待处理'),
            array('url'=>'act=refund&op=refund_all','text'=>'所有记录'),
            array('url'=>'act=refund&op=reason','text'=>'退款退货原因')
    );
    public function __construct(){
        parent::__construct();
        $model_refund = Model('refund_return');
        $model_refund->getRefundStateArray();
        Tpl::output('top_link',$this->sublink($this->links,$_GET['op']));
    }

    public function indexOp() {
        Tpl::showpage('refund_manage.list');
    }

    /**
     * 待处理列表
     */
    public function get_manage_xmlOp() {
        $model_refund = Model('refund_return');
        $condition = array();
        //状态:1为处理中,2为待管理员处理,3为已完成
        $condition['refund_state'] = 2;

        list($condition,$order) = $this->_get_condition($condition);

        if ($_GET['order_from'] > 0 ){
            $refund_list = $model_refund->getRefundJoinOrdersList($condition,$_POST['rp'],$order);
        }else{
            $refund_list = $model_refund->getRefundList($condition,$_POST['rp'],$order);
        }

        $data = array();
        $data['now_page'] = $model_refund->shownowpage();
        $data['total_num'] = $model_refund->gettotalnum();
        foreach ($refund_list as $k => $refund_info) {
            $list = array();
            $list['operation'] = "<a class=\"btn orange\" href=\"index.php?act=refund&op=edit&refund_id={$refund_info['refund_id']}\"><i class=\"fa fa-gavel\"></i>处理</a>";
            $list['refund_sn'] = $refund_info['refund_sn'];
            $list['refund_amount'] = ncPriceFormat($refund_info['refund_amount']);
            if(!empty($refund_info['pic_info'])) {
                $info = unserialize($refund_info['pic_info']);
                if (is_array($info) && !empty($info['buyer'])) {
                    foreach($info['buyer'] as $pic_name) {
                        if(!empty($pic_name)) {
                            $list['pic_info'] .= "<a href='".refund_image($pic_name).
                            "' target='_blank' class='pic-thumb-tip' onMouseOut='toolTip()' onMouseOver='toolTip(\"<img src=".
                            refund_image($pic_name).">\")'><i class='fa fa-picture-o'></i></a> ";
                        }
                    }
                    $list['pic_info'] = trim($list['pic_info']);
                }
            }
            if (empty($list['pic_info'])) $list['pic_info'] = '';
            $list['buyer_message'] = "<span title='{$refund_info['buyer_message']}'>{$refund_info['buyer_message']}</span>";
            $list['add_times'] = date('Y-m-d H:i:s',$refund_info['add_time']);
            $list['goods_name'] = $refund_info['goods_name'];
            if ($refund_info['goods_id'] > 0) {
                $list['goods_name'] = "<a class='open' title='{$refund_info['goods_name']}' href='". urlShop('goods', 'index', array('goods_id' => $refund_info['goods_id'])) .
                "' target='blank'>{$refund_info['goods_name']}</a>";
            }
            $list['seller_message'] = $refund_info['seller_message'];
            $list['seller_times'] = !empty($refund_info['seller_time']) ? date('Y-m-d H:i:s',$refund_info['seller_time']) : '';
            if ($refund_info['goods_image'] != '') {
                $list['goods_image'] = "<a href='".thumb($refund_info,360)."' target='_blank' class='pic-thumb-tip' onMouseOut='toolTip()' onMouseOver='toolTip(\"<img src=".thumb($refund_info,240).">\")'><i class='fa fa-picture-o'></i></a> ";
            } else {
                $list['goods_image'] = '';
            }
            $list['goods_id'] = !empty($refund_info['goods_id']) ? $refund_info['goods_id'] : '';
            $list['order_sn'] = $refund_info['order_sn'];
            $list['buyer_name'] = $refund_info['buyer_name'];
            $list['buyer_id'] = $refund_info['buyer_id'];
            $list['store_name'] = $refund_info['store_name'];
            $list['store_id'] = $refund_info['store_id'];
            $data['list'][$refund_info['refund_id']] = $list;
        }
        exit(Tpl::flexigridXML($data));
    }

    /**
     * 所有记录
     */
    public function refund_allOp() {
        Tpl::showpage('refund_all.list');
    }

    /**
     * 所有记录
     */
    public function get_all_xmlOp() {
        $model_refund = Model('refund_return');
        $condition = array();

        list($condition,$order) = $this->_get_condition($condition);

        if ($_GET['order_from'] > 0){
            $refund_list = $model_refund->getRefundJoinOrdersList($condition,!empty($_POST['rp']) ? intval($_POST['rp']) : 15,$order);
        }else{
            $refund_list = $model_refund->getRefundList($condition,!empty($_POST['rp']) ? intval($_POST['rp']) : 15,$order);
        }

        $data = array();
        $data['now_page'] = $model_refund->shownowpage();
        $data['total_num'] = $model_refund->gettotalnum();
        foreach ($refund_list as $k => $refund_info) {
            $list = array();
            if ($refund_info['refund_state'] == 2) {
                $list['operation'] = "<a class=\"btn orange\" href=\"index.php?act=refund&op=edit&refund_id={$refund_info['refund_id']}\"><i class=\"fa fa-gavel\"></i>处理</a>";
            }
            $list['operation'] .= "<a class=\"btn green\" href=\"index.php?act=refund&op=view&refund_id={$refund_info['refund_id']}\"><i class=\"fa fa-list-alt\"></i>查看</a>";
            $list['refund_sn'] = $refund_info['refund_sn'];
            $list['refund_amount'] = ncPriceFormat($refund_info['refund_amount']);
            if(!empty($refund_info['pic_info'])) {
                $info = unserialize($refund_info['pic_info']);
                if (is_array($info) && !empty($info['buyer'])) {
                    foreach($info['buyer'] as $pic_name) {
                        if(!empty($pic_name)) {
                            $list['pic_info'] .= "<a href='".refund_image($pic_name).
                            "' target='_blank' class='pic-thumb-tip' onMouseOut='toolTip()' onMouseOver='toolTip(\"<img src=".
                            refund_image($pic_name).">\")'><i class='fa fa-picture-o'></i></a> ";
                        }
                    }
                    $list['pic_info'] = trim($list['pic_info']);
                }
            }
            if (empty($list['pic_info'])) $list['pic_info'] = '';
            $list['buyer_message'] = "<span title='{$refund_info['buyer_message']}'>{$refund_info['buyer_message']}</span>";
            $list['add_times'] = date('Y-m-d H:i:s',$refund_info['add_time']);
            $list['goods_name'] = $refund_info['goods_name'];
            if ($refund_info['goods_id'] > 0) {
                $list['goods_name'] = "<a class='open' title='{$refund_info['goods_name']}' href='". urlShop('goods', 'index', array('goods_id' => $refund_info['goods_id'])) .
                "' target='blank'>{$refund_info['goods_name']}</a>";
            }
            $state_array = $model_refund->getRefundStateArray('seller');
            $list['seller_state'] = $refund_info['refund_state'] == 4 ? '已取消' : $state_array[$refund_info['seller_state']];

            $admin_array = $model_refund->getRefundStateArray('admin');
            $list['refund_state'] = $refund_info['seller_state'] == 2 ? $admin_array[$refund_info['refund_state']]:'';

            $list['seller_message'] = "<span title='{$refund_info['seller_message']}'>{$refund_info['seller_message']}</i>";
            $list['admin_message'] = "<span title='{$refund_info['admin_message']}'>{$refund_info['admin_message']}</span>";
            $list['seller_times'] = !empty($refund_info['seller_time']) ? date('Y-m-d H:i:s',$refund_info['seller_time']) : '';
            if ($refund_info['goods_image'] != '') {
                $list['goods_image'] = "<a href='".thumb($refund_info,360)."' target='_blank' class='pic-thumb-tip' onMouseOut='toolTip()' onMouseOver='toolTip(\"<img src=".thumb($refund_info,240).">\")'><i class='fa fa-picture-o'></i></a> ";
            } else {
                $list['goods_image'] = '';
            }
            $list['goods_id'] = !empty($refund_info['goods_id']) ? $refund_info['goods_id'] : '';
            $list['order_sn'] = $refund_info['order_sn'];
            $list['buyer_name'] = $refund_info['buyer_name'];
            $list['buyer_id'] = $refund_info['buyer_id'];
            $list['store_name'] = $refund_info['store_name'];
            $list['store_id'] = $refund_info['store_id'];
            $data['list'][$refund_info['refund_id']] = $list;
        }
        exit(Tpl::flexigridXML($data));
    }

    /**
     * 退款处理页
     */
    public function editOp() {
        $model_refund = Model('refund_return');
        $condition = array();
        $condition['refund_id'] = intval($_GET['refund_id']);
        $refund = $model_refund->getRefundReturnInfo($condition);
        $memberInfo = Model('member')->getMemberInfo(array('member_id' => $refund['buyer_id']),'member_mobile');
        $member_mobile = $memberInfo['member_mobile'];
        $order_model = Model('order');
        $order_info = $order_model->getOrderInfo(array('order_id' => $refund['order_id']),[],"payment_time");
        $payment_time=1546963200; //1月8号之前无需积分
        $erpoints = Logic('erp_points')->getUserPoints($member_mobile);
        $refund['erpoints'] = $erpoints;
        $refund['needpoints'] = $order_info['payment_time'] > $payment_time?floor($refund['refund_amount']):0;
        $order_id = $refund['order_id'];
        $model_order = Model('order');
        $order = $model_order->getOrderInfo(array('order_id'=> $order_id),array());
        if ($order['payment_time'] > 0) {
            $order['pay_amount'] = $order['order_amount']-$order['rcb_amount']-$order['pd_amount'];//在线支付金额=订单总价格-充值卡支付金额-预存款支付金额
        }
        Tpl::output('order',$order);
        $detail_array = $model_refund->getDetailInfo($condition);
        if(empty($detail_array)) {
            $model_refund->addDetail($refund,$order);
            $detail_array = $model_refund->getDetailInfo($condition,"*",true);
        }
        Tpl::output('detail_array',$detail_array);

        if (chksubmit()) {
            $lock = Redis::lock('edit_shop_refund:'.$refund['refund_id'])->setAutoRelease();
            if (!$lock->get()) {
                showMessage('处理中，请勿频繁操作...');
            }
            if ($refund['refund_state'] != '2') {//检查状态,防止页面刷新不及时造成数据错误
                showMessage(Language::get('nc_common_save_fail'));
            }
            if ($detail_array['pay_time'] > 0) {
                $refund['pay_amount'] = $detail_array['pay_amount'];//已完成在线退款金额
            } else {
                if ($_POST['admin_state'] == '3') {
                    $refund_array = array();
                    $refund_array['seller_state'] = '3';//商家处理状态:1为待审核,2为同意,3为不同意
                    $refund_array['seller_message'] = $refund['seller_message'].'(商家同意,但管理员不同意退款)';
                    $refund_array['admin_time'] = time();
                    $refund_array['refund_state'] = '3';//状态:1为处理中,2为待管理员处理,3为已完成
                    $refund_array['admin_message'] = $_POST['admin_message'];
                    //非周期购主订单退款才走数据中心
                    if ($order['order_type'] == 9 && $order['is_head'] == 1) {
                        //TODO 2
                        $push_param = array(
                            'pushType'=>2,
                            'refundSn'=>$refund['refund_order_sn'] ? : $refund['refund_sn'],
                            'refundId'=>$refund['refund_id'],
                            'refundType'=>$refund['refund_type'],
                            'status'=>'审核不通过',
                            'remarks'=>$_POST['admin_message']
                        );
                        Model('cycle_push_info')->pushSubscribeMsg($order['order_sn'],$push_param);
                    }else{
                        //平台订单驳回售后申请
                        $refund['reason_info'] = $_POST['admin_message'];
                        //视频号
                        $refund['return_type'] = 4;
                        $refund['is_live'] = $order['is_live'];
                        $erp_result = Logic('refund')->refundOrder($refund, $order);
                        if (!$erp_result) {
                            showMessage("驳回申请失败，您稍后再申请~~");
                        }
                    }
                    $model_refund->editRefundReturn($condition, $refund_array);
                    if ($refund['order_lock'] == '2') $model_refund->editOrderUnlock($order_id);//订单解锁

                    //更新周期购订单状态
                    if ($order['order_type'] == 9) {
                        Model('cycle_push_info')->refundRejectMainOrder($order);
                        Model('cycle_push_info')->pushUnpushedOrder($order['order_sn']);
                    }
                    // 发送买家消息
                    $param = array();
                    $param['code'] = 'refund_return_notice';
                    $param['member_id'] = $refund['buyer_id'];
                    $param['param'] = array(
                        'refund_url' => urlShop('member_refund', 'view', array('refund_id' => $refund['refund_id'])),
                        'refund_sn' => $refund['refund_sn']
                    );
                    $refund['msg'] = '管理员不同意退款';//状态描述
                    $param['param']['mp_array'] = Logic('wx_api')->getTemplateData($param['code'],$refund);
                    RealTimePush('sendMemberMsg', $param);
                    $this->log('退款处理，退款编号'.$refund['refund_sn']);

                    showMessage(Language::get('nc_common_save_succ'),'index.php?act=refund&op=index');
                }
            }
            $refund['admin_state_balance'] = $_POST['admin_state_balance'];

            $state = $model_refund->editOrderRefund($refund,$this->admin_info['name']);
            if ($state) {
                $refund_array = array();
                $refund_array['admin_time'] = time();
                $refund_array['refund_state'] = '3';//状态:1为处理中,2为待管理员处理,3为已完成
                $refund_array['admin_message'] = $_POST['admin_message'];
                //非周期购主订单退款才走数据中心
                if ($order['order_type'] == 9 && $order['is_head'] == 1) {
                    $push_param = array(
                        'pushType'=>1,
                        'refundId'=>$refund['refund_id'],
                        'refundType'=>$refund['refund_type'],
                        'refundAmount'=>$refund['refund_amount'],
                        'refundTime'=>date('Y-m-d H:i:s', $refund['add_time']),
                    );
                    Model('cycle_push_info')->pushSubscribeMsg($order['order_sn'],$push_param);
                }else{
                    //平台订单售后申请同意
                    $refund['mobile'] = $order['buyer_phone'];
                    $refund['reason_info'] = $_POST['admin_message'];
                    // 视频号 13:退款完成
                    $refund['return_type'] = 13;
                    $refund['is_live'] = $order['is_live'];
                    $erp_result = Logic('refund')->refundOrder($refund, $order);
                    if (!$erp_result) {
                        showMessage("驳回申请失败，您稍后再申请~~");
                    }
                }
                $model_refund->editRefundReturn($condition, $refund_array);

                //退款ERP订单
                $vr_logic=Logic('erp_realorder');
                $vr_logic->syncOrderRefund($refund);

                $this->log('退款确认，退款编号'.$refund['refund_sn']);

                $detail = array();
                $detail['pay_time'] = time();
                $detail['refund_state'] = 2;
                $model_refund->editDetail(array('refund_id'=> $detail_array['refund_id']), $detail);

                showMessage(Language::get('nc_common_save_succ'),'index.php?act=refund&op=index');
            } else {
                showMessage(Language::get('nc_common_save_fail'));
            }
        }

        Tpl::output('refund',$refund);

        if ($order['order_type'] == 11){
            $model_refund->preOrderRefund($order);
        }
        $info['buyer'] = array();
        if(!empty($refund['pic_info'])) {
            $info = unserialize($refund['pic_info']);
        }
        Tpl::output('pic_list',$info['buyer']);
        Tpl::showpage('refund.edit');
    }

    /**
     * 退款记录查看页
     *
     */
    public function viewOp() {
        $model_refund = Model('refund_return');
        $condition = array();
        $condition['refund_id'] = intval($_GET['refund_id']);
        $refund = $model_refund->getRefundReturnInfo($condition);
        Tpl::output('refund',$refund);
        $info['buyer'] = array();
        if(!empty($refund['pic_info'])) {
            $info = unserialize($refund['pic_info']);
        }
        $order_model = Model('order');
        $order = $order_model->getOrderInfo(array('order_id'=> $refund['order_id']),array());
        if ($order['order_type'] == 11){
            $model_refund->preOrderRefund($order);
        }
        Tpl::output('pic_list',$info['buyer']);
        $detail_array = $model_refund->getDetailInfo($condition);
        Tpl::output('detail_array',$detail_array);
        Tpl::showpage('refund.view');
    }

    /**
     * 退款退货原因
     */
    public function reasonOp() {
        $model_refund = Model('refund_return');
        $condition = array();

        $reason_list = $model_refund->getReasonList($condition,200);
        Tpl::output('reason_list',$reason_list);

        Tpl::showpage('refund_reason.list');
    }

    /**
     * 新增退款退货原因
     *
     */
    public function add_reasonOp() {
        $model_refund = Model('refund_return');
        if (chksubmit()) {
            $reason_array = array();
            $reason_array['reason_info'] = $_POST['reason_info'];
            $reason_array['sort'] = intval($_POST['sort']);
            $reason_array['update_time'] = time();

            $state = $model_refund->addReason($reason_array);
            if ($state) {
                $this->log('新增退款退货原因，编号'.$state);
                showMessage(Language::get('nc_common_save_succ'),'index.php?act=refund&op=reason');
            } else {
                showMessage(Language::get('nc_common_save_fail'));
            }
        }
        Tpl::showpage('refund_reason.add');
    }

    /**
     * 编辑退款退货原因
     *
     */
    public function edit_reasonOp() {
        $model_refund = Model('refund_return');
        $condition = array();
        $condition['reason_id'] = intval($_GET['reason_id']);
        $reason_list = $model_refund->getReasonList($condition);
        $reason = $reason_list[$condition['reason_id']];
        if (chksubmit()) {
            $reason_array = array();
            $reason_array['reason_info'] = $_POST['reason_info'];
            $reason_array['sort'] = intval($_POST['sort']);
            $reason_array['update_time'] = time();
            $state = $model_refund->editReason($condition, $reason_array);
            if ($state) {
                $this->log('编辑退款退货原因，编号'.$condition['reason_id']);
                showMessage(Language::get('nc_common_save_succ'),'index.php?act=refund&op=reason');
            } else {
                showMessage(Language::get('nc_common_save_fail'));
            }
        }
        Tpl::output('reason',$reason);
        Tpl::showpage('refund_reason.edit');
    }

    /**
     * 删除退款退货原因
     *
     */
    public function del_reasonOp() {
        $model_refund = Model('refund_return');
        $condition = array();
        $condition['reason_id'] = intval($_GET['reason_id']);
        $state = $model_refund->delReason($condition);
        if ($state) {
            $this->log('删除退款退货原因，编号'.$condition['reason_id']);
            showMessage(Language::get('nc_common_del_succ'),'index.php?act=refund&op=reason');
        } else {
            showMessage(Language::get('nc_common_del_fail'));
        }
    }

    /**
     * 封装共有查询代码
     */
    private function _get_condition($condition) {
        if ($_REQUEST['query'] != '' && in_array($_REQUEST['qtype'],array('order_sn','store_id','store_name','buyer_name','goods_name','refund_sn'))) {
            $condition[$_REQUEST['qtype']] = array('like',"%{$_REQUEST['query']}%");
        }
        if ($_GET['keyword'] != '' && in_array($_GET['keyword_type'],array('order_sn','store_name','buyer_name','goods_name','refund_sn'))) {
            if ($_GET['jq_query']) {
                $condition[$_GET['keyword_type']] = $_GET['keyword'];
            } else {
                $condition[$_GET['keyword_type']] = array('like',"%{$_GET['keyword']}%");
            }
        }
        if (!in_array($_GET['qtype_time'],array('add_time','seller_time','admin_time'))) {
            $_GET['qtype_time'] = null;
        }
        $if_start_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_start_date']);
        $if_end_time = preg_match('/^20\d{2}-\d{2}-\d{2}$/',$_GET['query_end_date']);
        $start_unixtime = $if_start_time ? strtotime($_GET['query_start_date']) : null;
        $end_unixtime = $if_end_time ? strtotime($_GET['query_end_date']): null;
        if ($_GET['qtype_time'] && ($start_unixtime || $end_unixtime)) {
            $condition[$_GET['qtype_time']] = array('time',array($start_unixtime,$end_unixtime));
        }
        if (floatval($_GET['query_start_amount']) > 0 && floatval($_GET['query_end_amount']) > 0) {
            $condition['refund_amount'] = array('between',floatval($_GET['query_start_amount']).','.floatval($_GET['query_end_amount']));
        }
        if ($_GET['refund_state'] == 2) {
            $condition['refund_state'] = 2;
        }
        $sort_fields = array('buyer_name','store_name','goods_id','refund_id','seller_time','refund_amount','buyer_id','store_id');
        if ($_REQUEST['sortorder'] != '' && in_array($_REQUEST['sortname'],$sort_fields)) {
            $order = $_REQUEST['sortname'].' '.$_REQUEST['sortorder'];
        }
        // 销售渠道
        if ($_GET['order_from'] > 0) {
            foreach ($condition as $k => $v) {
                unset($condition[$k]);
                $condition['refund_return.' . $k] = $v;
            }
            if ($_GET['order_from'] == 99) { // 视频号
                $condition['orders.is_live&orders.payment_code'] = [2, 'wx_jsapi', '_multi' => false];
            } else {
                $condition['orders.order_from'] = $_GET['order_from'];
            }
            $condition['refund_return.refund_type'] = '1';//类型:1为退款,2为退货
            $order = 'refund_return.'.$order;
        }
        $condition['store_id'] = $_SESSION['store_id'];
        return array($condition,$order);
    }

    /**
     * csv导出
     */
    public function export_step1Op() {
        $model_refund = Model('refund_return');
        $condition = array();
        if (preg_match('/^[\d,]+$/', $_GET['refund_id'])) {
            $_GET['refund_id'] = explode(',',trim($_GET['refund_id'],','));
            $condition['refund_id'] = array('in',$_GET['refund_id']);
        }
        list($condition,$order) = $this->_get_condition($condition);
        if (!is_numeric($_GET['curpage'])){
            $count = $model_refund->getRefundCount($condition);
            if ($count > self::EXPORT_SIZE ){   //显示下载链接
                $array = array();
                $page = ceil($count/self::EXPORT_SIZE);
                for ($i=1;$i<=$page;$i++){
                    $limit1 = ($i-1)*self::EXPORT_SIZE + 1;
                    $limit2 = $i*self::EXPORT_SIZE > $count ? $count : $i*self::EXPORT_SIZE;
                    $array[$i] = $limit1.' ~ '.$limit2 ;
                }
                Tpl::output('list',$array);
                Tpl::output('murl','javascript:history.back(-1)');
                Tpl::showpage('export.excel');
                exit();
            }
            $limit = false;
        } else {
            $limit1 = ($_GET['curpage']-1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $limit = $limit1 .','. $limit2;
        }
        if ($_GET['order_from'] > 0) {
            $refund_list = $model_refund->getRefundJoinOrdersList($condition, '', $order,$limit);
        }else{
            $refund_list = $model_refund->getRefundList($condition,'',$order,$limit);
        }
        $this->createXls($refund_list);
    }

    /**
     * Notes:退款管理导出
     * desc:导出相关样式说明：http://www.edbiji.com/doccenter/showdoc/209/nav/3755.html
     * desc:setShouldWrapText false=不自动换行
     * @param $refund_list
     * User: rocky
     * DateTime: 2022/10/25 14:30
     */
    public function createXls($refund_list){
        $writer = WriterFactory::create(Type::XLSX);
        //不自动换行
        $writer->setDefaultRowStyle((new StyleBuilder())->setShouldWrapText(false)->build());
        $writer->openToBrowser(urlencode('refund' .$_GET['curpage'] . '-'.date('Y-m-d').'.xlsx'));
        $writer->addRow(['退单编号','退款金额','申请图片','申请原因','申请时间','涉及商品','商家处理','平台处理','商家处理备注','平台处理备注','商家申核时间','商品图','商品ID','订单编号','买家','买家ID','商家名称','商家ID']);
        $model_refund = Model('refund_return');
        $state_array = $model_refund->getRefundStateArray('seller');
        $admin_array = $model_refund->getRefundStateArray('admin');
        foreach ($refund_list as $k => $refund_info) {
            $list['refund_sn'] = $refund_info['refund_sn'];
            $list['refund_amount'] = ncPriceFormat($refund_info['refund_amount']);
            if(!empty($refund_info['pic_info'])) {
                $info = unserialize($refund_info['pic_info']);
                if (is_array($info) && !empty($info['buyer'])) {
                    foreach($info['buyer'] as $pic_name) {
                        $list['pic_info'] .= refund_image($pic_name).'|';
                    }
                    $list['pic_info'] = trim($list['pic_info'],'|');
                }
            }
            if (empty($list['pic_info'])) $list['pic_info'] = '';
            $list['buyer_message'] = $refund_info['buyer_message'];
            $list['add_times'] = date('Y-m-d H:i:s',$refund_info['add_time']);
            $list['goods_name'] = $refund_info['goods_name'];
            $list['seller_state'] = $state_array[$refund_info['seller_state']];
            $list['refund_state'] = $refund_info['seller_state'] == 2 ? $admin_array[$refund_info['refund_state']]:'';
            $list['seller_message'] = $refund_info['seller_message'];
            $list['admin_message'] = $refund_info['admin_message'];
            $list['seller_times'] = !empty($refund_info['seller_time']) ? date('Y-m-d H:i:s',$refund_info['seller_time']) : '';
            if ($refund_info['goods_image'] != '') {
                $list['goods_image'] = thumb($refund_info,360);
            } else {
                $list['goods_image'] = '';
            }
            $list['goods_id'] = !empty($refund_info['goods_id']) ? $refund_info['goods_id'] : '';
            $list['order_sn'] = $refund_info['order_sn'];
            $list['buyer_name'] = $refund_info['buyer_name'];
            $list['buyer_id'] = $refund_info['buyer_id'];
            $list['store_name'] = $refund_info['store_name'];
            $list['store_id'] = $refund_info['store_id'];

            $writer->addRow($list);
        }

        $writer->close();
    }
}
