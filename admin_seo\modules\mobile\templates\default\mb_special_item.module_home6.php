<?php defined('InShopNC') or exit('Access Invalid!');?>
<style type="text/css">
.mb-item-edit-content {
background: #EFFAFE url(<?php echo ADMIN_TEMPLATES_URL;
?>/images/cms_edit_bg_line.png) repeat-y scroll 0 0;
}
</style>
<?php if($item_edit_flag) { ?>
<!-- 操作说明 -->
<div class="explanation" id="explanation">
  <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
    <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
    <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
  <ul>
    <li>从右侧筛选按钮，点击添加按钮完成添加</li>
    <li>鼠标移动到已有商品上，会出现删除按钮可以对商品进行删除</li>
    <li>操作完成后点击保存编辑按钮进行保存</li>
  </ul>
</div>
<?php } ?>
<div class="index_block goods-list">
  <?php if($item_edit_flag) { ?>
  <h3>商品版块</h3>
  <?php } ?>
  <div class="title">
    <?php if($item_edit_flag) { ?>
    <h5>标题：</h5>
    <input id="home1_title" type="text" class="txt w200" name="item_data[title]" value="<?php echo $item_data['title'];?>">
    <?php } else { ?>
    <span><?php echo $item_data['title'];?></span>
    <?php } ?>
      <input id="sort" type="hidden" value="<?php echo $sort?>">
  </div>
  <div nctype="item_content" class="content">
    <?php if($item_edit_flag) { ?>
    <h5>内容：</h5>
    <?php } ?>
    <?php if(!empty($item_data['item']) && is_array($item_data['item'])) {?>
    <?php foreach($item_data['item'] as $item_value) {?>
    <div nctype="item_image" class="item">
        <div class="goods-id" nctype="goods_id">商品id:<?php echo $item_value['goods_id'];?></div>

        <div class="goods-pic"><img nctype="goods_image" src="<?php echo cthumb($item_value['goods_image']);?>" alt=""></div>
      <div class="goods-name" nctype="goods_name"><?php echo $item_value['goods_name'];?></div>
      <div class="goods-price" nctype="goods_price">￥<?php echo $item_value['goods_promotion_price'];?></div>
      <?php if($item_edit_flag) { ?>
      <input nctype="goods_id" name="item_data[item][<?php echo $item_value[goods_id];?>][goods_id]" type="hidden" value="<?php echo $item_value['goods_id'];?>">
          <input nctype="goods_sort" name="item_data[item][<?php echo $item_value[goods_id];?>][sort]" type="hidden" value="<?php echo $item_value['sort'];?>">
          <input nctype="image" name="item_data[sort]" type="hidden"  value="2">
          <a nctype="btn_edit_item_goods" data-desc="320*130" href="javascript:;" style="right: 60px;"><i class="fa fa-pencil-square-o"></i>编辑</a>
          <a nctype="btn_del_item_image" href="javascript:;"><i class="fa fa-trash-"></i>删除</a>
      <?php } ?>
    </div>
    <?php } ?>
    <?php } ?>
  </div>

</div>
<?php if($item_edit_flag) { ?>
<div class="search-goods">
    <div style="float: left;display: block">
        <h5>查询条件：</h5>
        <select name="" id="txt_goods_id">
            <option value="1">关键字</option>
            <option value="2">商品id</option>
        </select>
    </div>
    <div style="float: left;display: block">
        <h5>商品</h5>
        <input id="txt_goods_name" type="text" class="txt w200" name="">
    </div>
    <div  style="float: left;display: block">
        <h5>操作：</h5>
        <a id="btn_mb_special_goods_search" class="ncap-btn" href="javascript:;" style="vertical-align: top; margin-left: 5px;">搜索</a>

    </div>
  <div id="mb_special_goods_list" style="margin-top: 100px;"></div>
</div>
<?php } ?>
<script id="item_goods_template" type="text/html">
    <div nctype="item_image" class="item">
        <div class="goods-id" nctype="goods_id" >商品id:<%=goods_id%></div>

        <div class="goods-pic"><img nctype="image" src="<%=goods_image%>" alt=""></div>
        <div class="goods-name" nctype="goods_name"><%=goods_name%></div>
        <div class="goods-price" nctype="goods_price"><%=goods_price%></div>
        <input nctype="goods_id" name="item_data[item][<%=goods_id%>][goods_id]" type="hidden" value="<%=goods_id%>">
        <input nctype="goods_sort" name="item_data[item][<%=goods_id%>][sort]" type="hidden" value="<%=sort%>">
        <input nctype="image" name="item_data[sort]" type="hidden"  value="2">
        <a nctype="btn_edit_item_goods" data-desc="320*130" href="javascript:;" style="right: 60px;"><i class="fa fa-pencil-square-o"></i>编辑</a>
        <a nctype="btn_del_item_image" href="javascript:;">删除</a>
    </div>
</script> 
<script src="<?php echo RESOURCE_SITE_URL;?>/js/jquery.ajaxContent.pack.js" type="text/javascript"></script> 
<script type="text/javascript">
    $(document).ready(function(){
        $('#btn_mb_special_goods_search').on('click', function() {
            var url = '<?php echo urlAdminCms('cms_special', 'goods_list');?>';
            var keyword = $('#txt_goods_name').val();
            var goods_id=$('#txt_goods_id').val();
            var reg = new RegExp("^[0-9]*$");
            if(goods_id==2){
                if(!reg.test(keyword)){
                    alert('商品id必须是数字');
                    return false;
                }
            }
            if(keyword) {
                $('#mb_special_goods_list').load(url + '&' + $.param({keyword: keyword,goods_id:goods_id}));
            }
        });

        $('#mb_special_goods_list').on('click', '[nctype="btn_add_goods"]', function() {
            var sort=parseInt($('#sort').val())+1;

            var item = {};
            item.goods_id = $(this).attr('data-goods-id');
            item.goods_name = $(this).attr('data-goods-name');
            item.goods_price = $(this).attr('data-goods-price');
            item.goods_image = $(this).attr('data-goods-image');
            item.sort=sort;
            $('#sort').val(sort);
            var html = template.render('item_goods_template', item);
            $('[nctype="item_content"]').append(html);
        });
    });
</script> 
