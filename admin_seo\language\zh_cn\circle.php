<?php
defined('InShopNC') or exit('Access Invalid!');
/**
 * circle_class
 */
$lang['circle_please_choose_class']	= '请选择想要删除的分类。';
$lang['circle_class_name_not_null']	= '请填写分类名称';
$lang['circle_class_sort_is_number']= '分类排序只能为数字';
$lang['circle_continue_add']		= '继续添加';
$lang['circle_return_list']			= '返回列表';

$lang['circle_class_name']			= '圈子分类名称';
$lang['circle_class_name_tips']		= '不能超过8个字符';
$lang['circle_class_is_recommend']	= '是否推荐';
$lang['circle_class_status']		= '圈子分类状态';
$lang['circle_class_sort_tips']		= '填写0到255的数字。';
$lang['circle_class_relevance_gclass'] = '关联商品分类';
$lang['circle_class_name_maxlength']= '分类名称不能超过8个字符';
$lang['circle_class_sort_max']		= '排序最大值不能超过255';
$lang['circle_class_prompts_one']	= '创建圈子需要选择分类，用户可以根据分类搜索圈子。';
$lang['circle_class_prompts_two']	= '推荐圈子分类，将分类推荐到首页展示。';
$lang['nc_add_time']				= '创建时间';
$lang['nc_del_circle']				= '删除圈子';
$lang['nc_add_circle']				= '新增圈子';
$lang['nc_edit_circle']				= '编辑圈子';
$lang['nc_circle_pass_cerify']		= '圈子申请通过';
$lang['nc_circle_open']				= '开启圈子';
$lang['nc_circle_close']			= '关闭圈子';
$lang['nc_pass']					= '通过';
/**
 * circle_member
 */
$lang['circle_member_name']			= '成员名称';
$lang['circle_name']				= '圈子名称';
$lang['circle_member_prompts_one']	= '成员列表按照所加入的圈子显示，成员加入过不同的圈子，可能在列表中出现多次。';
$lang['circle_member_prompts_two']	= '推荐功能可以把优秀成员推荐到首页。';
$lang['circle_member_prompts_three']= '建议推荐发表话题数在4以上的成员。';
$lang['circle_belong_to_circle']	= '所属圈子';
$lang['circle_member_identity']		= '身份';
$lang['circle_member_identity_master']	= '圈主';
$lang['circle_member_identity_manager']	= '管理';
$lang['circle_member_identity_member']	= '成员';
$lang['circle_member_theme_count']	= '话题数';
$lang['circle_member_reply_count']	= '回复数';
$lang['circle_member_join_time']	= '加入时间';
$lang['circle_member_lastspeak_time']	= '最后发言';
$lang['circle_no_speak']		= '禁言';
$lang['circle_allow']				= '允许';
$lang['circle_prohibit']			= '禁止';
$lang['circle_member_del_confirm']	= '请注意成员身份，圈主被删除圈子将没有圈主?';
$lang['circle_member_sort_theme_desc']	= '话题数由高到低';
$lang['circle_member_sort_reply_desc']	= '回复数由高到低';

/**
 * circle_manage
 */
$lang['circle_choose_del_circle']	= '请选择你要删除的圈子';
$lang['circle_name_not_null']		= '请填写圈子名称';
$lang['circle_wait_verify']			= '待审核';
$lang['circle_name_tips']			= '圈子名称不能超过8个字符';
$lang['circle_class']				= '圈子分类';
$lang['circle_desc']				= '圈子简介';
$lang['circle_desc_tips']			= '圈子简介不能超过240个字符';
$lang['circle_tag']					= '圈子标签';
$lang['circle_tag_tips']			= '最多可输入50字，请用","进行分隔，例如”手机,发烧友”';
$lang['circle_notice']				= '圈子公告';
$lang['circle_notice_tips']			= '圈子公告不能超过240个字符';
$lang['circle_image']				= '圈子图片';
$lang['circle_ststus']				= '圈子状态';
$lang['circle_is_recommend']		= '是否推荐';
$lang['circle_choose_master']		= '选择圈主';
$lang['circle_name_length_4_12']	= '圈子名字4到12个字符';
$lang['circle_change_name_please']	= '该名称已存在，请更换一个名称';
$lang['circle_choose_master_please']= '请选择圈主';
$lang['circle_master_choose_error']	= '用户不存在或者已经超出允许建立、加入圈子数';
$lang['circle_desc_maxlength']		= '圈子简介长度不能超过240个字符';
$lang['circle_tag_maxlength']		= '圈子标签长度不能超过50个字符';
$lang['circle_notice_maxlength']	= '圈子公告长度不能超过240个字符';
$lang['circle_sort_digits']			= '请填写数字';
$lang['circle_sort_max']			= '排序不能大于255';
$lang['circle_verifying']			= '审核中';
$lang['circle_verify_fail']			= '审核失败';
$lang['circle_verify_fail_reason']	= '关闭/审核失败原因';
$lang['circle_verify_fail_reason_maxlength']	= '圈子关闭原因不能超过240个字符';

$lang['nc_member_name']				= '会员名称';
$lang['nc_result']					= '结果';
$lang['circle_choose_master_result_null']	= '没有搜索到需要的结果。';
$lang['circle_master_name']			= '圈主名称';
$lang['circle_status']				= '圈子状态';
$lang['circle_waiting_verify']		= '等待审核';
$lang['circle_prompts_one']			= '创建圈子需要选择分类，用户可以根据分类搜索圈子。';
$lang['circle_prompts_two']			= '被推荐的圈子将会在圈子首页展示。';
$lang['circle_hot']					= '热门';
$lang['circle_del_confirm']			= '你确定要删除吗？选择确认后与其相关的所有数据都将被删除。';

$lang['circle_verify_prompts_one']	= '创建圈子需要选择分类，用户可以根据分类搜索圈子。';
$lang['circle_verify_prompts_two']	= '被推荐的圈子将会在圈子首页展示。';
$lang['circle_verify_pass']			= '通过审核';
/**
 * circle_setting
 */
$lang['circle_setting_seo']				= 'SEO设置';
$lang['circle_setting_sec']				= '防灌水设置';
$lang['circle_setting_exp']				= '经验规则设置';
$lang['circle_setting_prompts_one']		= '替换内容可以使用限定符 {x} 以限定相邻两字符间可忽略的文字，x 是忽略的字节数。如 "a{1}s{2}s"(不含引号) 可以过滤 "ass" 也可过滤 "axsxs" 和 "axsxxs" 等等。';
$lang['circle_setting_prompts_two']		= '每个中文字符相当于 3 个字节。';
$lang['circle_setting_prompts_three']	= '"/"(不含引号)开头和结尾则表示格式为正则表达式，这时替换内容可用"(n)"引用正则中的子模式，如"/\d{10}([^\d]+|$)/"。';
$lang['circle_setting_isuse']			= '圈子开关';
$lang['circle_setting_isuse_tips']		= '圈子开关，关闭后前台将不会在显示。';
$lang['circle_setting_name']			= '圈子名称';
$lang['circle_setting_name_tips']		= '圈子名称，将显示在圈子前台首页等位置。';
$lang['circle_setting_logo']			= '圈子LOGO';
$lang['circle_setting_logo_tips']		= '建议图片大小 220px * 70px';
$lang['circle_setting_iscreate']		= '创建新圈组开关';
$lang['circle_setting_iscreate_tips']	= '关闭后用户不能在创建新圈组';
$lang['circle_setting_istalk']			= '发表话题/回复话题开关';
$lang['circle_setting_wordfilter']		= '文字过滤';
$lang['circle_setting_wordfilter_tips']	= '内容用英文半角逗号隔开，即“,”。例：“违禁物品,a{1}s{2}s,/\d{10}([^\d]+|$)/”。详细技巧请查看操作提示。';	
$lang['circle_setting_create_sum']		= '每人可创建圈子数';
$lang['circle_setting_create_sum_tips']	= '会员允许创建圈子数量，数量需要大于可加入圈子数量。';
$lang['circle_setting_join_sum']		= '每人可加入圈子数';
$lang['circle_setting_join_sum_tips']	= '会员允许加入圈子数量，所创建的圈子也计算在内。';
$lang['circle_setting_manage_sum']		= '圈子管理数量';
$lang['circle_setting_manage_sum_tips']	= '每个圈子添加管理的最大数量';
$lang['circle_setting_interval']		= '两次发布时间间隔(秒)';
$lang['circle_setting_interval_tips']	= '两次发帖间隔小于此时间，0 为不限制。';
$lang['circle_setting_contentleast']	= '内容字数下限';
$lang['circle_setting_contentleast_tips']	= '内容的最小字符数。';
$lang['circle_setting_adv_prompts_one']	= '圈子首页幻灯广告，默认为四图轮播，根据序列号依次进行播放。';
$lang['circle_setting_adv_prompts_two']	= '上传图大小请严格按照输入框下方提示文字要求进行选择，过大或过小的图片会引起显示变形。';
$lang['circle_setting_adv_prompts_three']	= '如该图片需要跳转到某个网址请在上传框后方输入对应链接地址。';
$lang['circle_setting_adv_url_address']	= '请输入图片要跳转的链接地址';
$lang['circle_setting_adv_img_check']	= '图片限于png,gif,jpeg,jpg格式';
$lang['circle_setting_adv']				= '首页幻灯广告';
$lang['circle_setting_exp_release']		= '发表主题可获经验';
$lang['circle_setting_exp_reply']		= '回复主题可获经验';
$lang['circle_setting_exp_release_max']	= '每天发表主题、回复主题可获最高经验';
$lang['circle_setting_exp_replied']		= '被回复主题可获经验';
$lang['circle_setting_exp_replied_max']	= '每天被回复主题可获最高经验';
$lang['circle_setting_exp_tips']		= '0表示不限制';

/**
 * circle_theme
 */
$lang['circle_theme_list']			= '话题列表';
$lang['circle_theme_info']			= '话题详细';
$lang['circle_theme']				= '话题';
$lang['circle_theme_content']		= '话题内容';
$lang['circle_theme_check_reply']	= '查看回复';
$lang['circle_theme_del_confirm']	= '确定要删除主题吗?';
$lang['circle_theme_del']			= '删除话题';

$lang['circle_theme_name']			= '话题名称';
$lang['circle_top']					= '置顶';
$lang['circle_cream']				= '精华';
$lang['circle_theme_prompts_one']	= '推荐功能可以把话题推荐到首页。';
$lang['circle_theme_prompts_two']	= '“查看”可以查看话题的详细信息，直接跳转到话题详细页。';
$lang['circle_theme_prompts_three']	= '“回复”可以查看当前话题的回复信息，并可以进行删除操作。';
$lang['circle_reply']				= '回复';
$lang['circle_reply_list']			= '回复列表';
$lang['circle_reply_content']		= '回复内容';
$lang['circle_reply_time']			= '回复时间';
$lang['circle_reply_check_info']	= '查看详细';
$lang['cirlce_administrator']		= '平台管理';

$lang['circle_poll_info']			= '投票信息';
$lang['circle_poll_form']			= '投票形式';
$lang['circle_poll_starttime']		= '开始时间';
$lang['circle_poll_days']			= '记票天数';
$lang['circle_poll_sum']			= '总投票数';
$lang['circle_poll_radio']			= '单选';
$lang['circle_poll_checkbox']		= '多选';
$lang['circle_poll_option']			= '投票选项';
$lang['circle_poll_option_count']	= '得票数';
$lang['circle_poll_option_participant']	= '参与者';
/**
 * experience log
 */
$lang['circle_exp_theme_delete']	= '话题被删除失去经验';
$lang['circle_exp_reply_delete']	= '回复被删除失去经验';
