<?php defined('InShopNC') or exit('Access Invalid!');?>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title"> <a class="back" href="index.php?act=distri_binding_chain&op=index" title="返回列表"> <i class="fa fa-arrow-circle-o-left"></i> </a>
            <div class="subject">
                <h3>返回列表</h3>
                <h5>操作 - 新增</h5>
            </div>
        </div>
    </div>
</div>
<div class="ncap-form-default">
    <dl class="row">
        <dt class="tit">
            <label>内部分销员Id（客服）</label>
        </dt>
        <dd class="opt">
            <input type="text" value="" name="member_id" id="member_id" class="input-txt">
            <p class="notic"></p>
        </dd>
    </dl>
    <dl class="row">
        <dt class="tit">
            <label>绑定门店财务编码</label>
        </dt>
        <dd class="opt">
            <input type="text" value="" name="account_id" id="account_id" class="input-txt">
            <p class="notic"></p>
        </dd>
    </dl>
    <dl class="row">
        <dt class="tit">
            <label>门店爱省钱账号绑定的手机号</label>
        </dt>
        <dd class="opt">
            <input type="text" value="" name="chain_member_mobile" id="chain_member_mobile" class="input-txt">
            <p class="notic"></p>
        </dd>
    </dl>
    <div class="bot" id="submit-holder"><a class="ncap-btn-big ncap-btn-green" href="javascript:;" onclick="cash_no()"><?php echo $lang['nc_submit'];?></a></div>
</div>

<script>
    function cash_no(){
        var member_id = $('input[name="member_id"]').val()
        var account_id = $('input[name="account_id"]').val()
        var chain_member_mobile = $('input[name="chain_member_mobile"]').val()
        if(!member_id){
            alert('内部分销员Id必须填写');
            return false;
        }
        if(!account_id){
            alert('绑定门店财务编码必须填写');
            return false;
        }
        if(!chain_member_mobile){
            alert('门店爱省钱账号绑定的手机号必须填写');
            return false;
        }
        var phoneReg = /(^1[3|4|5|6|7|8|9]\d{9}$)|(^09\d{8}$)/;
        if (!phoneReg.test(chain_member_mobile)) {
            alert("手机号应是11位数字"); return false;
        }
        $.ajax({
            type: "POST",
            url: "index.php?act=distri_binding_chain&op=bind_add_chain",
            data: {member_id:member_id,account_id:account_id,chain_member_mobile:chain_member_mobile},
            dataType:'json',
            success: function(data){
                alert(data.msg);
                if(data.code == 200){
                    window.location.href=window.location.href;
                }
            }
        });
    }
</script>