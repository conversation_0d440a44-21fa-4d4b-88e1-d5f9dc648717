<?php defined('InShopNC') or exit('Access Invalid!');?>
<form method="post" enctype="multipart/form-data" name="form1" action="index.php?act=distri_order&op=order_modify">
    <input type="hidden" name="form_submit" value="ok" />
    <input type="hidden" name="order_id" value="<?php echo $output['order_id']?>" />
    <input type="hidden" name="order_type" value="<?php echo $output['order_type']?>" />
  <div class="ncap-form-default">
    <dl class="row">
      <dt class="tit">
          <label><select name="dis_name_type"><option value="1" selected="selected">手机号码</option><option value="2">会员ID</option></select></label>
      </dt>
        <dd class="opt" ><input value="" name="dis_name" style="margin-top: 2px;"> <a class="ncap-btn-big ncap-btn-green" href="javascript:queryMemberInfo()" style="height: 14px; padding-top: 0; margin-top: -4px;">搜索</a>
      </dd>
    </dl>
    <dl class="row">
        <table align="center" border="1" class="memberInfo"><tr><td width="50" height="22">会员ID</td><td width="100">会员名称</td><td width="100">会员手机</td><td width="200">绑定分院</td></tr></table>
    </dl>
      <?php for ($i = 1;$i <= 3;$i++) { ?>
          <dl class="row">
              <dt class="tit">
                  <label>审核凭证<?php echo $i;?></label>
              </dt>
              <dd class="opt">
                  <div class="input-file-show">
                      <span class="type-file-box">
            <input type="text" name="textfield" id="textfield<?php echo $i;?>" class="type-file-text" />
            <input type="button" name="button" id="button<?php echo $i;?>" value="选择上传..." class="type-file-button" />
            <input class="type-file-file" id="pic<?php echo $i;?>" name="pic<?php echo $i;?>" type="file" size="30" hidefocus="true" title="最多三张图片，每张图片大小3M内，点击确认上传生效" accept="image/*">
            </span></div> <a href="JavaScript:void(0);" class="ncap-btn" onclick="clear_pic(<?php echo $i;?>)"><i class="fa fa-trash"></i>
                      删除</a>
                  <p class="notic"></p>
              </dd>
          </dl>
      <?php } ?>

    <div class="bot" id="submit-holder"><a class="ncap-btn-big ncap-btn-green" href="javascript:checkForm()">确认</a></div>

  </div>
</form>
<script type="text/javascript" src="<?php echo ADMIN_RESOURCE_URL;?>/js/jquery.nyroModal.js"></script>
<script>
    function checkForm(){
        if ($("input[name=dis_name]").val()==""){
            alert("新分销员信息为空!");
            $("input[name=dis_name]").focus();
            return false;
        }
        if ($("#textfield1").val()=="" && $("#textfield2").val()=="" && $("#textfield3").val()=="" ){
            alert("请上传审核凭证!");
            return false;
        }
        document.form1.submit();
    }
    function queryMemberInfo(){
        if ($("input[name=dis_name]").val()==""){
            return false;
        }
        $('.result').remove();
        $.ajax({
            type: "GET",
            url: "index.php?act=distri_bill&op=ajax_dis_name",
            data: {dis_name_type:$("select[name=dis_name_type]").val(),dis_name:$("input[name=dis_name]").val()},
            dataType:'json',
            success: function(data){
                if(data.code == 200){
                    var trTemp = $("<tr class='result' height='26'></tr>");
                    trTemp.append("<td>" + data.member_id + "</td>");
                    trTemp.append("<td>" + data.member_name + "</td>");
                    trTemp.append("<td>" + data.member_mobile + "</td>");
                    trTemp.append("<td>" + (data.chain_name==null ? '外部代理人或其他' : data.chain_name) + "</td>");
                    trTemp.appendTo(".memberInfo");
                }else{
                    var trTemp = $("<tr class='result' height='26'></tr>");
                    trTemp.append("<td colspan='4'>" + data.msg + "</td>");
                    trTemp.appendTo(".memberInfo");
                }
            }
        });
    }
    $(function(){
        $('input[class="type-file-file"]').change(function(){
            var pic=$(this).val();
            var extStart=pic.lastIndexOf(".");
            var ext=pic.substring(extStart,pic.length).toUpperCase();
            $(this).parent().find(".type-file-text").val(pic);
            if(ext!=".PNG"&&ext!=".GIF"&&ext!=".JPG"&&ext!=".JPEG"){
                alert("图片限于png,gif,jpeg,jpg格式");
                $(this).attr('value','');
                return false;
            }
        });
        $('.nyroModal').nyroModal();
    });
    function clear_pic(n) {//置空
        $("#show" + n + "").remove();
        $("#textfield" + n + "").val("");
        $("#pic" + n + "").val("");
        $("#show_pic"+n+"").val("");
    }
</script>