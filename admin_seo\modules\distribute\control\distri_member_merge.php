<?php
/**
 * 分销-分销商管理
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;

defined('InShopNC') or exit('Access Invalid!');

class distri_member_mergeControl extends SystemControl
{
    private $_links = array(
        array('url' => 'act=distri_member_merge&op=index', 'text' => '合并记录'),
        array('url' => 'act=distri_member_merge&op=member_merge', 'text' => '合并申请'),

        );
    const EXPORT_SIZE = 10000;

    function __construct()
    {
        parent::__construct();
    }



    public function indexOp()
    {
        //输出子菜单
        Tpl::output('top_link', $this->sublink($this->_links, 'index'));
        Tpl::output('mem_stat', 2);
        Tpl::showpage('dis_member_merge.index');
    }

    /**
     * 合并申请
     */
    public function member_mergeOp()
    {
        $model_setting = Model('setting');
        $setting_info = $model_setting->getRowSetting('distri_user_time');

        Tpl::output('setting_info',$setting_info);
        //输出子菜单
        Tpl::output('top_link', $this->sublink($this->_links, 'member_merge'));
        Tpl::output('mem_stat', 1);

        Tpl::showpage('distri_member.merge');
    }
    /**
     * 认证申请
     */
    public function auth_upOp()
    {
        //输出子菜单
        Tpl::output('top_link', $this->sublink($this->_links, 'auth_up'));
        Tpl::output('mem_stat', 1);
        Tpl::showpage('member.index');
    }

    /**
     * Notes:分销员修改手机号
     * User: rocky
     * DateTime: 2021/12/14 16:52
     */
    public function manage_saveOp()
    {
        $old = $_POST['old_mobile'];
        $new = $_POST['new_mobile'];

        if (!preg_match('/^1\d{10}$/', $new) || !preg_match('/^1\d{10}$/', $old)) {
            showMessage('手机号格式不正确');
        }
        if ($new == $old) {
            showMessage('新旧账号不能一致');
        }

        $newMember = Model('member')->getMemberInfo(['member_mobile' => $new]);
        $oldMember = Model('member')->getMemberInfo(['member_mobile' => $old]);

        if (empty($newMember) || empty($oldMember)) {
            showMessage('账号不存在');
        }
        if ($oldMember['distri_state'] <> 2) {
            showMessage('旧账号非分销账号，不需要合并');
        }

        $model = Model();
        $message = '操作成功。';

        try {
            $model->beginTransaction();

            foreach ([
                         'dis_pay' => 'dis_member_id',
                         'dis_trad_cash' => 'tradc_member_id',
                         'dis_trad_log' => 'lg_member_id',
                         'distri_member_fans' => ['member_id','dis_member_id'],
                         'distri_outside_member' => ['member_id','dis_member_id'],
                         'vr_order' => 'dis_member_id',
                         'vr_order_code' => 'dis_member_id',
                         'order_goods' => 'dis_member_id',
                     ] as $table => $columns) {

                foreach ((array)$columns as $column ){
                    $model->table($table)
                        ->where([$column => $oldMember['member_id']])->update([
                            $column => $newMember['member_id']
                        ]);
                }
            }

            Model('member')->editMember(['member_id' => $newMember['member_id']], [
                'trad_amount' => $newMember['trad_amount'] + $oldMember['trad_amount'],
                'freeze_trad' => $newMember['freeze_trad'] + $oldMember['freeze_trad'],
                'dis_trad_money' => $newMember['dis_trad_money'] + $oldMember['dis_trad_money'],
                'distri_state' => $oldMember['distri_state'],
                'bill_user_name' => $oldMember['bill_user_name'],
                'bill_type_code' => $oldMember['bill_type_code'],
                'bill_type_number' => $oldMember['bill_type_number'],
                'bill_bank_name' => $oldMember['bill_bank_name'],
                'distri_chainid' => $oldMember['distri_chainid'],
                'bill_bank_branch' => $oldMember['bill_bank_branch'],
                'member_identity' => $oldMember['member_identity'],
            ]);

            Model('member')->editMember(['member_id' => $oldMember['member_id']], [
                'trad_amount' => 0,
                'freeze_trad' => 0,
                'dis_trad_money' => 0,
                'distri_state' => 0
            ]);

            $model->commit();
        } catch (Exception $exception) {
            $model->rollback();
            $message = '操作失败。';
        }
        showMessage($message);
    }

    /**
     * 认证申请
     */
    public function member_settingOp()
    {
        $model_setting = Model('setting');
        $setting_info = $model_setting->getRowSetting('distri_user_time');

        Tpl::output('setting_info',$setting_info);
        //输出子菜单
        Tpl::output('top_link', $this->sublink($this->_links, 'member_setting'));
        Tpl::output('mem_stat', 1);

        Tpl::showpage('distri_member.merge');
    }

    /**
     * 认证详情
     */
    public function member_infoOp()
    {
        $member_id = intval($_REQUEST['member_id']);
        if ($member_id <= 0) {
            showMessage('会员不存在', 'index.php?act=distri_member');
        }
        $member_model = Model('member');
        $member_info = $member_model->getMemberInfoByID($member_id);

        //可提现金额
        $available_trad = $member_info['trad_amount'];

        //冻结金额
        $freeze_trad = floatval($member_info['freeze_trad']);
        if($member_info['distri_state'] == 2){
            if($member_info['trad_amount'] >= C('distribute_bill_limit')){
                $freeze_trad += C('distribute_bill_limit');
                $available_trad -= C('distribute_bill_limit');
            }else{
                $freeze_trad += $member_info['trad_amount'];
                $available_trad = 0;
            }
        }

        $member_info['available_distri_trad'] = $available_trad;
        $member_info['freeze_distri_trad'] = $freeze_trad;
        $chain_info = Model('chain')->getChainInfo(array('chain_id'=>$member_info['distri_chainid']),"chain_name");
        $member_info['chain_name'] = $chain_info['chain_name'];

        Tpl::output('member_info', $member_info);
        $sex_array = $this->get_sex();
        Tpl::output('sex_array', $sex_array);
        Tpl::showpage('member.info');
    }

    /**
     * 会员认证
     */
    public function authOp()
    {
        if (!empty($_POST)) {
            $param = array();
            $member_model = Model('member');
            $param['auth_message'] = trim($_POST['joinin_message']);
            $param['distri_state'] = $this->_get_stat($_POST['verify_type']);
            $param['distri_code'] = getUniqueCode(uniqid());

            if($_POST['verify_type'] == 'pass'){
                $param['distri_handle_time'] = time();
                $param['distri_show'] = 1;
            }
            $dis_member_model = Model('dis_member_fans');

            $member_id = intval($_POST['member_id']);

            $stat = $member_model->editMember(array('member_id' => $member_id), $param);
            if ($stat) {
                if($param['distri_show'] == 1){
                    if (intval($param['distri_chainid']) > 0) {
                        $content = '【阿闻爱省钱】您已通过审核，请至分店后台绑定对应注册手机号，绑定成功后即可使用。';
                    } else {
                        $content = '【阿闻爱省钱】您的代理人资质已审核通过，来阿闻爱省钱小程序开启您的“爱宠健康大使”生涯。稍后将通过客服电话为您提供咨询指引服务，请注意接听。';
                    }
                    $member_info = $member_model->getMemberInfo(['member_id'=>$member_id],'member_mobile,distri_formId,weixin_mini_openidasq,distri_chainid');
                    /*if ($member_info['distri_formId']) {
                        $logic_wx_api = Logic('wx_api');
                        $order_info['check_notes'] = $param['auth_message'];
                        $order_info['check_time'] = date('Y-m-d H:i:s');
                        $param['code'] = 'fenxiao_check_result';
                        $res = $logic_wx_api->getTemplateData($param['code'],$order_info);
                        $access_token = $logic_wx_api->getAsqAccessToken();
                        $msg['to_id'] = $member_info['weixin_mini_openidasq'];//小程序openid
                        $msg['subject'] = "QS4U7IIvz2CFUseMO0FMVhNNpXbNOVn8yGgPNLw4pTo";//模板ID
                        $msg['log_msg'] = serialize($res);
                        $msg['form_id'] = $member_info['distri_formId'];
                        $msg['page_url'] = "pages/distribute_index/index";
                        $result = $logic_wx_api->sendMiniTemplate($access_token,$msg);
                        if (!$result) {
                            $member_model->sendSmsMobile($member_info['member_mobile'], $content);
                        }
                    }else {*/
                        $member_model->sendSmsMobile($member_info['member_mobile'], $content);
                    //}
                    //如果申请的是外部分销员 lihaobin
                    if(!$member_info['distri_chainid']){

                        $dis_member_model->addDisMemberOutside($member_id);
                    }

                }
                showMessage('认证处理成功', 'index.php?act=distri_member');
            } else {
                showMessage('认证处理失败', 'index.php?act=distri_member&op=member_info&member_id=' . $member_id);
            }
        } else {
            showMessage('非法请求', 'index.php?act=distri_member');
        }
    }

    /**
     * 会员批量认证
     */
    public function batch_authOp(){
        if (chksubmit()) {
            $member_model = Model('member');
            $dis_member_model = Model('dis_member_fans');
            $condition = array();
            $condition['distri_state'] = 1;
            if ($_POST['member_id'] != '') {
                $id_array = explode(',', $_POST['member_id']);
                $condition['member_id'] = array('in', $id_array);
            }
            $member_list =  $member_model->getMemberList($condition,'member_id,member_mobile,distri_formId,weixin_mini_openidasq');
            if(!empty($member_list)){
                $param = array();
                $param['auth_message'] = trim($_POST['joinin_message']);
                $param['distri_state'] = $_POST['verify_type'] == 1 ? 2 : 3;
                foreach ($member_list as $member_info){
                    $param['distri_code'] = getUniqueCode(uniqid());
                    if($_POST['verify_type'] == 1){
                        $param['distri_handle_time'] = time();
                        $param['distri_show'] = 1;
                        if (intval($param['distri_chainid']) > 0) {
                            $content = '【阿闻爱省钱】您已通过审核，请至分店后台绑定对应注册手机号，绑定成功后即可使用。';
                        } else {
                            $content = '【阿闻爱省钱】您的代理人资质已审核通过，来阿闻爱省钱小程序开启您的“爱宠健康大使”生涯。稍后将通过客服电话为您提供咨询指引服务，请注意接听。';
                        }
                        /*if ($member_info['distri_formId']) {
                            $logic_wx_api = Logic('wx_api');
                            $order_info['check_notes'] = $param['auth_message'];
                            $order_info['check_time'] = date('Y-m-d H:i:s');
                            $param['code'] = 'fenxiao_check_result';
                            $res = $logic_wx_api->getTemplateData($param['code'],$order_info);
                            $access_token = $logic_wx_api->getAsqAccessToken();
                            $msg['to_id'] = $member_info['weixin_mini_openidasq'];//小程序openid
                            $msg['subject'] = "QS4U7IIvz2CFUseMO0FMVhNNpXbNOVn8yGgPNLw4pTo";//模板ID
                            $msg['log_msg'] = serialize($res);
                            $msg['form_id'] = $member_info['distri_formId'];
                            $msg['page_url'] = "pages/distribute_index/index";
                            $result = $logic_wx_api->sendMiniTemplate($access_token,$msg);
                            if (!$result) {
                                $member_model->sendSmsMobile($member_info['member_mobile'], $content);
                            }
                        }else{*/
                            $member_model->sendSmsMobile($member_info['member_mobile'], $content);
                        //}


                    }
                    $member_model->editMember(array('member_id' => $member_info['member_id']), $param);
                    //如果申请的是外部分销员 lihaobin
                    if(!$member_info['distri_chainid']){

                        $dis_member_model->addDisMemberOutside($member_info['member_id']);
                    }
                }
            }else {
                showDialog(Language::get('nc_common_save_fail'),'','error','$("#flexigrid").flexReload();CUR_DIALOG.close();');
            }
            showDialog(L('nc_common_op_succ'), '', 'succ', '$("#flexigrid").flexReload();CUR_DIALOG.close()');
        }
        Tpl::output('member_id', $_GET['id']);
        Tpl::showpage('distri_member.batch_auth', 'null_layout');
    }

    /**
     * 清退分销商
     */
    public function member_cancleOp()
    {
        $member_id = intval($_GET['member_id']);
        $data = array();
        $data['state'] = false;
        if ($member_id <= 0) {
            $data['msg'] = '参数错误';
            exit(json_encode($data));
        }
        $member_model = Model('member');
        $param = array();
        $param['distri_state'] = 4;
        $param['distri_code'] = '';
        $param['quit_time'] = time();
        $param['distri_chainid'] = 0;
        $param['distri_quit_times'] = array('exp','distri_quit_times+1');
        $condition = array();
        $condition['member_id'] = $member_id;
        $member_info = $member_model->getMemberInfo($condition);
        $stat = $member_model->editMember($condition, $param);
        if ($stat) {
            $data['state'] = true;

            $dis_goods_model = Model('dis_goods');
            $dis_goods_model->delDistriGoods($condition);

            //Model('dis_trad')->autoDistriTrad($member_info);

            $distribue_logic = Logic('distribute');
            //分销人清退操作
            $distribue_logic->disMemberClear($member_info);

            exit(json_encode($data));
        } else {
            $data['msg'] = '清退失败';
            exit(json_encode($data));
        }
    }

    /**
     * 分销员查看
     */
    public function show_memberOp()
    {
        $member_id = intval($_GET['member_id']);
        if($member_id <= 0){
            showMessage('参数错误','index.php?act=distri_member&op=show_member','html','error');
        }
        $this->_links[] = array('url' => 'act=distri_member&op=show_member', 'text' => '分销订单');
        Tpl::output('top_link', $this->sublink($this->_links, 'show_member'));
        Tpl::output('member_id',$member_id);
        Tpl::showpage('member.show');
    }

    /**
     * 性别
     * @return multitype:string
     */
    private function get_sex() {
        $array = array();
        $array[1] = '男';
        $array[2] = '女';
        $array[3] = '保密';
        return $array;
    }

    /**
     * 获取审核状态值
     * @param $param
     * @return int
     */
    private function _get_stat($param)
    {
        $stat = 1;
        if ($param == 'pass') {
            $stat = 2;
        } elseif ($param == 'fail') {
            $stat = 3;
        }
        return $stat;
    }

    /**
     * 输出XML数据
     */
    public function get_xmlOp()
    {
        $model_member = Model('member');
        $condition = array();
        if ($_POST['query'] != '') {
            $condition[$_POST['qtype']] = $_POST['query'];
        }

        $order = '';
        $param = array('new_membe_id', 'old_member_id', 'new_member_mobile', 'old_member_mobile');
        if (in_array($_POST['sortname'], $param) && in_array($_POST['sortorder'], array('asc', 'desc'))) {
            $order = $_POST['sortname'] . ' ' . $_POST['sortorder'];
        }
        $page = $_POST['rp'];

        $member_list = Model()->table('dis_member_merge')->where($condition)->page($page)->order($order)->select();


        $data = array();
        $data['now_page'] = $model_member->shownowpage();
        $data['total_num'] = $model_member->gettotalnum();

        foreach ($member_list as $value) {
            $param = array();

            $param['new_member_id'] = $value['new_member_id'];
            $param['new_member_mobile'] = $value['new_member_mobile'];
            $param['old_member_mobile'] = $value['old_member_mobile'];
            $param['create_time_text'] = date('Y-m-d H:i:s',$value['create_time']);
            $data['list'][$value['new_member_id']] = $param;
        }
        echo Tpl::flexigridXML($data);
        exit();
    }


    public function get_member_xmlOp()
    {
        $member_id = intval($_GET['member_id']);

        $condition = array();

        $condition['dis_member_id'] = $member_id;

        $model = Model('dis_order');
        $order_model = Model('order');
        $vr_order_model = Model('vr_order');
        $page = $_POST['rp'];
        $member_list = $model->getDisPayOrderList($condition, 'order_sn,order_id,dis_pay_amount,dis_pay_time,log_state,is_virtual', $page);

        $data = array();
        $data['now_page'] = $model->shownowpage();
        $data['total_num'] = $model->gettotalnum();

        foreach ($member_list as $value) {
            $param = array();
            if($value['is_virtual']==1){
                $vr_order_info = $vr_order_model->getOrderInfo(['order_id'=>$value['order_id']],'order_sn,order_amount,order_state,add_time');
                $param['order_sn'] = $vr_order_info['order_sn'];
                $param['add_time'] = date('Y-m-d', $vr_order_info['add_time']);
                $param['order_amount'] = ncPriceFormat($vr_order_info['order_amount']);
                $param['order_state'] = str_replace(array(10,20,40,0),array('待付款','待使用','已完成','已取消'),$vr_order_info['order_state']);
            }else{
                $order_info = $order_model->getOrderInfo(['order_id'=>$value['order_id']],['order_goods'],'order_sn,order_amount,order_state,add_time');

                $param['order_sn'] = $order_info['order_sn'];
                $param['add_time'] = date('Y-m-d', $order_info['add_time']);
                $param['order_amount'] = ncPriceFormat($order_info['order_amount']);
                $param['order_state'] = str_replace(array(10,20,30,40,0),array('待付款','待发货','已发货','已完成','已取消'),$order_info['order_state']);

            }
            $param['dis_pay_amount'] = ncPriceFormat($value['dis_pay_amount']);
            $param['dis_pay_time'] = $value['dis_pay_time']?date('Y-m-d', $value['dis_pay_time']):'';
            $param['log_state'] = str_replace(array(1,0),array('已结算','未结算'),intval($value['log_state']));
            $data['list'][$value['order_id']] = $param;

        }

        echo Tpl::flexigridXML($data);
        exit();

    }


    /**
     * csv导出
     */
    public function export_csvOp()
    {
        $model_member = Model('member');
        $condition = array();
        $where = [];
        $limit = false;
        if ($_GET['id'] != '') {
            $id_array = explode(',', $_GET['id']);
            $where['member.member_id'] = $condition['member_id'] = array('in', $id_array);
        }
        if ($_GET['query'] != '') {
            $condition[$_GET['qtype']] = array('like', '%' . $_GET['query'] . '%');
        }
        $order = '';
        $param = array('member_id', 'member_name', 'member_avatar', 'member_email', 'member_mobile', 'member_sex', 'member_truename', 'member_time', 'member_login_time', 'member_login_ip', 'trad_amount', 'distri_state', 'freeze_trad','distri_time');
        if (in_array($_GET['sortname'], $param) && in_array($_GET['sortorder'], array('asc', 'desc'))) {
            $order = $_GET['sortname'] . ' ' . $_GET['sortorder'];
        }
        $distri_stat = 1;
        if ($_REQUEST['mem_state'] == 2) {
            $where['member.distri_state'] =$condition['distri_state'] = array('in',array(2,4,5));

            $distri_stat = $_REQUEST['mem_state'];
        } else {
            $where['member.distri_state'] =$condition['distri_state'] = array('gt', 0);
        }

        if (!is_numeric($_GET['curpage'])) {
            $count = $model_member->getMemberCount($condition);
            if ($count > self::EXPORT_SIZE) {   //显示下载链接
                $array = array();
                $page = ceil($count / self::EXPORT_SIZE);
                for ($i = 1; $i <= $page; $i++) {
                    $limit1 = ($i - 1) * self::EXPORT_SIZE + 1;
                    $limit2 = $i * self::EXPORT_SIZE > $count ? $count : $i * self::EXPORT_SIZE;
                    $array[$i] = $limit1 . ' ~ ' . $limit2;
                }
                Tpl::output('list', $array);
                Tpl::output('murl', 'index.php?act=member&op=index');
                Tpl::showpage('export.excel');
                exit();
            }
        } else {
            $limit1 = ($_GET['curpage'] - 1) * self::EXPORT_SIZE;
            $limit2 = self::EXPORT_SIZE;
            $limit = $limit1 . ',' . $limit2;
        }

        $field = 'member.member_id,member.member_name,member.member_mobile,member.distri_state,member.member_email,member.distri_time,member.distri_handle_time,
        member.bill_user_name,member.distri_chainid,
        chain.chain_name,chain_region.region_name';
        $on = 'member.distri_chainid = chain.chain_id,chain.region_id = chain_region.region_id';

        $member_list_tmp = Model()->table('member,chain,chain_region')->field($field)->join('left,left')->on($on)->where($where)->limit($limit)->select();
        //$member_list_tmp = $model_member->getMemberList($condition, '*', null, $order, $limit);

        $member_list = array();

        foreach ($member_list_tmp as $value) {
            if($value['distri_chainid']>0){
                $value['distri_type'] = '内部员工';
            }else{
                $value['distri_type'] = '外部代理';
            }

            $member_list[$value['member_id']] = $value;
        }

        $this->getMemberExtension($member_list);
        $this->createCsv($member_list,$distri_stat);
    }

    /**
     * 生成csv文件
     */
    private function createCsv($member_list,$distri_stat)
    {
        $data = array();
        foreach ($member_list as $value) {
            $param = array();
            $param['member_id'] = $value['member_id'];
            $param['member_name'] = $value['member_name'];
            if ($distri_stat == 1) {
                $param['distri_stat'] = str_replace(array('1', '2', '3', '4', '5'), array('待审核', '已通过', '未通过', '清退', '退出'), $value['distri_state']);
                $param['member_mobile'] = $value['member_mobile'];
                $param['member_email'] = $value['member_email'];
                $param['distri_time'] = $value['distri_time'] ? date('Y-m-d', $value['distri_time']) : '';
                $param['distri_handle_time'] = ($value['distri_handle_time'] && $value['distri_state'] == 2) ? date('Y-m-d', $value['distri_handle_time']) : '';
                $param['distri_type'] = $value['distri_type'];
                $param['chain_name'] = $value['chain_name'];
                $param['region_name'] = $value['region_name'];
                $param['bill_user_name'] = $value['bill_user_name'];
            } else {
                $param['member_email'] = $value['member_email'];
                $param['member_mobile'] = $value['member_mobile'];
                $param['order_count'] = $value['order_count'] ? $value['order_count'] : 0;
                $param['had_pay_amount'] = ncPriceFormat($value['had_pay_amount']);
                $param['unpay_amount'] = ncPriceFormat($value['unpay_amount']);
                $param['distri_amount'] = ncPriceFormat($value['had_pay_amount'] + $value['unpay_amount']);
                $param['distri_type'] = $value['distri_type'];
                $param['chain_name'] = $value['chain_name'];
                $param['region_name'] = $value['region_name'];
                $param['bill_user_name'] = $value['bill_user_name'];
            }
            $data[$value['member_id']] = $param;
        }
        $header = array();
        if($distri_stat == 1){
            $header = array('member_id' => '会员ID', 'member_name' => '会员名称', 'distri_stat' => '申请状态', 'member_email' => '会员邮箱', 'member_mobile' => '会员手机', 'distri_time' => '申请时间', 'distri_handle_time' => '通过时间','distri_type'=>'人员归属','chain_name'=>'归属门店','region_name'=>'归属大区','bill_user_name'=>'持卡人');
        }else{
            $header = array('member_id' => '会员ID', 'member_name' => '会员名称', 'member_email' => '会员邮箱', 'member_mobile' => '会员手机', 'order_count' => '分销单数', 'had_pay_amount' => '已结佣金(元)', 'unpay_amount' => '未结佣金(元)', 'distri_amount' => '分销总额(元)','distri_type'=>'人员归属','chain_name'=>'归属门店','region_name'=>'归属大区','bill_user_name'=>'持卡人');
        }

        \Shopnc\Lib::exporter()->output('member_list' . $_GET['curpage'] . '-' . date('Y-m-d'), $data, $header);
    }

}