<?php

use Upet\Models\Order as OrderAlias;

defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title"><a class="back" href="javascript:history.back(-1)" title="返回列表"><i class="fa fa-arrow-circle-o-left"></i></a>
      <div class="subject">
        <h3>虚拟订单退款 - 查看退单“退单编号：<?php echo $output['refund']['refund_sn']; ?>”</h3>
        <h5>虚拟类商品订单退款申请及审核处理</h5>
      </div>
    </div>
  </div>
  <div class="ncap-form-default">
    <div class="title">
      <h3>买家退款申请</h3>
    </div>
      <dl class="row">
          <dt class="tit">订单编号</dt>
          <dd class="opt"><?php echo $output['refund']['order_sn']; ?> </dd>
      </dl>
      <dl class="row">
        <dt class="tit">申请时间</dt>
        <dd class="opt"><?php echo date('Y-m-d H:i:s',$output['refund']['add_time']); ?> </dd>
      </dl>
    <dl class="row">
      <dt class="tit">商品名称</dt>
      <dd class="opt"><a href="<?php echo urlShop('goods','index',array('goods_id'=> $output['refund']['goods_id']));?>" target="_blank"><?php echo $output['refund']['goods_name']; ?></a></dd>
    </dl>
    <dl class="row">
      <dt class="tit">兑换码</dt>
      <dd class="opt">
        <?php $has_tips=false;?>
        <?php if (is_array($output['code_array']) && !empty($output['code_array'])) { ?>
        <?php foreach ($output['code_array'] as $key => $val) { 
          $model_vr_order = Model('vr_order');
          $vr_code_info = $model_vr_order->getOrderCodeInfo(array('vr_code' => $val),'vr_usetime,chain_name');
          //如果超过 3个月（90天）
          if(($output['refund']['add_time']-$vr_code_info['vr_usetime'])>90*86400 && $vr_code_info['vr_usetime']>0){
          	$has_tips=true;
          }
          ?>
          <?php echo $val.($vr_code_info['vr_usetime']?'<font color="red">（核销时间：'.date("Y-m-d H:i:s",$vr_code_info['vr_usetime']).' 核销门店：'.$vr_code_info['chain_name'].'）</font>':'');?><br />
        <?php } ?>
        <?php } ?>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit"><?php echo $lang['refund_order_refund'];?></dt>
      <dd class="opt"><?php echo ncPriceFormat($output['refund']['refund_amount']); ?></dd>
    </dl>
    <dl class="row">
      <dt class="tit">退款说明</dt>
      <dd class="opt"><?php echo $output['refund']['buyer_message']; ?></dd>
    </dl>
    <div class="title">
      <h3>平台退款审核</h3>
    </div>
    <dl class="row">
      <dt class="tit">平台处理</dt>
      <dd class="opt"><?php echo $output['admin_array'][$output['refund']['admin_state']];?></dd>
    </dl>
    <?php if (in_array($output['refund']['admin_state'],[2,3])) { ?>
    <dl class="row">
      <dt class="tit"><?php echo $lang['refund_message'];?></dt>
      <dd class="opt"><?php echo $output['refund']['admin_message']; ?></dd>
    </dl>
    <dl class="row">
      <dt class="tit">处理时间</dt>
      <dd class="opt"><?php echo $output['refund']['admin_time'] ? date('Y-m-d H:i:s',$output['refund']['admin_time']) : null; ?> </dd>
    </dl>
    <?php if($has_tips){?>
    <dl class="row">
    <dt class="tit"><font color="red">异常退款</font></dt>
    <dd class="opt"><font color="red">核销完超过3个月退款！ </font></dd>
    </dl>
    <?php }?>
    <?php if (in_array($output['detail_array']['refund_code'],['wxpay', 'wx_jsapi', 'wx_saoma','card'])) { ?>
        <dl class="row">
            <dt class="tit"></dt>
            <dd class="opt">
                <a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="wxpayBtn">
                    <?php echo $output['detail_array']['refund_code'] == 'card' ? '储值卡' : '微信'; ?>退款查询
                </a>
            </dd>
        </dl>
    <?php } elseif ($output['detail_array']['refund_code'] == 'alipay') { ?>
        <dl class="row">
            <dt class="tit"></dt>
            <dd class="opt">
                <a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="alipayBtn">支付宝退款查询</a>
            </dd>
        </dl>
    <?php } ?>
    <?php if ($output['detail_array']['refund_state'] == 2) { ?>
    <div class="title">
      <h3>退款详细</h3>
    </div>
    <dl class="row">
      <dt class="tit">支付方式</dt>
      <dd class="opt"><?php echo orderPaymentName($output['detail_array']['refund_code']);?></dd>
    </dl>
    <dl class="row">
      <dt class="tit">在线退款金额</dt>
      <dd class="opt"><?php echo ncPriceFormat($output['detail_array']['pay_amount']); ?> </dd>
    </dl>
    <dl class="row">
      <dt class="tit">预存款金额</dt>
      <dd class="opt"><?php echo ncPriceFormat($output['detail_array']['pd_amount']); ?> </dd>
    </dl>
    <dl class="row">
      <dt class="tit">充值卡金额</dt>
      <dd class="opt"><?php echo ncPriceFormat($output['detail_array']['rcb_amount']); ?> </dd>
    </dl>
    <?php } ?>
    <?php } ?>
  </div>
</div>
<script type="text/javascript" src="<?php echo ADMIN_RESOURCE_URL;?>/js/jquery.nyroModal.js"></script>
<script type="text/javascript" src="<?php echo ADMIN_RESOURCE_URL;?>/js/refund.js"></script>

<script type="text/javascript">
$(function(){
    $('.nyroModal').nyroModal();
    $("#wxpayBtn").click(function(){
        var ajaxurl = '<?php echo ADMIN_SITE_URL;?>/index.php?act=vr_refund&op=queryWxpay&refund_id=<?php echo $output['refund']['refund_id']; ?>';
        show_msg(ajaxurl);
    });
    $("#alipayBtn").click(function(){
        var ajaxurl = '<?php echo ADMIN_SITE_URL;?>/index.php?act=vr_refund&op=get_detail&refund_id=<?php echo $output['refund']['refund_id']; ?>';
        show_msg(ajaxurl);
    });
});
</script>