<?php defined('InShopNC') or exit('Access Invalid!');?>
  <div class="ncap-form-default">
    <dl class="row">
      <dt class="tit">
        <label>提现编号</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['tradc_sn']; ?>
        <p class="notic"></p>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit">
        <label>会员名称</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['tradc_member_name']; ?>
        <p class="notic"></p>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit">
        <label>税前提现金额</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['tradc_amount']; ?>&nbsp;<?php echo $lang['currency_zh'];?>
        <p class="notic"></p>
      </dd>
    </dl>
      <dl class="row">
          <dt class="tit">
              <label>税后提现金额</label>
          </dt>
          <dd class="opt"><?php echo $output['info']['after_tradc_amount']; ?>&nbsp;<?php echo $lang['currency_zh'];?>
              <p class="notic"></p>
          </dd>
      </dl>
    <dl class="row">
      <dt class="tit">
        <label>收款银行</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['tradc_bank_name']; ?>
        <p class="notic"></p>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit">
        <label>收款账号</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['tradc_bank_no']; ?>
        <p class="notic"></p>
      </dd>
    </dl>
    <dl class="row">
      <dt class="tit">
        <label>收款人姓名</label>
      </dt>
      <dd class="opt"><?php echo $output['info']['tradc_bank_user']; ?>
        <p class="notic"></p>
      </dd>
    </dl>
    <?php if (intval($output['info']['tradc_payment_time'])) {?>
    <dl class="row">
      <dt class="tit">
        <label>操作时间</label>
      </dt>
      <dd class="opt"><?php echo @date('Y-m-d',$output['info']['tradc_payment_time']); ?> ( 支付管理员: <?php echo $output['info']['tradc_payment_admin'];?> )
        <p class="notic"></p>
      </dd>
    </dl>
    <?php } ?>
      <?php if (intval($output['info']['tradc_payment_state']==2)) {?>
          <dl class="row">
              <dt class="tit">
                  <label>提现失败原因</label>
              </dt>
              <dd class="opt">
                  <?php echo $output['info']['tradc_content'];?>

              </dd>
          </dl>
      <?php }else{ ?>
          <dl class="row">
              <dt class="tit">
                  <label>提现失败原因</label>
              </dt>
              <dd class="opt">
                  <textarea name="tradc_content" row="30" cols="100"></textarea>
                  <p class="notic">*限制150字内</p>
              </dd>
          </dl>
      <?php } ?>
    <?php if (!intval($output['info']['tradc_payment_state'])) {?>
    <div class="bot" id="submit-holder"><a class="ncap-btn-big ncap-btn-red" href="javascript:;" onclick="cash_no(<?php echo $output['info']['tradc_id']; ?>)" style="margin-right: 100px;">拒绝</a> <a class="ncap-btn-big ncap-btn-green" href="javascript:if (confirm('付款后不能取消，确认要付款吗？')){window.location.href='index.php?act=distri_cash&op=cash_pay&id=<?php echo $output['info']['tradc_id']; ?>';}else{}">付款</a></div>


    <?php } ?>


  </div>

<script>
    function cash_no(id){

        var tradc_content = $('textarea[name="tradc_content"]').val()
        if(!tradc_content){
            alert('拒绝原因必须填写');
            return false;
        }
        $.ajax({
            type: "POST",
            url: "index.php?act=distri_cash&op=cash_no",
            data: {id:id,tradc_content:tradc_content},
            dataType:'json',
            success: function(data){
                if(data.code == 200){
                    window.location.href=window.location.href;
                }else{
                    alert(data.msg);
                }
            }
        });
    }



</script>