<?php defined('InShopNC') or exit('Access Invalid!');?>

<div class="page">
  <div class="fixed-bar">
    <div class="item-title"><a class="back" href="index.php?act=goods_app&op=index" title="返回宠医云商品列表"><i class="fa fa-arrow-circle-o-left"></i></a>
      <div class="subject">
        <h3>分销员合并账号</h3>
        <h5>分销员合并</h5>
      </div>
        <?php echo $output['top_link'];?>
    </div>
  </div>
    <!-- 操作说明 -->
    <div class="explanation" id="explanation">
        <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
            <h4 title="<?php echo $lang['nc_prompts_title'];?>"><?php echo $lang['nc_prompts'];?></h4>
            <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span'];?>"></span> </div>
        <ul>
            <li>合并操作不可逆，请确认清楚后，保留帐号的准确性</li>
        </ul>
    </div>
  <form id="member_form" method="post" action='index.php?act=distri_member_merge&op=manage_save'>
    <input type="hidden" name="form_submit" value="ok" />
    <div class="ncap-form-default" id="explanation">


        <dl class="row">
            <dt class="tit">
                <label for="distribute_bill_limit">历史手机号</label>
            </dt>
            <dd class="opt">
                <input type="text" value="<?php echo $output['setting']['distribute_bill_limit'];?>" name="old_mobile" class="input-txt">
                <span class="err"></span>
                <p class="notic"></p>
            </dd>
        </dl>
        <dl class="row">
            <dt class="tit">
                <label for="distribute_bill_limit">新手机号</label>
            </dt>
            <dd class="opt">
                <input type="text" value="<?php echo $output['setting']['distribute_cash_limit'];?>" name="new_mobile" class="input-txt">
                <span class="err"></span>
                <p class="notic"></p>
            </dd>
        </dl>
      <div class="bot"><a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="submitBtn" >确认提交</a></div>
    </div>
  </form>
</div>
<script src="<?php echo RESOURCE_SITE_URL;?>/js/common_select.js" charset="utf-8"></script> 
<script src="<?php echo RESOURCE_SITE_URL;?>/js/jquery.ajaxContent.pack.js" type="text/javascript"></script> 
<script>
    $(document).ready(function(){
        $("#submitBtn").click(function(){
            $("form").submit();
        });
    });
</script> 
