<?php defined('InShopNC') or exit('Access Invalid!'); ?>

<div class="page">
    <div class="fixed-bar">
        <div class="item-title"><a class="back" href="index.php?act=pointprod&op=pointorder_list" title="返回列表"><i
                        class="fa fa-arrow-circle-o-left"></i></a>
            <div class="subject">
                <h3>批量导入实物积分兑换订单</h3>
                <h5>批量导入实物积分兑换订单，更新发货状态</h5>
            </div>
            <ul class="tab-base nc-row">
                <li><a class="current"><span>上传文件</span></a></li>
            </ul>
        </div>
        <!-- 操作说明 -->
        <div class="explanation" id="explanation">
            <div class="title" id="checkZoom"><i class="fa fa-lightbulb-o"></i>
                <h4 title="<?php echo $lang['nc_prompts_title']; ?>"><?php echo $lang['nc_prompts']; ?></h4>
                <span id="explanationZoom" title="<?php echo $lang['nc_prompts_span']; ?>">f</span></div>
            <ul>
                <li>依据服务器环境支持最大上传组件大小设置选项，如需要上传超大附件需调整服务器环境配置。</li>
                <li>导出的订单数据中填写待发货状态的<b style="color: red;">物流单号，如：820600272557604315，物流编码：如：中通，顺丰</b></li>
            </ul>
        </div>
        <form method="post" enctype="multipart/form-data" id="pointorder_form">
            <input type="hidden" name="form_submit" value="ok"/>
            <div class="ncap-form-default">
                <dl class="row">
                    <dt class="tit">
                        <label for="default_user_portrait"><?php echo $lang['default_user_pic']; ?></label>
                    </dt>
                    <dd class="opt">
                        <div class="input-file-show"><span class="show"><a class="nyroModal" rel="gal"
                                                                           href="<?php echo UPLOAD_SITE_URL . '/' . (ATTACH_COMMON . '/' . $output['list_setting']['default_user_portrait']); ?>"></a></span><span
                                    class="type-file-box">
        <input class="type-file-file" id="default_user_portrait" name="files" type="file" size="30" hidefocus="true"
               nc_type="change_default_user_portrait" title="">
        </span></div>
                        <p class="notic">上传文件为xlsx/xls/csv格式的文件。</p>
                    </dd>
                </dl>
                <div class="bot">
                    <a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green"
                       id="submitBtn"><?php echo $lang['nc_submit']; ?></a>
                </div>
            </div>
        </form>
    </div>
    <script type="text/javascript" src="<?php echo ADMIN_RESOURCE_URL; ?>/js/jquery.nyroModal.js"></script>

    <script type="text/javascript">
        //按钮先执行验证再提交表单
        $(function () {
            $("#submitBtn").click(function () {
                if ($("#pointorder_form").valid()) {
                    $("#pointorder_form").submit();
                }
            });
        });
        $(function () {
// 模拟默认用户图片上传input type='file'样式
            var textButton = "<input type='text' name='textfield' id='textfield4' class='type-file-text' /><input type='button' name='button' id='button4' value='选择上传...' class='type-file-button' />"
            $(textButton).insertBefore("#default_user_portrait");
            $("#default_user_portrait").change(function () {
                $("#textfield4").val($("#default_user_portrait").val());
            });
// 上传图片类型
            $('input[class="type-file-file"]').change(function () {
                var filepath = $(this).val();
                var extStart = filepath.lastIndexOf(".");
                var ext = filepath.substring(extStart, filepath.length).toUpperCase();
                /*if(ext!=".PNG"&&ext!=".GIF"&&ext!=".JPG"&&ext!=".JPEG"){
                    alert("");
                    $(this).attr('value','');
                    return false;
                }*/
            });
// 点击查看图片
            $('.nyroModal').nyroModal();
        });
    </script>