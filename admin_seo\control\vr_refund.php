<?php
/**
 * 在线退款
 *
 *
 *
 *
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net
 * @link       http://www.shopnc.net
 * @since      File available since Release v1.1
 */

use Shopnc\Tpl;
use Upet\Integrates\Redis\RedisManager as Redis;
use Upet\Models\Order as OrderAlias;

defined('InShopNC') or exit('Access Invalid!');
class vr_refundControl extends SystemControl{
    public function __construct(){
        parent::__construct();
    }

    public function indexOp() {
        $this->get_detailOp();
    }

    /**
     * 微信退款
     *
     */
    public function wxpayOp() {

        $result = array('state'=>'false','msg'=>'参数错误，微信退款失败');
        $refund_id = intval($_GET['refund_id']);

        $lock = Redis::lock('wxpay_vr_refund:'.$refund_id)->setAutoRelease();
        if (!$lock->get()) {
            showMessage('处理中，请勿频繁操作...');
        }
        $model_refund = Model('vr_refund');
        $condition = array();
        $condition['refund_id'] = $refund_id;
        $condition['refund_state'] = '1';
        $detail_array = $model_refund->getDetailInfo($condition);//退款详细

        if(!empty($detail_array) && in_array($detail_array['refund_code'],OrderAlias::PAY_CENTER_TYPES)) {
            $order = $model_refund->getPayDetailInfo($detail_array);//退款订单详细
            $refund_amount = $order['pay_refund_amount'];//本次在线退款总金额
            if ($refund_amount > 0) {
                //电银支付
                if((C('dianyin_pay') && $order['payment_from'] > 0) || $order['payment_from'] > 0) {
                    $use_refund_time = $order['payment_time'] + C('dianyin_allow_time');
                    if ($order['payment_code'] <> 'card' && $use_refund_time > time()) {
                        $result['msg'] = '操作太快了，5分钟后再来吧~';
                    }else{
                        $refund = $model_refund->getRefundInfo(array('refund_id'=> $refund_id));

                        $refund_data = array();
                        $refund_data['trade_no'] = $order['trade_no'];
                        $refund_data['refund_amount'] = $refund_amount;
                        $refund_data['order_type'] = $order['order_type'];
                        $refund_data['payment_code'] = $order['payment_code'];
                        $refund_data['refund_sn'] = $refund['refund_sn'];
                        
                        /**@var dianyin_payLogic $dianyinLogic*/
                        $dianyinLogic = Logic('dianyin_pay');
                        $dianyin_result =$dianyinLogic->orderRefund($refund_data);
                        $data = $dianyin_result['data'];

                        if ($dianyin_result['state'] && in_array($data['rspCode'],['1','2','0'])) {
                            $detail_array = array();
                            $detail_array['pay_amount'] = ncPriceFormat($data['refundAmt']/100);
                            $detail_array['pay_time'] = time();
                            $model_refund->editDetail(array('refund_id'=> $refund_id), $detail_array);
                            $result['state'] = 'true';
                            $result['msg'] = '已提交退款申请:'.$detail_array['pay_amount'];

                            $consume_array = array();
                            $consume_array['member_id'] = $refund['buyer_id'];
                            $consume_array['member_name'] = $refund['buyer_name'];
                            $consume_array['consume_amount'] = $detail_array['pay_amount'];
                            $consume_array['consume_time'] = time();
                            $consume_array['consume_remark'] = '在线退款成功（到账有延迟），虚拟退款单号：'.$refund['refund_sn'];
                            QueueClient::push('addConsume', $consume_array);//old end

                            $refund['admin_state_virbalance'] = 3;
                            $refund['admin_time'] = time();
                            $refund['admin_state'] = '2';
                            $refund['admin_message'] = "管理员处理在线退款";
                            //默认为退款中
                            $refund['dy_state'] = 1;

                            if($data['rspCode'] === '1'){
                                $refund['dy_state'] = 2;
                                $refund['dy_dealtime'] = time();
                            }

                            $refund['dy_refund_id'] = $data['refundId'];
                            $refund['dy_transaction_no'] = $data['transactionNo'];
                            $state = $model_refund->editOrderRefund($refund);
                            if ($state) {
                                // 发送买家消息
                                $param = array();
                                $param['code'] = 'refund_return_notice';
                                $param['member_id'] = $refund['buyer_id'];
                                $param['param'] = array(
                                    'refund_url' => urlShop('member_vr_refund', 'view', array('refund_id' => $refund['refund_id'])),
                                    'refund_sn' => $refund['refund_sn']
                                );
                                $refund['msg'] = '管理员已处理退款，请查收';//状态描述
                                $param['param']['mp_array'] = Logic('wx_api')->getTemplateData($param['code'],$refund);
                                RealTimePush('sendMemberMsg', $param);
                                $refund['mobile'] = $order['buyer_phone'];
                                //退款ERP虚拟订单
                                Logic('refund')->syncBatchVrOrderRefund($refund, $order);

                                $this->log('虚拟订单退款审核，退款编号'.$refund['refund_sn']);
                            }
                        }else{
                            if ($data['rspCode'] == "3" || $data['rspCode'] == "0" || $data['rspCode'] == "-1") {
                                $result['msg'] = '退款返回信息,'.$data['msg'];//错误描述
                            }else{
                                $result['msg'] = '退款错误,'.$dianyin_result['msg'];//错误描述
                            }
                        }
                    }
                }else{
                    $wxpay = $order['payment_config'];
                    define('WXPAY_APPID', $wxpay['appid']);
                    define('WXPAY_MCHID', $wxpay['mchid']);
                    define('WXPAY_KEY', $wxpay['key']);
                    $total_fee = $order['pay_amount']*100;//微信订单实际支付总金额(在线支付金额,单位为分)
                    $refund_fee = $refund_amount*100;//本次微信退款总金额(单位为分)
                    $api_file = BASE_PATH.DS.'api'.DS.'refund'.DS.'wxpay'.DS.'WxPay.Api.php';
                    include $api_file;
                    $input = new WxPayRefund();
                    $input->SetTransaction_id($order['trade_no']);//微信订单号
                    $input->SetTotal_fee($total_fee);
                    $input->SetRefund_fee($refund_fee);
                    $input->SetOut_refund_no($detail_array['batch_no']);//退款批次号
                    $input->SetOp_user_id(WxPayConfig::MCHID);
                    $data = WxPayApi::refund($input);
                    if(!empty($data) && $data['return_code'] == 'SUCCESS') {//请求结果
                        if($data['result_code'] == 'SUCCESS') {//业务结果
                            $detail_array = array();
                            $detail_array['pay_amount'] = ncPriceFormat($data['refund_fee']/100);
                            $detail_array['pay_time'] = time();
                            $model_refund->editDetail(array('refund_id'=> $refund_id), $detail_array);
                            $result['state'] = 'true';
                            $result['msg'] = '微信成功退款:'.$detail_array['pay_amount'];

                            $refund = $model_refund->getRefundInfo(array('refund_id'=> $refund_id));
                            $consume_array = array();
                            $consume_array['member_id'] = $refund['buyer_id'];
                            $consume_array['member_name'] = $refund['buyer_name'];
                            $consume_array['consume_amount'] = $detail_array['pay_amount'];
                            $consume_array['consume_time'] = time();
                            $consume_array['consume_remark'] = '微信在线退款成功（到账有延迟），虚拟退款单号：'.$refund['refund_sn'];
                            QueueClient::push('addConsume', $consume_array);//old end

                            $refund['admin_state_virbalance'] = 3;
                            $refund['admin_time'] = time();
                            $refund['admin_state'] = '2';
                            $refund['admin_message'] = "管理员处理微信支付在线退款";
                            $state = $model_refund->editOrderRefund($refund);
                            if ($state) {
                                // 发送买家消息
                                $param = array();
                                $param['code'] = 'refund_return_notice';
                                $param['member_id'] = $refund['buyer_id'];
                                $param['param'] = array(
                                    'refund_url' => urlShop('member_vr_refund', 'view', array('refund_id' => $refund['refund_id'])),
                                    'refund_sn' => $refund['refund_sn']
                                );
                                $refund['msg'] = '管理员已处理退款，请查收';//状态描述
                                $param['param']['mp_array'] = Logic('wx_api')->getTemplateData($param['code'],$refund);
                                RealTimePush('sendMemberMsg', $param);
                                $refund['mobile'] = $order['buyer_phone'];
                                //退款ERP虚拟订单
                                Logic('refund')->syncBatchVrOrderRefund($refund, $order);

                                $this->log('虚拟订单退款审核，退款编号'.$refund['refund_sn']);
                            }
                        } else {
                            $result['msg'] = '微信退款错误,'.$data['err_code_des'];//错误描述
                        }
                    } else {
                        $result['msg'] = '微信接口错误,'.$data['return_msg'];//返回信息
                    }
                }
            }
        }
        exit(json_encode($result));
    }
    
    /**
     * 微信退款查询
     */
    public function queryWxpayOp() {    	
    	$result = array('state'=>'false','msg'=>'参数错误，订单不存在');
    	$refund_id = intval($_GET['refund_id']);
    	$model_refund = Model('vr_refund');
    	$condition = array();
    	$condition['refund_id'] = $refund_id;
    	$detail_array = $model_refund->getDetailInfo($condition);//退款详细
    	if(!empty($detail_array) && in_array($detail_array['refund_code'],OrderAlias::PAY_CENTER_TYPES)) {
    		$order = $model_refund->getPayDetailInfo($detail_array);//退款订单详细
    		if (is_array($order) && !empty($order)) {
                if((C('dianyin_pay') && $order['payment_from'] > 0) || $order['payment_from'] > 0) {
                    $refund = $model_refund->getRefundInfo($condition,"refund_amount,dy_refund_id,dy_transaction_no");
                    $refund_data = array();
                    $refund_data['dy_transaction_no'] = $refund['dy_transaction_no'];
                    $refund_data['dy_refund_id'] = $refund['dy_refund_id'];
                    $refund_data['refund_amount'] = $refund['refund_amount'];
                    $refund_data['payment_code'] = $order['payment_code'];
                    $refund_data['trade_no'] = $order['trade_no'];
                    $dianyin_result = Logic('dianyin_pay')->queryOrder($refund_data);
                    if ($dianyin_result['state']) {
                        $data = $dianyin_result['data'];
                        $pay_amount = ncPriceFormat($data['refundAmt'] / 100);
                        $result['state'] = 'true';
                        $result['msg'] = '查询成功退款:' . $pay_amount;
                    }else{
                        $result['msg'] = '查询退款:' . $dianyin_result['msg'];
                    }
                }else{
                    $wxpay = $order['payment_config'];
                    define('WXPAY_APPID', $wxpay['appid']);
                    define('WXPAY_MCHID', $wxpay['mchid']);
                    define('WXPAY_KEY', $wxpay['key']);
                    $api_file = BASE_PATH.DS.'api'.DS.'refund'.DS.'wxpay'.DS.'WxPay.Api.php';
                    include $api_file;
                    $input = new WxPayRefund();
                    $input->SetTransaction_id($order['trade_no']);//微信订单号
                    $data = WxPayApi::refundQuery($input);
                    if(!empty($data) && $data['return_code'] == 'SUCCESS') {//请求结果
                        if ($data['result_code'] == 'SUCCESS') {//业务结果
                            $pay_amount = ncPriceFormat($data['refund_fee'] / 100);
                            $result['state'] = 'true';
                            $result['msg'] = '查询微信成功退款:' . $pay_amount;
                        }
                    }
                }
    		}
    	}
    	exit(json_encode($result));
    }

    /**
     * 支付宝退款
     *
     */
    public function alipayOp() {
        $refund_id = intval($_GET['refund_id']);
        $lock = Redis::lock('alipay_vr_refund:'.$refund_id)->setAutoRelease();
        if (!$lock->get()) {
            showMessage('处理中，请勿频繁操作...');
        }
        $model_refund = Model('vr_refund');
        $condition = array();
        $condition['refund_id'] = $refund_id;
        $condition['refund_state'] = '1';
        $detail_array = $model_refund->getDetailInfo($condition);//退款详细
        if(!empty($detail_array) && $detail_array['refund_code'] == 'alipay') {
            $order = $model_refund->getPayDetailInfo($detail_array);//退款订单详细
            $refund_amount = $order['pay_refund_amount'];//本次在线退款总金额
            if ($refund_amount > 0) {
                $payment_config = $order['payment_config'];
                $alipay_config = array();
                $alipay_config['seller_email'] = $payment_config['alipay_account'];
                $alipay_config['partner'] = $payment_config['alipay_partner'];
                $alipay_config['key'] = $payment_config['alipay_key'];
                $api_file = BASE_PATH.DS.'api'.DS.'refund'.DS.'alipay'.DS.'alipay.class.php';
                include $api_file;
                $alipaySubmit = new AlipaySubmit($alipay_config);
                $parameter = getPara($alipay_config);
                $batch_no = $detail_array['batch_no'];
                $b_date = substr($batch_no,0,8);
                if($b_date != date('Ymd')) {
                    $batch_no = date('Ymd').substr($batch_no, 8);//批次号。支付宝要求格式为：当天退款日期+流水号。
                    $model_refund->editDetail(array('refund_id'=> $refund_id), array('batch_no'=> $batch_no));
                }
                $parameter['notify_url'] = ADMIN_SITE_URL."/api/refund/alipay/vr_notify_url.php";
                $parameter['batch_no'] = $batch_no;
                $parameter['detail_data'] = $order['trade_no'].'^'.$refund_amount.'^协商退款';//数据格式为：原交易号^退款金额^理由
                $pay_url = $alipaySubmit->buildRequestParaToString($parameter);
                @header("Location: ".$pay_url);
            }
        }
    }

    /**
     * 在线退款查询
     *
     */
    public function get_detailOp() {
        $result = array('state'=>'false','msg'=>'退款正在处理中或已失败，稍后查询');
        $refund_id = intval($_GET['refund_id']);
        $model_refund = Model('vr_refund');
        $condition = array();
        $condition['refund_id'] = $refund_id;
        $detail_array = $model_refund->getDetailInfo($condition);//退款详细
        if($detail_array['pay_time'] > 0) {
            $result = array('state'=>'true','msg'=>'成功退款:'.ncPriceFormat($detail_array['pay_amount']));
        }
        exit(json_encode($result));
    }
}
