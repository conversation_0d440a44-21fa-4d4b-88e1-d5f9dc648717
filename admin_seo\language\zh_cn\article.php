<?php
defined('InShopNC') or exit('Access Invalid!');
/**
 * index
 */
$lang['article_index_del_succ']	= '删除文章成功。';
$lang['article_index_choose']	= '请选择要删除的内容!';
$lang['article_index_manage']	= '文章管理';
$lang['article_index_manage_subhead']= '网站系统文章索引与管理';
$lang['article_index_title']	= '标题';
$lang['article_index_class']	= '文章分类';
$lang['article_index_show']		= '显示';
$lang['article_index_addtime']	= '添加时间';
$lang['article_index_help1']	= '区别于会员协议，可在文章列表页点击查看';
/**
 * 添加文章
 */
$lang['article_add_url']		= '链接';
$lang['article_add_url_tip']			= '当填写&quot;链接&quot;后点击文章标题将直接跳转至链接地址，不显示文章内容。链接格式请以http://开头';
$lang['article_add_show']		= '显示';
$lang['article_add_class']		= '所属分类';
$lang['article_add_content']	= '文章内容';
$lang['article_add_upload']		= '图片上传';
$lang['article_add_batch_upload']	= '批量上传';
$lang['article_add_normal_upload']	= '普通上传';
$lang['article_add_uploaded']		= '已传图片';
$lang['article_add_insert']			= '插入';
$lang['article_add_title_null']		= '文章标题不能为空';
$lang['article_add_class_null']		= '文章分类不能为空';
$lang['article_add_url_wrong']		= '链接格式不正确';
$lang['article_add_content_null']	= '文章内容不能为空';
$lang['article_add_sort_int']		= '文章排序仅能为数字';
$lang['article_add_del_fail']		= '删除失败';
$lang['article_add_img_wrong']      = '图片限于png,gif,jpeg,jpg格式';
$lang['article_add_tolist']      = '返回文章列表';
$lang['article_add_continueadd']      = '继续新增文章';
$lang['article_add_ok']      = '新增文章成功。';
$lang['article_add_fail']      = '新增文章失败。';
/**
 * 文章编辑
 */
$lang['article_edit_back_to_list']	= '返回文章列表';
$lang['article_edit_edit_again']	= '重新编辑该文章';
$lang['article_edit_succ']			= '编辑文章成功';
$lang['article_edit_fail']			= '编辑文章失败';
/**
 * iframe上传
 */
$lang['article_iframe_upload']		= '上传';
$lang['article_iframe_uploadfail']		= '上传失败';

